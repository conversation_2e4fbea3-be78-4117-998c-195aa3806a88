import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const preferenceSchema = z.object({
  category: z.enum(['APPEARANCE', 'NOTIFICATION', 'DASHBOARD', 'WORKFLOW', 'PRIVACY']),
  preferences: z.record(z.any())
})

const dashboardLayoutSchema = z.object({
  widgets: z.array(z.object({
    id: z.string(),
    type: z.string(),
    position: z.object({
      x: z.number(),
      y: z.number(),
      width: z.number(),
      height: z.number()
    }),
    config: z.record(z.any()).optional()
  })),
  layout: z.enum(['grid', 'list', 'compact']).default('grid')
})

// GET /api/settings/preferences - Get user preferences
export const GET = withPermission('USER', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { searchParams } = new URL(request.url)
  const category = searchParams.get('category')

  try {
    const where: any = {
      userId: user.id,
      storeId
    }

    if (category) {
      where.category = category
    }

    const preferences = await prisma.userPreference.findMany({
      where,
      orderBy: { category: 'asc' }
    })

    // Group preferences by category
    const groupedPreferences = preferences.reduce((acc: any, pref) => {
      acc[pref.category] = {
        ...acc[pref.category],
        ...pref.preferences
      }
      return acc
    }, {})

    // Add default preferences for missing categories
    const defaultPreferences = getDefaultUserPreferences()
    Object.keys(defaultPreferences).forEach(cat => {
      if (!groupedPreferences[cat]) {
        groupedPreferences[cat] = defaultPreferences[cat]
      } else {
        // Merge with defaults for missing keys
        groupedPreferences[cat] = {
          ...defaultPreferences[cat],
          ...groupedPreferences[cat]
        }
      }
    })

    return createSuccessResponse({
      preferences: groupedPreferences,
      user: {
        id: user.id,
        name: user.name,
        role: user.role
      }
    })

  } catch (error) {
    console.error('Error fetching user preferences:', error)
    return createErrorResponse('Failed to fetch preferences', 500)
  }
})

// POST /api/settings/preferences - Update user preferences
export const POST = withPermission('USER', 'UPDATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const { category, preferences } = preferenceSchema.parse(body)

  try {
    // Validate preferences based on category
    const validationResult = validatePreferences(category, preferences)
    if (!validationResult.isValid) {
      return createErrorResponse(`Invalid preferences: ${validationResult.error}`, 400)
    }

    // Update or create user preferences
    const userPreference = await prisma.userPreference.upsert({
      where: {
        userId_storeId_category: {
          userId: user.id,
          storeId,
          category
        }
      },
      update: {
        preferences,
        updatedAt: new Date()
      },
      create: {
        userId: user.id,
        storeId,
        category,
        preferences
      }
    })

    return createSuccessResponse({
      category: userPreference.category,
      preferences: userPreference.preferences
    }, 'Preferences updated successfully')

  } catch (error) {
    console.error('Error updating user preferences:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to update preferences',
      400
    )
  }
})

// PUT /api/settings/preferences - Bulk update preferences
export const PUT = withPermission('USER', 'UPDATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()

  const bulkUpdateSchema = z.object({
    preferences: z.record(z.record(z.any()))
  })

  const { preferences } = bulkUpdateSchema.parse(body)

  try {
    const results = []
    const errors = []

    for (const [category, prefs] of Object.entries(preferences)) {
      try {
        // Validate category
        if (!['APPEARANCE', 'NOTIFICATION', 'DASHBOARD', 'WORKFLOW', 'PRIVACY'].includes(category)) {
          errors.push(`Invalid category: ${category}`)
          continue
        }

        // Validate preferences
        const validationResult = validatePreferences(category, prefs)
        if (!validationResult.isValid) {
          errors.push(`Invalid preferences for ${category}: ${validationResult.error}`)
          continue
        }

        // Update preferences
        const userPreference = await prisma.userPreference.upsert({
          where: {
            userId_storeId_category: {
              userId: user.id,
              storeId,
              category
            }
          },
          update: {
            preferences: prefs,
            updatedAt: new Date()
          },
          create: {
            userId: user.id,
            storeId,
            category,
            preferences: prefs
          }
        })

        results.push({
          category: userPreference.category,
          preferences: userPreference.preferences
        })

      } catch (error) {
        errors.push(`Error updating ${category}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    return createSuccessResponse({
      updated: results,
      errors,
      summary: {
        total: Object.keys(preferences).length,
        successful: results.length,
        failed: errors.length
      }
    }, `Bulk update completed: ${results.length} successful, ${errors.length} failed`)

  } catch (error) {
    console.error('Error bulk updating preferences:', error)
    return createErrorResponse('Failed to bulk update preferences', 500)
  }
})

// DELETE /api/settings/preferences - Reset preferences to default
export const DELETE = withPermission('USER', 'UPDATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { searchParams } = new URL(request.url)
  const category = searchParams.get('category')

  try {
    if (category) {
      // Reset specific category
      await prisma.userPreference.deleteMany({
        where: {
          userId: user.id,
          storeId,
          category
        }
      })
    } else {
      // Reset all preferences
      await prisma.userPreference.deleteMany({
        where: {
          userId: user.id,
          storeId
        }
      })
    }

    return createSuccessResponse({}, 
      category ? `${category} preferences reset to default` : 'All preferences reset to default'
    )

  } catch (error) {
    console.error('Error resetting preferences:', error)
    return createErrorResponse('Failed to reset preferences', 500)
  }
})

// Helper functions

function validatePreferences(category: string, preferences: any): { isValid: boolean; error?: string } {
  try {
    switch (category) {
      case 'APPEARANCE':
        return validateAppearancePreferences(preferences)
      case 'NOTIFICATION':
        return validateNotificationPreferences(preferences)
      case 'DASHBOARD':
        return validateDashboardPreferences(preferences)
      case 'WORKFLOW':
        return validateWorkflowPreferences(preferences)
      case 'PRIVACY':
        return validatePrivacyPreferences(preferences)
      default:
        return { isValid: false, error: 'Invalid category' }
    }
  } catch (error) {
    return { isValid: false, error: error instanceof Error ? error.message : 'Validation error' }
  }
}

function validateAppearancePreferences(prefs: any): { isValid: boolean; error?: string } {
  const schema = z.object({
    theme: z.enum(['light', 'dark', 'auto']).optional(),
    primaryColor: z.string().regex(/^#[0-9A-F]{6}$/i).optional(),
    fontSize: z.enum(['small', 'medium', 'large']).optional(),
    sidebarCollapsed: z.boolean().optional(),
    compactMode: z.boolean().optional(),
    animations: z.boolean().optional()
  })

  try {
    schema.parse(prefs)
    return { isValid: true }
  } catch (error) {
    return { isValid: false, error: 'Invalid appearance preferences' }
  }
}

function validateNotificationPreferences(prefs: any): { isValid: boolean; error?: string } {
  const schema = z.object({
    email: z.object({
      enabled: z.boolean(),
      lowStock: z.boolean().optional(),
      newOrders: z.boolean().optional(),
      systemAlerts: z.boolean().optional(),
      reports: z.boolean().optional()
    }).optional(),
    push: z.object({
      enabled: z.boolean(),
      lowStock: z.boolean().optional(),
      newOrders: z.boolean().optional(),
      systemAlerts: z.boolean().optional()
    }).optional(),
    inApp: z.object({
      enabled: z.boolean(),
      sound: z.boolean().optional(),
      desktop: z.boolean().optional()
    }).optional(),
    frequency: z.enum(['immediate', 'hourly', 'daily']).optional()
  })

  try {
    schema.parse(prefs)
    return { isValid: true }
  } catch (error) {
    return { isValid: false, error: 'Invalid notification preferences' }
  }
}

function validateDashboardPreferences(prefs: any): { isValid: boolean; error?: string } {
  try {
    if (prefs.layout) {
      dashboardLayoutSchema.parse(prefs.layout)
    }

    const schema = z.object({
      layout: dashboardLayoutSchema.optional(),
      defaultView: z.enum(['overview', 'sales', 'inventory', 'customers']).optional(),
      refreshInterval: z.number().min(30).max(3600).optional(), // 30 seconds to 1 hour
      showWelcome: z.boolean().optional(),
      quickActions: z.array(z.string()).optional()
    })

    schema.parse(prefs)
    return { isValid: true }
  } catch (error) {
    return { isValid: false, error: 'Invalid dashboard preferences' }
  }
}

function validateWorkflowPreferences(prefs: any): { isValid: boolean; error?: string } {
  const schema = z.object({
    autoSave: z.boolean().optional(),
    confirmActions: z.boolean().optional(),
    defaultPageSize: z.number().min(10).max(100).optional(),
    shortcuts: z.record(z.string()).optional(),
    defaultFilters: z.record(z.any()).optional(),
    quickSearch: z.boolean().optional()
  })

  try {
    schema.parse(prefs)
    return { isValid: true }
  } catch (error) {
    return { isValid: false, error: 'Invalid workflow preferences' }
  }
}

function validatePrivacyPreferences(prefs: any): { isValid: boolean; error?: string } {
  const schema = z.object({
    shareAnalytics: z.boolean().optional(),
    trackActivity: z.boolean().optional(),
    showOnlineStatus: z.boolean().optional(),
    allowDataExport: z.boolean().optional(),
    sessionTimeout: z.number().min(300).max(86400).optional() // 5 minutes to 24 hours
  })

  try {
    schema.parse(prefs)
    return { isValid: true }
  } catch (error) {
    return { isValid: false, error: 'Invalid privacy preferences' }
  }
}

function getDefaultUserPreferences() {
  return {
    APPEARANCE: {
      theme: 'light',
      primaryColor: '#3b82f6',
      fontSize: 'medium',
      sidebarCollapsed: false,
      compactMode: false,
      animations: true
    },
    NOTIFICATION: {
      email: {
        enabled: true,
        lowStock: true,
        newOrders: true,
        systemAlerts: true,
        reports: false
      },
      push: {
        enabled: true,
        lowStock: true,
        newOrders: true,
        systemAlerts: false
      },
      inApp: {
        enabled: true,
        sound: true,
        desktop: true
      },
      frequency: 'immediate'
    },
    DASHBOARD: {
      defaultView: 'overview',
      refreshInterval: 300, // 5 minutes
      showWelcome: true,
      quickActions: ['new-sale', 'add-product', 'view-reports'],
      layout: {
        widgets: [
          {
            id: 'sales-overview',
            type: 'sales-chart',
            position: { x: 0, y: 0, width: 6, height: 4 }
          },
          {
            id: 'recent-orders',
            type: 'orders-list',
            position: { x: 6, y: 0, width: 6, height: 4 }
          },
          {
            id: 'inventory-alerts',
            type: 'alerts',
            position: { x: 0, y: 4, width: 4, height: 3 }
          },
          {
            id: 'top-products',
            type: 'products-chart',
            position: { x: 4, y: 4, width: 8, height: 3 }
          }
        ],
        layout: 'grid'
      }
    },
    WORKFLOW: {
      autoSave: true,
      confirmActions: true,
      defaultPageSize: 25,
      shortcuts: {
        'new-sale': 'ctrl+n',
        'search': 'ctrl+k',
        'save': 'ctrl+s'
      },
      defaultFilters: {},
      quickSearch: true
    },
    PRIVACY: {
      shareAnalytics: false,
      trackActivity: true,
      showOnlineStatus: true,
      allowDataExport: true,
      sessionTimeout: 3600 // 1 hour
    }
  }
}
