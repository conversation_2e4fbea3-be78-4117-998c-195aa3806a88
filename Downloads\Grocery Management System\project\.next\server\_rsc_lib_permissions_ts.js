"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_lib_permissions_ts";
exports.ids = ["_rsc_lib_permissions_ts"];
exports.modules = {

/***/ "(rsc)/./lib/permissions.ts":
/*!****************************!*\
  !*** ./lib/permissions.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ROLE_PERMISSIONS: () => (/* binding */ ROLE_PERMISSIONS),\n/* harmony export */   getModulePermissions: () => (/* binding */ getModulePermissions),\n/* harmony export */   getUserModules: () => (/* binding */ getUserModules),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission)\n/* harmony export */ });\nconst ROLE_PERMISSIONS = [\n    // FOUNDER - Full access\n    {\n        role: \"FOUNDER\",\n        module: \"USER\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"ASSIGN\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"ROLE\",\n        permissions: [\n            \"MANAGE\",\n            \"ACCESS_SETTINGS\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"STORE\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"SETTINGS\",\n        permissions: [\n            \"ACCESS_SETTINGS\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"REPORTS\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\",\n            \"PRINT\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"SUBSCRIPTION\",\n        permissions: [\n            \"READ\",\n            \"UPDATE\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"AUDIT_LOG\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"DASHBOARD\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    // SUPER_ADMIN\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"USER\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"ASSIGN\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"STORE\",\n        permissions: [\n            \"READ\",\n            \"UPDATE\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"SETTINGS\",\n        permissions: [\n            \"ACCESS_SETTINGS\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"REPORTS\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\",\n            \"PRINT\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"AUDIT_LOG\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"SUPPORT\",\n        permissions: [\n            \"READ\",\n            \"CREATE\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"DASHBOARD\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    // ADMIN\n    {\n        role: \"ADMIN\",\n        module: \"PRODUCT\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"IMPORT\",\n            \"EXPORT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"CATEGORY\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"INVENTORY\",\n        permissions: [\n            \"READ\",\n            \"UPDATE\",\n            \"EXPORT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"PURCHASE\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"APPROVE\",\n            \"REJECT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"PURCHASE_RETURN\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"SALES\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"EXPORT\",\n            \"PRINT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"SALES_RETURN\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"CUSTOMER\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"SUPPLIER\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"DISTRIBUTOR\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"EXPENSE\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"PAYMENT\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"B2B\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"APPROVE\",\n            \"REJECT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"BILLING\",\n        permissions: [\n            \"READ\",\n            \"PRINT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"TAX\",\n        permissions: [\n            \"READ\",\n            \"UPDATE\",\n            \"ACCESS_SETTINGS\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"NOTIFICATION\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"DOCUMENT\",\n        permissions: [\n            \"UPLOAD\",\n            \"READ\",\n            \"DOWNLOAD\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"REPORTS\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\",\n            \"PRINT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"DASHBOARD\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"FEEDBACK\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"SUPPORT\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    // STAFF\n    {\n        role: \"STAFF\",\n        module: \"SALES\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"SALES_RETURN\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"INVENTORY\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"BILLING\",\n        permissions: [\n            \"READ\",\n            \"PRINT\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"DOCUMENT\",\n        permissions: [\n            \"UPLOAD\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"CUSTOMER\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"DASHBOARD\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    // DISTRIBUTOR\n    {\n        role: \"DISTRIBUTOR\",\n        module: \"PURCHASE\",\n        permissions: [\n            \"READ\",\n            \"APPROVE\",\n            \"REJECT\"\n        ]\n    },\n    {\n        role: \"DISTRIBUTOR\",\n        module: \"PURCHASE_RETURN\",\n        permissions: [\n            \"READ\",\n            \"APPROVE\"\n        ]\n    },\n    {\n        role: \"DISTRIBUTOR\",\n        module: \"DASHBOARD\",\n        permissions: [\n            \"READ\"\n        ]\n    }\n];\nfunction hasPermission(userRole, module, permission) {\n    const rolePermissions = ROLE_PERMISSIONS.filter((rp)=>rp.role === userRole && rp.module === module);\n    return rolePermissions.some((rp)=>rp.permissions.includes(permission));\n}\nfunction getUserModules(userRole) {\n    return ROLE_PERMISSIONS.filter((rp)=>rp.role === userRole).map((rp)=>rp.module);\n}\nfunction getModulePermissions(userRole, module) {\n    const rolePermission = ROLE_PERMISSIONS.find((rp)=>rp.role === userRole && rp.module === module);\n    return rolePermission?.permissions || [];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/permissions.ts\n");

/***/ })

};
;