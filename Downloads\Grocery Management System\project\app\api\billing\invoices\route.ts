import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  getPaginationParams,
  buildSearchFilter,
  createPaginatedResponse,
  createAuditLog,
  validateStoreAccess,
  buildDateRangeFilter
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const invoiceSchema = z.object({
  customerId: z.string().min(1, 'Customer is required'),
  saleId: z.string().optional(),
  invoiceType: z.enum(['SALE', 'PROFORMA', 'CREDIT_NOTE', 'DEBIT_NOTE']).default('SALE'),
  dueDate: z.string().optional(),
  items: z.array(z.object({
    productId: z.string().min(1, 'Product is required'),
    variantId: z.string().optional(),
    description: z.string().optional(),
    quantity: z.number().min(1, 'Quantity must be at least 1'),
    unitPrice: z.number().min(0, 'Unit price must be positive'),
    discount: z.number().min(0).default(0),
    discountType: z.enum(['AMOUNT', 'PERCENTAGE']).default('PERCENTAGE'),
    taxRate: z.number().min(0).max(100).default(0),
    hsnCode: z.string().optional(),
    cessRate: z.number().min(0).max(100).default(0)
  })).min(1, 'At least one item is required'),
  discount: z.number().min(0).default(0),
  discountType: z.enum(['AMOUNT', 'PERCENTAGE']).default('AMOUNT'),
  shippingCharges: z.number().min(0).default(0),
  packingCharges: z.number().min(0).default(0),
  otherCharges: z.number().min(0).default(0),
  roundOff: z.number().default(0),
  notes: z.string().optional(),
  termsAndConditions: z.string().optional(),
  placeOfSupply: z.string().optional(),
  reverseCharge: z.boolean().default(false)
})

// GET /api/billing/invoices - Get all invoices with advanced filtering
export const GET = withPermission('BILLING', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { page, limit, skip, search, sortBy, sortOrder } = getPaginationParams(request)
  const { searchParams } = new URL(request.url)
  
  const status = searchParams.get('status')
  const invoiceType = searchParams.get('invoiceType')
  const customerId = searchParams.get('customerId')
  const startDate = searchParams.get('startDate')
  const endDate = searchParams.get('endDate')
  const minAmount = searchParams.get('minAmount')
  const maxAmount = searchParams.get('maxAmount')
  const overdue = searchParams.get('overdue') === 'true'

  // Build filters
  const where: any = {
    storeId,
    ...buildSearchFilter(search, ['invoiceNo']),
    ...buildDateRangeFilter(startDate || undefined, endDate || undefined, 'createdAt')
  }

  if (status) {
    where.status = status
  }

  if (invoiceType) {
    where.invoiceType = invoiceType
  }

  if (customerId) {
    where.customerId = customerId
  }

  if (minAmount || maxAmount) {
    where.totalAmount = {}
    if (minAmount) where.totalAmount.gte = parseFloat(minAmount)
    if (maxAmount) where.totalAmount.lte = parseFloat(maxAmount)
  }

  if (overdue) {
    where.dueDate = { lt: new Date() }
    where.status = { in: ['SENT', 'PARTIAL'] }
  }

  // Get total count
  const total = await prisma.invoice.count({ where })

  // Get invoices with pagination
  const invoices = await prisma.invoice.findMany({
    where,
    include: {
      customer: {
        select: {
          id: true,
          name: true,
          phone: true,
          email: true,
          gstNumber: true
        }
      },
      items: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
              unit: true,
              hsnCode: true
            }
          }
        }
      },
      createdBy: {
        select: {
          id: true,
          name: true
        }
      },
      _count: {
        select: {
          items: true,
          payments: true
        }
      }
    },
    orderBy: { [sortBy]: sortOrder },
    skip,
    take: limit
  })

  // Add calculated fields
  const invoicesWithCalculations = invoices.map(invoice => ({
    ...invoice,
    itemsCount: invoice._count.items,
    paymentsCount: invoice._count.payments,
    balanceAmount: invoice.totalAmount - invoice.paidAmount,
    paymentStatus: invoice.paidAmount === 0 ? 'unpaid' : 
                   invoice.paidAmount >= invoice.totalAmount ? 'paid' : 'partial',
    isOverdue: invoice.dueDate && new Date(invoice.dueDate) < new Date() && 
               invoice.status !== 'PAID',
    daysPastDue: invoice.dueDate ? 
                 Math.max(0, Math.ceil((Date.now() - new Date(invoice.dueDate).getTime()) / (1000 * 60 * 60 * 24))) : 0
  }))

  return createPaginatedResponse(invoicesWithCalculations, page, limit, total)
})

// POST /api/billing/invoices - Create new invoice
export const POST = withPermission('BILLING', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const data = invoiceSchema.parse(body)

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Validate customer exists
      const customer = await tx.customer.findFirst({
        where: {
          id: data.customerId,
          storeId,
          isActive: true
        }
      })

      if (!customer) {
        throw new Error('Customer not found')
      }

      // Validate products
      const productIds = data.items.map(item => item.productId)
      const products = await tx.product.findMany({
        where: {
          id: { in: productIds },
          storeId,
          isActive: true
        }
      })

      if (products.length !== productIds.length) {
        throw new Error('One or more products not found')
      }

      // Generate invoice number
      const invoiceCount = await tx.invoice.count({ where: { storeId } })
      const invoiceNo = `INV-${String(invoiceCount + 1).padStart(6, '0')}`

      // Calculate tax breakdown and totals
      const taxCalculation = calculateTaxes(data.items, customer.gstNumber, data.placeOfSupply)
      
      let subtotal = 0
      let totalItemDiscount = 0

      const invoiceItems = data.items.map(item => {
        const product = products.find(p => p.id === item.productId)!
        const itemSubtotal = item.quantity * item.unitPrice
        
        let itemDiscount = 0
        if (item.discountType === 'PERCENTAGE') {
          itemDiscount = (itemSubtotal * item.discount) / 100
        } else {
          itemDiscount = item.discount
        }

        const taxableAmount = itemSubtotal - itemDiscount
        const itemTaxes = calculateItemTax(taxableAmount, item.taxRate, item.cessRate, customer.gstNumber)

        subtotal += itemSubtotal
        totalItemDiscount += itemDiscount

        return {
          productId: item.productId,
          variantId: item.variantId,
          description: item.description || product.name,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          discount: itemDiscount,
          taxableAmount,
          cgstRate: itemTaxes.cgstRate,
          cgstAmount: itemTaxes.cgstAmount,
          sgstRate: itemTaxes.sgstRate,
          sgstAmount: itemTaxes.sgstAmount,
          igstRate: itemTaxes.igstRate,
          igstAmount: itemTaxes.igstAmount,
          cessRate: item.cessRate,
          cessAmount: itemTaxes.cessAmount,
          totalAmount: taxableAmount + itemTaxes.totalTax,
          hsnCode: item.hsnCode || product.hsnCode
        }
      })

      // Apply overall discount
      let overallDiscount = data.discount || 0
      if (data.discountType === 'PERCENTAGE') {
        overallDiscount = (subtotal * overallDiscount) / 100
      }

      const totalDiscount = totalItemDiscount + overallDiscount
      const taxableValue = subtotal - totalDiscount
      const totalTax = taxCalculation.totalTax
      const grossAmount = taxableValue + totalTax + data.shippingCharges + data.packingCharges + data.otherCharges
      const finalAmount = grossAmount + data.roundOff

      // Create invoice
      const invoice = await tx.invoice.create({
        data: {
          invoiceNo,
          invoiceType: data.invoiceType,
          customerId: data.customerId,
          saleId: data.saleId,
          invoiceDate: new Date(),
          dueDate: data.dueDate ? new Date(data.dueDate) : undefined,
          subtotal,
          discount: totalDiscount,
          taxableAmount: taxableValue,
          cgstAmount: taxCalculation.cgstAmount,
          sgstAmount: taxCalculation.sgstAmount,
          igstAmount: taxCalculation.igstAmount,
          cessAmount: taxCalculation.cessAmount,
          totalTax,
          shippingCharges: data.shippingCharges,
          packingCharges: data.packingCharges,
          otherCharges: data.otherCharges,
          roundOff: data.roundOff,
          totalAmount: finalAmount,
          status: 'DRAFT',
          paymentStatus: 'UNPAID',
          placeOfSupply: data.placeOfSupply,
          reverseCharge: data.reverseCharge,
          notes: data.notes,
          termsAndConditions: data.termsAndConditions,
          createdById: user.id,
          storeId,
          items: {
            create: invoiceItems
          }
        },
        include: {
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                  unit: true
                }
              }
            }
          },
          customer: {
            select: {
              id: true,
              name: true,
              phone: true,
              email: true,
              gstNumber: true
            }
          }
        }
      })

      return invoice
    })

    // Create audit log
    await createAuditLog(
      'CREATE',
      'INVOICE',
      `Created invoice: ${result.invoiceNo} - Total: ₹${result.totalAmount} - Customer: ${result.customer.name}`,
      user.id,
      storeId
    )

    return createSuccessResponse(result, 'Invoice created successfully')

  } catch (error) {
    console.error('Error creating invoice:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to create invoice',
      400
    )
  }
})

// Helper functions for tax calculations

function calculateTaxes(items: any[], customerGST?: string, placeOfSupply?: string) {
  let cgstAmount = 0
  let sgstAmount = 0
  let igstAmount = 0
  let cessAmount = 0

  items.forEach(item => {
    const taxableAmount = item.quantity * item.unitPrice - (item.discount || 0)
    const itemTaxes = calculateItemTax(taxableAmount, item.taxRate, item.cessRate, customerGST)
    
    cgstAmount += itemTaxes.cgstAmount
    sgstAmount += itemTaxes.sgstAmount
    igstAmount += itemTaxes.igstAmount
    cessAmount += itemTaxes.cessAmount
  })

  return {
    cgstAmount,
    sgstAmount,
    igstAmount,
    cessAmount,
    totalTax: cgstAmount + sgstAmount + igstAmount + cessAmount
  }
}

function calculateItemTax(taxableAmount: number, taxRate: number, cessRate: number, customerGST?: string) {
  const isInterState = false // This would be determined by comparing store state with customer state
  
  let cgstRate = 0
  let sgstRate = 0
  let igstRate = 0
  let cgstAmount = 0
  let sgstAmount = 0
  let igstAmount = 0
  let cessAmount = 0

  if (taxRate > 0) {
    if (isInterState || !customerGST) {
      // Inter-state or B2C - IGST
      igstRate = taxRate
      igstAmount = (taxableAmount * taxRate) / 100
    } else {
      // Intra-state - CGST + SGST
      cgstRate = taxRate / 2
      sgstRate = taxRate / 2
      cgstAmount = (taxableAmount * cgstRate) / 100
      sgstAmount = (taxableAmount * sgstRate) / 100
    }
  }

  if (cessRate > 0) {
    cessAmount = (taxableAmount * cessRate) / 100
  }

  return {
    cgstRate,
    cgstAmount,
    sgstRate,
    sgstAmount,
    igstRate,
    igstAmount,
    cessAmount,
    totalTax: cgstAmount + sgstAmount + igstAmount + cessAmount
  }
}
