import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  getPaginationParams,
  buildSearchFilter,
  createPaginatedResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const pricingTierSchema = z.object({
  name: z.string().min(1, 'Tier name is required'),
  description: z.string().optional(),
  tierType: z.enum(['VOLUME', 'CUSTOMER', 'PRODUCT']),
  conditions: z.object({
    minQuantity: z.number().min(0).optional(),
    maxQuantity: z.number().min(0).optional(),
    minOrderValue: z.number().min(0).optional(),
    customerTier: z.enum(['BRONZE', 'SILVER', 'GOLD', 'PLATINUM']).optional(),
    productCategories: z.array(z.string()).optional(),
    validFrom: z.string().optional(),
    validTo: z.string().optional()
  }),
  discountRules: z.array(z.object({
    productId: z.string().optional(),
    categoryId: z.string().optional(),
    discountType: z.enum(['PERCENTAGE', 'AMOUNT', 'FIXED_PRICE']),
    discountValue: z.number().min(0),
    maxDiscount: z.number().min(0).optional()
  })),
  isActive: z.boolean().default(true)
})

// GET /api/b2b/pricing - Get all pricing tiers
export const GET = withPermission('B2B', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { page, limit, skip, search, sortBy, sortOrder } = getPaginationParams(request)
  const { searchParams } = new URL(request.url)
  
  const tierType = searchParams.get('tierType')
  const isActive = searchParams.get('isActive')

  // Build filters
  const where: any = {
    storeId,
    ...buildSearchFilter(search, ['name', 'description'])
  }

  if (tierType) {
    where.tierType = tierType
  }

  if (isActive !== null) {
    where.isActive = isActive === 'true'
  }

  // Get total count
  const total = await prisma.pricingTier.count({ where })

  // Get pricing tiers with pagination
  const pricingTiers = await prisma.pricingTier.findMany({
    where,
    include: {
      discountRules: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true
            }
          },
          category: {
            select: {
              id: true,
              name: true
            }
          }
        }
      },
      createdBy: {
        select: {
          id: true,
          name: true
        }
      }
    },
    orderBy: { [sortBy]: sortOrder },
    skip,
    take: limit
  })

  return createPaginatedResponse(pricingTiers, page, limit, total)
})

// POST /api/b2b/pricing - Create new pricing tier
export const POST = withPermission('B2B', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const data = pricingTierSchema.parse(body)

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Validate products and categories in discount rules
      for (const rule of data.discountRules) {
        if (rule.productId) {
          const product = await tx.product.findFirst({
            where: { id: rule.productId, storeId, isActive: true }
          })
          if (!product) {
            throw new Error(`Product not found: ${rule.productId}`)
          }
        }

        if (rule.categoryId) {
          const category = await tx.category.findFirst({
            where: { id: rule.categoryId, storeId, isActive: true }
          })
          if (!category) {
            throw new Error(`Category not found: ${rule.categoryId}`)
          }
        }
      }

      // Create pricing tier
      const pricingTier = await tx.pricingTier.create({
        data: {
          name: data.name,
          description: data.description,
          tierType: data.tierType,
          conditions: data.conditions,
          isActive: data.isActive,
          createdById: user.id,
          storeId
        }
      })

      // Create discount rules
      if (data.discountRules.length > 0) {
        await tx.discountRule.createMany({
          data: data.discountRules.map(rule => ({
            pricingTierId: pricingTier.id,
            productId: rule.productId,
            categoryId: rule.categoryId,
            discountType: rule.discountType,
            discountValue: rule.discountValue,
            maxDiscount: rule.maxDiscount,
            storeId
          }))
        })
      }

      return pricingTier
    })

    // Create audit log
    await createAuditLog(
      'CREATE',
      'PRICING_TIER',
      `Created pricing tier: ${result.name} (${result.tierType})`,
      user.id,
      storeId
    )

    return createSuccessResponse(result, 'Pricing tier created successfully')

  } catch (error) {
    console.error('Error creating pricing tier:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to create pricing tier',
      400
    )
  }
})

// POST /api/b2b/pricing/calculate - Calculate pricing for order
export const POST = withPermission('B2B', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()

  const calculationSchema = z.object({
    customerId: z.string().min(1, 'Customer is required'),
    items: z.array(z.object({
      productId: z.string().min(1, 'Product is required'),
      quantity: z.number().min(1, 'Quantity must be at least 1')
    })).min(1, 'At least one item is required')
  })

  const { customerId, items } = calculationSchema.parse(body)

  try {
    // Get customer details
    const customer = await prisma.customer.findFirst({
      where: { id: customerId, storeId, isActive: true }
    })

    if (!customer) {
      return createErrorResponse('Customer not found', 404)
    }

    // Get products with base prices
    const productIds = items.map(item => item.productId)
    const products = await prisma.product.findMany({
      where: { id: { in: productIds }, storeId, isActive: true },
      include: {
        category: {
          select: { id: true, name: true }
        }
      }
    })

    if (products.length !== productIds.length) {
      return createErrorResponse('One or more products not found', 404)
    }

    // Get applicable pricing tiers
    const pricingTiers = await prisma.pricingTier.findMany({
      where: { storeId, isActive: true },
      include: {
        discountRules: true
      }
    })

    // Calculate pricing for each item
    const pricingResults = items.map(item => {
      const product = products.find(p => p.id === item.productId)!
      const basePrice = product.sellingPrice
      let finalPrice = basePrice
      let appliedDiscounts: any[] = []

      // Check each pricing tier
      for (const tier of pricingTiers) {
        if (isPricingTierApplicable(tier, customer, item, products)) {
          const discount = calculateDiscount(tier, product, item.quantity, basePrice)
          if (discount.amount > 0) {
            appliedDiscounts.push({
              tierName: tier.name,
              discountType: discount.type,
              discountValue: discount.value,
              discountAmount: discount.amount
            })
            finalPrice = Math.min(finalPrice, basePrice - discount.amount)
          }
        }
      }

      const totalDiscount = basePrice - finalPrice
      const totalAmount = finalPrice * item.quantity

      return {
        productId: item.productId,
        productName: product.name,
        sku: product.sku,
        quantity: item.quantity,
        basePrice,
        finalPrice,
        totalDiscount,
        discountPercentage: basePrice > 0 ? (totalDiscount / basePrice) * 100 : 0,
        totalAmount,
        appliedDiscounts
      }
    })

    const summary = {
      subtotal: pricingResults.reduce((sum, item) => sum + (item.basePrice * item.quantity), 0),
      totalDiscount: pricingResults.reduce((sum, item) => sum + (item.totalDiscount * item.quantity), 0),
      finalTotal: pricingResults.reduce((sum, item) => sum + item.totalAmount, 0)
    }

    return createSuccessResponse({
      customer: {
        id: customer.id,
        name: customer.name,
        loyaltyTier: customer.loyaltyTier
      },
      items: pricingResults,
      summary,
      calculatedAt: new Date()
    }, 'Pricing calculated successfully')

  } catch (error) {
    console.error('Error calculating pricing:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to calculate pricing',
      400
    )
  }
})

// Helper functions

function isPricingTierApplicable(tier: any, customer: any, item: any, products: any[]): boolean {
  const conditions = tier.conditions

  // Check customer tier
  if (conditions.customerTier && customer.loyaltyTier !== conditions.customerTier) {
    return false
  }

  // Check quantity conditions
  if (conditions.minQuantity && item.quantity < conditions.minQuantity) {
    return false
  }

  if (conditions.maxQuantity && item.quantity > conditions.maxQuantity) {
    return false
  }

  // Check product categories
  if (conditions.productCategories && conditions.productCategories.length > 0) {
    const product = products.find(p => p.id === item.productId)
    if (!product || !conditions.productCategories.includes(product.category.id)) {
      return false
    }
  }

  // Check date validity
  const now = new Date()
  if (conditions.validFrom && new Date(conditions.validFrom) > now) {
    return false
  }

  if (conditions.validTo && new Date(conditions.validTo) < now) {
    return false
  }

  return true
}

function calculateDiscount(tier: any, product: any, quantity: number, basePrice: number): any {
  let bestDiscount = { type: '', value: 0, amount: 0 }

  for (const rule of tier.discountRules) {
    // Check if rule applies to this product
    if (rule.productId && rule.productId !== product.id) {
      continue
    }

    if (rule.categoryId && rule.categoryId !== product.category.id) {
      continue
    }

    let discountAmount = 0

    switch (rule.discountType) {
      case 'PERCENTAGE':
        discountAmount = (basePrice * rule.discountValue) / 100
        break
      case 'AMOUNT':
        discountAmount = rule.discountValue
        break
      case 'FIXED_PRICE':
        discountAmount = Math.max(0, basePrice - rule.discountValue)
        break
    }

    // Apply max discount limit
    if (rule.maxDiscount && discountAmount > rule.maxDiscount) {
      discountAmount = rule.maxDiscount
    }

    // Keep the best discount
    if (discountAmount > bestDiscount.amount) {
      bestDiscount = {
        type: rule.discountType,
        value: rule.discountValue,
        amount: discountAmount
      }
    }
  }

  return bestDiscount
}
