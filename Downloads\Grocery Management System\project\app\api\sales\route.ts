import { NextRequest, NextResponse } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  getPaginationParams,
  buildSearchFilter,
  createPaginatedResponse,
  createAuditLog,
  validateStoreAccess,
  buildDateRangeFilter
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const saleItemSchema = z.object({
  productId: z.string().min(1, 'Product is required'),
  quantity: z.number().min(1, 'Quantity must be at least 1'),
  unitPrice: z.number().min(0, 'Unit price must be positive'),
  discount: z.number().min(0).default(0),
  taxRate: z.number().min(0).max(100).default(0)
})

const saleSchema = z.object({
  customerId: z.string().optional(),
  customerName: z.string().optional(),
  customerPhone: z.string().optional(),
  paymentMethod: z.enum(['CASH', 'CARD', 'UPI', 'BANK_TRANSFER', 'CREDIT']),
  items: z.array(saleItemSchema).min(1, 'At least one item is required'),
  discount: z.number().min(0).default(0),
  taxAmount: z.number().min(0).default(0),
  notes: z.string().optional()
})

export const GET = withPermission('SALES', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { page, limit, skip, search, sortBy, sortOrder } = getPaginationParams(request)

  // Get URL parameters for additional filters
  const url = new URL(request.url)
  const startDate = url.searchParams.get('startDate')
  const endDate = url.searchParams.get('endDate')
  const status = url.searchParams.get('status')
  const paymentMethod = url.searchParams.get('paymentMethod')

  // Build filters
  const where: any = {
    storeId,
    ...buildSearchFilter(search, ['saleNo', 'customerName', 'customerPhone']),
    ...buildDateRangeFilter(startDate || undefined, endDate || undefined, 'createdAt')
  }

  if (status) {
    where.status = status
  }

  if (paymentMethod) {
    where.paymentMethod = paymentMethod
  }

  // Get total count
  const total = await prisma.sale.count({ where })

  // Get sales with pagination
  const sales = await prisma.sale.findMany({
    where,
    include: {
      customer: {
        select: {
          id: true,
          name: true,
          phone: true,
          email: true
        }
      },
      items: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
              unit: true
            }
          }
        }
      },
      createdBy: {
        select: {
          id: true,
          name: true
        }
      }
    },
    orderBy: { [sortBy]: sortOrder },
    skip,
    take: limit
  })

  return createPaginatedResponse(sales, page, limit, total)
})

export const POST = withPermission('SALES', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const data = saleSchema.parse(body)

  // Start transaction
  const result = await prisma.$transaction(async (tx) => {
    // Validate products and check inventory
    const productIds = data.items.map(item => item.productId)
    const products = await tx.product.findMany({
      where: {
        id: { in: productIds },
        storeId,
        isActive: true
      },
      include: {
        inventory: {
          where: { storeId }
        }
      }
    })

    if (products.length !== productIds.length) {
      throw new Error('One or more products not found')
    }

    // Check inventory availability
    for (const item of data.items) {
      const product = products.find(p => p.id === item.productId)
      if (!product) {
        throw new Error(`Product not found: ${item.productId}`)
      }

      const inventory = product.inventory[0]
      if (!inventory || inventory.quantity < item.quantity) {
        throw new Error(`Insufficient stock for product: ${product.name}`)
      }
    }

    // Generate sale number
    const saleCount = await tx.sale.count({ where: { storeId } })
    const saleNo = `SAL-${String(saleCount + 1).padStart(6, '0')}`

    // Calculate totals
    let subtotal = 0
    let totalTax = 0
    let totalDiscount = data.discount || 0

    const saleItems = data.items.map(item => {
      const itemTotal = item.quantity * item.unitPrice
      const itemDiscount = item.discount || 0
      const itemTaxableAmount = itemTotal - itemDiscount
      const itemTax = (itemTaxableAmount * item.taxRate) / 100

      subtotal += itemTotal
      totalTax += itemTax
      totalDiscount += itemDiscount

      return {
        productId: item.productId,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalPrice: itemTaxableAmount + itemTax
      }
    })

    const finalTotal = subtotal - totalDiscount + totalTax

    // Create customer if provided but doesn't exist
    let customerId = data.customerId
    if (!customerId && (data.customerName || data.customerPhone)) {
      const customer = await tx.customer.create({
        data: {
          name: data.customerName || 'Walk-in Customer',
          phone: data.customerPhone,
          storeId
        }
      })
      customerId = customer.id
    }

    // Create sale
    const sale = await tx.sale.create({
      data: {
        saleNo,
        customerId,
        paymentMethod: data.paymentMethod,
        discount: totalDiscount,
        taxAmount: totalTax,
        totalAmount: finalTotal,
        paidAmount: finalTotal,
        status: 'COMPLETED',
        notes: data.notes,
        createdById: user.id,
        storeId,
        items: {
          create: saleItems
        }
      },
      include: {
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sku: true,
                unit: true
              }
            }
          }
        },
        customer: true
      }
    })

    // Update inventory
    for (const item of data.items) {
      await tx.inventory.updateMany({
        where: {
          productId: item.productId,
          storeId
        },
        data: {
          quantity: {
            decrement: item.quantity
          },
          lastUpdated: new Date()
        }
      })
    }

    return sale
  })

  // Create audit log
  await createAuditLog(
    'CREATE',
    'SALES',
    `Created sale: ${result.saleNo} - Total: ₹${result.totalAmount}`,
    user.id,
    storeId
  )

  return createSuccessResponse(result, 'Sale created successfully')
})