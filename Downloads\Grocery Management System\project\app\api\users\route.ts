import { NextRequest, NextResponse } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  getPaginationParams,
  buildSearchFilter,
  createPaginatedResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import bcrypt from 'bcryptjs'

const userSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Valid email is required'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  role: z.enum(['FOUNDER', 'SUPER_ADMIN', 'ADMIN', 'STAFF', 'DISTRIBUTOR']),
  phone: z.string().optional(),
  isActive: z.boolean().default(true),
  permissions: z.array(z.string()).optional()
})

const userUpdateSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  email: z.string().email('Valid email is required').optional(),
  password: z.string().min(6, 'Password must be at least 6 characters').optional(),
  role: z.enum(['FOUNDER', 'SUPER_ADMIN', 'ADMIN', 'STAFF', 'DISTRIBUTOR']).optional(),
  phone: z.string().optional(),
  isActive: z.boolean().optional(),
  permissions: z.array(z.string()).optional()
})

// GET /api/users - Get all users with pagination and filters
export const GET = withPermission('USER', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { page, limit, skip, search, sortBy, sortOrder } = getPaginationParams(request)

  // Get URL parameters for additional filters
  const url = new URL(request.url)
  const role = url.searchParams.get('role')
  const isActive = url.searchParams.get('isActive')

  // Build filters
  const where: any = {
    storeId,
    ...buildSearchFilter(search, ['name', 'email', 'phone'])
  }

  if (role) {
    where.role = role
  }

  if (isActive !== null) {
    where.isActive = isActive === 'true'
  }

  // Get total count
  const total = await prisma.user.count({ where })

  // Get users with pagination
  const users = await prisma.user.findMany({
    where,
    select: {
      id: true,
      name: true,
      email: true,
      phone: true,
      role: true,
      isActive: true,
      permissions: true,
      lastLoginAt: true,
      createdAt: true,
      updatedAt: true,
      _count: {
        select: {
          sales: true,
          purchases: true,
          auditLogs: true
        }
      }
    },
    orderBy: { [sortBy]: sortOrder },
    skip,
    take: limit
  })

  return createPaginatedResponse(users, page, limit, total)
})

// POST /api/users - Create new user
export const POST = withPermission('USER', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const data = userSchema.parse(body)

  // Check if email already exists
  const existingUser = await prisma.user.findFirst({
    where: {
      email: data.email,
      storeId
    }
  })

  if (existingUser) {
    return createErrorResponse('User with this email already exists', 400)
  }

  // Hash password
  const hashedPassword = await bcrypt.hash(data.password, 12)

  // Create user
  const newUser = await prisma.user.create({
    data: {
      name: data.name,
      email: data.email,
      password: hashedPassword,
      role: data.role,
      phone: data.phone,
      isActive: data.isActive,
      permissions: data.permissions || [],
      storeId
    },
    select: {
      id: true,
      name: true,
      email: true,
      phone: true,
      role: true,
      isActive: true,
      permissions: true,
      createdAt: true
    }
  })

  // Create audit log
  await createAuditLog(
    'CREATE',
    'USER',
    `Created user: ${newUser.name} (${newUser.email}) with role ${newUser.role}`,
    user.id,
    storeId
  )

  return createSuccessResponse(newUser, 'User created successfully')
})


