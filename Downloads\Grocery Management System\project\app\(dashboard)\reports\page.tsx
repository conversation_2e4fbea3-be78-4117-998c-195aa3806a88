'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/auth/auth-provider'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { toast } from 'sonner'
import { 
  BarChart3, 
  TrendingUp, 
  Download,
  Calendar,
  DollarSign,
  Package,
  Users,
  AlertTriangle,
  FileText
} from 'lucide-react'

export default function ReportsPage() {
  const { token } = useAuth()
  const [loading, setLoading] = useState(false)
  const [reportType, setReportType] = useState('sales-summary')
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  })
  const [reportData, setReportData] = useState<any>(null)

  const reportTypes = [
    { value: 'sales-summary', label: 'Sales Summary', icon: BarChart3 },
    { value: 'sales-detailed', label: 'Detailed Sales Report', icon: FileText },
    { value: 'inventory-summary', label: 'Inventory Summary', icon: Package },
    { value: 'low-stock', label: 'Low Stock Report', icon: AlertTriangle },
    { value: 'top-products', label: 'Top Products', icon: TrendingUp },
    { value: 'customer-summary', label: 'Customer Summary', icon: Users },
    { value: 'financial-summary', label: 'Financial Summary', icon: DollarSign }
  ]

  const generateReport = async () => {
    if (!token) return

    try {
      setLoading(true)
      const params = new URLSearchParams({
        type: reportType,
        startDate: dateRange.startDate,
        endDate: dateRange.endDate
      })

      const response = await fetch(`/api/reports?${params}`, {
        headers: { Authorization: `Bearer ${token}` }
      })

      if (!response.ok) {
        throw new Error('Failed to generate report')
      }

      const result = await response.json()
      setReportData(result.data)
    } catch (error) {
      console.error('Error generating report:', error)
      toast.error('Failed to generate report')
    } finally {
      setLoading(false)
    }
  }

  const exportReport = () => {
    if (!reportData) return

    const dataStr = JSON.stringify(reportData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${reportType}-${dateRange.startDate}-to-${dateRange.endDate}.json`
    link.click()
    URL.revokeObjectURL(url)
  }

  const renderSalesSummary = (data: any) => (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Total Sales</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{data.totalSales}</div>
          <p className="text-sm text-muted-foreground">transactions</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Total Revenue</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">₹{data.totalRevenue.toFixed(2)}</div>
          <p className="text-sm text-muted-foreground">gross revenue</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Average Order Value</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">₹{data.averageOrderValue.toFixed(2)}</div>
          <p className="text-sm text-muted-foreground">per transaction</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Total Tax</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">₹{data.totalTax.toFixed(2)}</div>
          <p className="text-sm text-muted-foreground">tax collected</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Total Discount</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">₹{data.totalDiscount.toFixed(2)}</div>
          <p className="text-sm text-muted-foreground">discounts given</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Items Sold</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{data.totalItems}</div>
          <p className="text-sm text-muted-foreground">total items</p>
        </CardContent>
      </Card>

      {/* Payment Methods */}
      <Card className="md:col-span-3">
        <CardHeader>
          <CardTitle>Payment Methods</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.entries(data.paymentMethods).map(([method, count]: [string, any]) => (
              <div key={method} className="text-center">
                <div className="text-lg font-bold">{count}</div>
                <div className="text-sm text-muted-foreground">{method}</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderInventorySummary = (data: any) => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Total Products</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{data.totalProducts}</div>
          <p className="text-sm text-muted-foreground">in inventory</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Stock Value</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">₹{data.totalStockValue.toFixed(2)}</div>
          <p className="text-sm text-muted-foreground">total value</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Low Stock Items</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-orange-600">{data.lowStockItems}</div>
          <p className="text-sm text-muted-foreground">need reorder</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Out of Stock</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">{data.outOfStockItems}</div>
          <p className="text-sm text-muted-foreground">zero quantity</p>
        </CardContent>
      </Card>

      {/* Category Breakdown */}
      <Card className="md:col-span-2 lg:col-span-4">
        <CardHeader>
          <CardTitle>Category Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {Object.entries(data.categoryBreakdown).map(([category, stats]: [string, any]) => (
              <div key={category} className="flex justify-between items-center p-2 border rounded">
                <span className="font-medium">{category}</span>
                <div className="text-right">
                  <div>{stats.products} products</div>
                  <div className="text-sm text-muted-foreground">₹{stats.stockValue.toFixed(2)}</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderLowStockReport = (data: any[]) => (
    <Card>
      <CardHeader>
        <CardTitle>Low Stock Items</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left p-2">Product</th>
                <th className="text-left p-2">SKU</th>
                <th className="text-left p-2">Category</th>
                <th className="text-right p-2">Current Stock</th>
                <th className="text-right p-2">Min Stock</th>
                <th className="text-right p-2">Cost Price</th>
              </tr>
            </thead>
            <tbody>
              {data.map((item, index) => (
                <tr key={index} className="border-b">
                  <td className="p-2 font-medium">{item.name}</td>
                  <td className="p-2 text-sm font-mono">{item.sku}</td>
                  <td className="p-2 text-sm">{item.category}</td>
                  <td className="p-2 text-right text-orange-600 font-medium">
                    {item.currentStock} {item.unit}
                  </td>
                  <td className="p-2 text-right">{item.minStock} {item.unit}</td>
                  <td className="p-2 text-right">₹{item.costPrice.toFixed(2)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  )

  const renderTopProducts = (data: any[]) => (
    <Card>
      <CardHeader>
        <CardTitle>Top Selling Products</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left p-2">Product</th>
                <th className="text-left p-2">Category</th>
                <th className="text-right p-2">Quantity Sold</th>
                <th className="text-right p-2">Revenue</th>
                <th className="text-right p-2">Sales Count</th>
              </tr>
            </thead>
            <tbody>
              {data.map((item, index) => (
                <tr key={index} className="border-b">
                  <td className="p-2">
                    <div className="font-medium">{item.product.name}</div>
                    <div className="text-sm text-muted-foreground">{item.product.sku}</div>
                  </td>
                  <td className="p-2 text-sm">{item.product.category?.name}</td>
                  <td className="p-2 text-right font-medium">
                    {item.totalQuantity} {item.product.unit}
                  </td>
                  <td className="p-2 text-right font-medium">₹{item.totalRevenue.toFixed(2)}</td>
                  <td className="p-2 text-right">{item.totalSales}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  )

  const renderFinancialSummary = (data: any) => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <Card>
        <CardHeader>
          <CardTitle>Revenue</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="flex justify-between">
            <span>Total Revenue:</span>
            <span className="font-bold">₹{data.revenue.total.toFixed(2)}</span>
          </div>
          <div className="flex justify-between">
            <span>Tax Collected:</span>
            <span>₹{data.revenue.tax.toFixed(2)}</span>
          </div>
          <div className="flex justify-between">
            <span>Discounts Given:</span>
            <span>₹{data.revenue.discount.toFixed(2)}</span>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Expenses</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="flex justify-between">
            <span>Total Expenses:</span>
            <span className="font-bold">₹{data.expenses.total.toFixed(2)}</span>
          </div>
          <div className="flex justify-between">
            <span>Tax Paid:</span>
            <span>₹{data.expenses.tax.toFixed(2)}</span>
          </div>
          <div className="flex justify-between">
            <span>Discounts Received:</span>
            <span>₹{data.expenses.discount.toFixed(2)}</span>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Profit</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="flex justify-between">
            <span>Gross Profit:</span>
            <span className={`font-bold ${data.profit.gross >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              ₹{data.profit.gross.toFixed(2)}
            </span>
          </div>
          <div className="flex justify-between">
            <span>Profit Margin:</span>
            <span className={`font-bold ${data.profit.margin >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {data.profit.margin.toFixed(2)}%
            </span>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Transactions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="flex justify-between">
            <span>Sales Count:</span>
            <span className="font-bold">{data.transactions.salesCount}</span>
          </div>
          <div className="flex justify-between">
            <span>Purchases Count:</span>
            <span className="font-bold">{data.transactions.purchasesCount}</span>
          </div>
          <div className="flex justify-between">
            <span>Avg Sale Value:</span>
            <span>₹{data.transactions.averageSaleValue.toFixed(2)}</span>
          </div>
          <div className="flex justify-between">
            <span>Avg Purchase Value:</span>
            <span>₹{data.transactions.averagePurchaseValue.toFixed(2)}</span>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderReportData = () => {
    if (!reportData) return null

    switch (reportType) {
      case 'sales-summary':
        return renderSalesSummary(reportData)
      case 'inventory-summary':
        return renderInventorySummary(reportData)
      case 'low-stock':
        return renderLowStockReport(reportData)
      case 'top-products':
        return renderTopProducts(reportData)
      case 'financial-summary':
        return renderFinancialSummary(reportData)
      case 'sales-detailed':
      case 'customer-summary':
        return (
          <Card>
            <CardHeader>
              <CardTitle>Report Data</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="text-sm overflow-auto max-h-96">
                {JSON.stringify(reportData, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )
      default:
        return null
    }
  }

  useEffect(() => {
    generateReport()
  }, [token])

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Reports</h1>
          <p className="text-muted-foreground">Generate and view business reports</p>
        </div>
      </div>

      {/* Report Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Generate Report</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="reportType">Report Type</Label>
              <Select value={reportType} onValueChange={setReportType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {reportTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      <div className="flex items-center gap-2">
                        <type.icon className="h-4 w-4" />
                        {type.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="startDate">Start Date</Label>
              <Input
                id="startDate"
                type="date"
                value={dateRange.startDate}
                onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
              />
            </div>

            <div>
              <Label htmlFor="endDate">End Date</Label>
              <Input
                id="endDate"
                type="date"
                value={dateRange.endDate}
                onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
              />
            </div>

            <div className="flex items-end gap-2">
              <Button onClick={generateReport} disabled={loading} className="flex-1">
                {loading ? 'Generating...' : 'Generate Report'}
              </Button>
              {reportData && (
                <Button variant="outline" onClick={exportReport}>
                  <Download className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Report Data */}
      {reportData && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">
              {reportTypes.find(t => t.value === reportType)?.label}
            </h2>
            <div className="text-sm text-muted-foreground">
              {dateRange.startDate} to {dateRange.endDate}
            </div>
          </div>
          {renderReportData()}
        </div>
      )}
    </div>
  )
}
