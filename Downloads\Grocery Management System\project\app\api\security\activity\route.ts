import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  getPaginationParams,
  buildSearchFilter,
  createPaginatedResponse,
  validateStoreAccess,
  buildDateRangeFilter
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// GET /api/security/activity - Get user activity tracking data
export const GET = withPermission('SECURITY', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { page, limit, skip, search, sortBy, sortOrder } = getPaginationParams(request)
  const { searchParams } = new URL(request.url)
  
  const userId = searchParams.get('userId')
  const action = searchParams.get('action')
  const entityType = searchParams.get('entityType')
  const ipAddress = searchParams.get('ipAddress')
  const startDate = searchParams.get('startDate')
  const endDate = searchParams.get('endDate')
  const includeAnalytics = searchParams.get('analytics') === 'true'

  try {
    // Build filters
    const where: any = {
      storeId,
      ...buildSearchFilter(search, ['description', 'entityType']),
      ...buildDateRangeFilter(startDate || undefined, endDate || undefined, 'createdAt')
    }

    if (userId) {
      where.userId = userId
    }

    if (action) {
      where.action = action
    }

    if (entityType) {
      where.entityType = entityType
    }

    if (ipAddress) {
      where.ipAddress = ipAddress
    }

    // Get total count
    const total = await prisma.auditLog.count({ where })

    // Get activity logs with pagination
    const activities = await prisma.auditLog.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      },
      orderBy: { [sortBy]: sortOrder },
      skip,
      take: limit
    })

    // Add calculated fields
    const activitiesWithCalculations = activities.map(activity => ({
      ...activity,
      timeAgo: getTimeAgo(activity.createdAt),
      riskLevel: calculateActivityRisk(activity),
      sessionInfo: extractSessionInfo(activity),
      deviceInfo: extractDeviceInfo(activity.userAgent),
      locationInfo: extractLocationInfo(activity.metadata)
    }))

    let analytics = null
    if (includeAnalytics) {
      analytics = await generateActivityAnalytics(storeId, where)
    }

    return createPaginatedResponse(activitiesWithCalculations, page, limit, total, { analytics })

  } catch (error) {
    console.error('Error fetching user activity:', error)
    return createErrorResponse('Failed to fetch user activity', 500)
  }
})

// POST /api/security/activity/track - Track custom user activity
export const POST = withPermission('USER', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()

  const activitySchema = z.object({
    action: z.string().min(1, 'Action is required'),
    entityType: z.string().min(1, 'Entity type is required'),
    entityId: z.string().optional(),
    description: z.string().min(1, 'Description is required'),
    metadata: z.record(z.any()).optional(),
    customFields: z.record(z.any()).optional()
  })

  const data = activitySchema.parse(body)

  try {
    // Enrich activity data with request information
    const enrichedMetadata = {
      ...data.metadata,
      timestamp: new Date().toISOString(),
      sessionId: extractSessionId(request),
      referrer: request.headers.get('referer'),
      ...data.customFields
    }

    const activity = await prisma.auditLog.create({
      data: {
        action: data.action,
        entityType: data.entityType,
        entityId: data.entityId,
        description: data.description,
        metadata: enrichedMetadata,
        userId: user.id,
        ipAddress: getClientIP(request),
        userAgent: request.headers.get('user-agent') || '',
        severity: 'LOW',
        storeId
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            role: true
          }
        }
      }
    })

    return createSuccessResponse(activity, 'Activity tracked successfully')

  } catch (error) {
    console.error('Error tracking activity:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to track activity',
      400
    )
  }
})

// GET /api/security/activity/analytics - Get detailed activity analytics
export const PUT = withPermission('SECURITY', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { searchParams } = new URL(request.url)
  const period = parseInt(searchParams.get('period') || '7') // days
  const userId = searchParams.get('userId')
  const groupBy = searchParams.get('groupBy') || 'day' // hour, day, week

  try {
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - period)

    const where: any = {
      storeId,
      createdAt: { gte: startDate }
    }

    if (userId) {
      where.userId = userId
    }

    const [
      activityTrends,
      userActivity,
      actionBreakdown,
      entityBreakdown,
      timePatterns,
      locationAnalysis,
      deviceAnalysis,
      riskAnalysis
    ] = await Promise.all([
      // Activity trends over time
      getActivityTrends(storeId, startDate, groupBy),
      
      // User activity summary
      getUserActivitySummary(storeId, startDate, userId),
      
      // Action breakdown
      getActionBreakdown(storeId, startDate, userId),
      
      // Entity type breakdown
      getEntityBreakdown(storeId, startDate, userId),
      
      // Time pattern analysis
      getTimePatterns(storeId, startDate, userId),
      
      // Location analysis
      getLocationAnalysis(storeId, startDate, userId),
      
      // Device analysis
      getDeviceAnalysis(storeId, startDate, userId),
      
      // Risk analysis
      getRiskAnalysis(storeId, startDate, userId)
    ])

    const analytics = {
      period: { days: period, startDate, endDate: new Date() },
      trends: activityTrends,
      users: userActivity,
      actions: actionBreakdown,
      entities: entityBreakdown,
      timePatterns,
      locations: locationAnalysis,
      devices: deviceAnalysis,
      risks: riskAnalysis,
      insights: generateActivityInsights({
        trends: activityTrends,
        users: userActivity,
        risks: riskAnalysis
      })
    }

    return createSuccessResponse(analytics, 'Activity analytics generated successfully')

  } catch (error) {
    console.error('Error generating activity analytics:', error)
    return createErrorResponse('Failed to generate activity analytics', 500)
  }
})

// Helper functions

async function generateActivityAnalytics(storeId: string, baseWhere: any) {
  const [
    totalActivities,
    uniqueUsers,
    topActions,
    recentActivity,
    riskDistribution
  ] = await Promise.all([
    // Total activities
    prisma.auditLog.count({ where: baseWhere }),

    // Unique active users
    prisma.auditLog.findMany({
      where: { ...baseWhere, userId: { not: null } },
      select: { userId: true },
      distinct: ['userId']
    }).then(users => users.length),

    // Top actions
    prisma.auditLog.groupBy({
      by: ['action'],
      where: baseWhere,
      _count: true,
      orderBy: { _count: { action: 'desc' } },
      take: 5
    }),

    // Recent activity (last hour)
    prisma.auditLog.count({
      where: {
        ...baseWhere,
        createdAt: { gte: new Date(Date.now() - 60 * 60 * 1000) }
      }
    }),

    // Risk level distribution
    prisma.auditLog.groupBy({
      by: ['severity'],
      where: baseWhere,
      _count: true
    })
  ])

  return {
    summary: {
      totalActivities,
      uniqueUsers,
      recentActivity
    },
    topActions: topActions.map(action => ({
      action: action.action,
      count: action._count
    })),
    riskDistribution: riskDistribution.map(risk => ({
      level: risk.severity,
      count: risk._count
    }))
  }
}

async function getActivityTrends(storeId: string, startDate: Date, groupBy: string) {
  const activities = await prisma.auditLog.findMany({
    where: {
      storeId,
      createdAt: { gte: startDate }
    },
    select: {
      createdAt: true,
      action: true,
      severity: true
    },
    orderBy: { createdAt: 'asc' }
  })

  const trends = new Map()
  
  activities.forEach(activity => {
    let key: string
    const date = new Date(activity.createdAt)
    
    switch (groupBy) {
      case 'hour':
        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:00`
        break
      case 'week':
        const weekStart = new Date(date)
        weekStart.setDate(date.getDate() - date.getDay())
        key = weekStart.toISOString().split('T')[0]
        break
      default: // day
        key = date.toISOString().split('T')[0]
    }
    
    if (!trends.has(key)) {
      trends.set(key, { period: key, total: 0, high: 0, critical: 0 })
    }
    
    const trend = trends.get(key)
    trend.total += 1
    if (activity.severity === 'HIGH') trend.high += 1
    if (activity.severity === 'CRITICAL') trend.critical += 1
  })

  return Array.from(trends.values()).sort((a, b) => a.period.localeCompare(b.period))
}

async function getUserActivitySummary(storeId: string, startDate: Date, userId?: string) {
  const where: any = {
    storeId,
    createdAt: { gte: startDate },
    userId: { not: null }
  }

  if (userId) {
    where.userId = userId
  }

  const userActivities = await prisma.auditLog.groupBy({
    by: ['userId'],
    where,
    _count: true,
    orderBy: { _count: { userId: 'desc' } },
    take: 10
  })

  const userIds = userActivities.map(ua => ua.userId).filter(Boolean)
  const users = await prisma.user.findMany({
    where: { id: { in: userIds } },
    select: { id: true, name: true, role: true }
  })

  return userActivities.map(ua => ({
    user: users.find(u => u.id === ua.userId),
    activityCount: ua._count,
    lastActivity: null // Would need additional query for this
  }))
}

async function getActionBreakdown(storeId: string, startDate: Date, userId?: string) {
  const where: any = {
    storeId,
    createdAt: { gte: startDate }
  }

  if (userId) {
    where.userId = userId
  }

  return await prisma.auditLog.groupBy({
    by: ['action'],
    where,
    _count: true,
    orderBy: { _count: { action: 'desc' } }
  }).then(actions => 
    actions.map(action => ({
      action: action.action,
      count: action._count
    }))
  )
}

async function getEntityBreakdown(storeId: string, startDate: Date, userId?: string) {
  const where: any = {
    storeId,
    createdAt: { gte: startDate }
  }

  if (userId) {
    where.userId = userId
  }

  return await prisma.auditLog.groupBy({
    by: ['entityType'],
    where,
    _count: true,
    orderBy: { _count: { entityType: 'desc' } }
  }).then(entities => 
    entities.map(entity => ({
      entityType: entity.entityType,
      count: entity._count
    }))
  )
}

async function getTimePatterns(storeId: string, startDate: Date, userId?: string) {
  const where: any = {
    storeId,
    createdAt: { gte: startDate }
  }

  if (userId) {
    where.userId = userId
  }

  const activities = await prisma.auditLog.findMany({
    where,
    select: { createdAt: true }
  })

  // Analyze by hour of day
  const hourlyPattern = Array.from({ length: 24 }, (_, hour) => ({
    hour,
    count: activities.filter(a => new Date(a.createdAt).getHours() === hour).length
  }))

  // Analyze by day of week
  const dailyPattern = Array.from({ length: 7 }, (_, day) => ({
    day: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][day],
    count: activities.filter(a => new Date(a.createdAt).getDay() === day).length
  }))

  return {
    hourly: hourlyPattern,
    daily: dailyPattern
  }
}

async function getLocationAnalysis(storeId: string, startDate: Date, userId?: string) {
  const where: any = {
    storeId,
    createdAt: { gte: startDate }
  }

  if (userId) {
    where.userId = userId
  }

  const activities = await prisma.auditLog.findMany({
    where,
    select: { ipAddress: true, metadata: true }
  })

  const ipCounts = activities.reduce((acc: any, activity) => {
    acc[activity.ipAddress] = (acc[activity.ipAddress] || 0) + 1
    return acc
  }, {})

  return Object.entries(ipCounts)
    .sort(([, a], [, b]) => (b as number) - (a as number))
    .slice(0, 10)
    .map(([ip, count]) => ({
      ipAddress: ip,
      count,
      location: 'Unknown' // Would integrate with IP geolocation service
    }))
}

async function getDeviceAnalysis(storeId: string, startDate: Date, userId?: string) {
  const where: any = {
    storeId,
    createdAt: { gte: startDate }
  }

  if (userId) {
    where.userId = userId
  }

  const activities = await prisma.auditLog.findMany({
    where,
    select: { userAgent: true }
  })

  const deviceTypes = activities.reduce((acc: any, activity) => {
    const deviceInfo = extractDeviceInfo(activity.userAgent)
    const key = `${deviceInfo.browser} on ${deviceInfo.os}`
    acc[key] = (acc[key] || 0) + 1
    return acc
  }, {})

  return Object.entries(deviceTypes)
    .sort(([, a], [, b]) => (b as number) - (a as number))
    .slice(0, 10)
    .map(([device, count]) => ({
      device,
      count
    }))
}

async function getRiskAnalysis(storeId: string, startDate: Date, userId?: string) {
  const where: any = {
    storeId,
    createdAt: { gte: startDate }
  }

  if (userId) {
    where.userId = userId
  }

  const [highRisk, mediumRisk, lowRisk] = await Promise.all([
    prisma.auditLog.count({ where: { ...where, severity: 'HIGH' } }),
    prisma.auditLog.count({ where: { ...where, severity: 'MEDIUM' } }),
    prisma.auditLog.count({ where: { ...where, severity: 'LOW' } })
  ])

  const total = highRisk + mediumRisk + lowRisk

  return {
    distribution: {
      high: { count: highRisk, percentage: total > 0 ? (highRisk / total) * 100 : 0 },
      medium: { count: mediumRisk, percentage: total > 0 ? (mediumRisk / total) * 100 : 0 },
      low: { count: lowRisk, percentage: total > 0 ? (lowRisk / total) * 100 : 0 }
    },
    total
  }
}

function calculateActivityRisk(activity: any): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
  let riskScore = 0

  // Base risk by action
  const actionRisk = {
    'DELETE': 3,
    'UPDATE': 2,
    'CREATE': 1,
    'READ': 0,
    'LOGIN': 1,
    'LOGOUT': 0
  }

  riskScore += actionRisk[activity.action as keyof typeof actionRisk] || 0

  // Entity type risk
  const entityRisk = {
    'USER': 3,
    'SETTING': 3,
    'SECURITY': 4,
    'PAYMENT': 3,
    'FINANCIAL': 2
  }

  riskScore += entityRisk[activity.entityType as keyof typeof entityRisk] || 0

  // Time-based risk
  const hour = new Date(activity.createdAt).getHours()
  if (hour < 6 || hour > 22) riskScore += 2 // Outside business hours

  // Convert to risk level
  if (riskScore >= 8) return 'CRITICAL'
  if (riskScore >= 6) return 'HIGH'
  if (riskScore >= 3) return 'MEDIUM'
  return 'LOW'
}

function extractSessionInfo(activity: any): any {
  const metadata = activity.metadata || {}
  return {
    sessionId: metadata.sessionId || 'unknown',
    duration: metadata.sessionDuration || null,
    pageViews: metadata.pageViews || null
  }
}

function extractDeviceInfo(userAgent: string): any {
  // Simplified user agent parsing
  const isMobile = /Mobile|Android|iPhone|iPad/.test(userAgent)
  const isTablet = /iPad|Tablet/.test(userAgent)
  
  let browser = 'Unknown'
  if (userAgent.includes('Chrome')) browser = 'Chrome'
  else if (userAgent.includes('Firefox')) browser = 'Firefox'
  else if (userAgent.includes('Safari')) browser = 'Safari'
  else if (userAgent.includes('Edge')) browser = 'Edge'

  let os = 'Unknown'
  if (userAgent.includes('Windows')) os = 'Windows'
  else if (userAgent.includes('Mac')) os = 'macOS'
  else if (userAgent.includes('Linux')) os = 'Linux'
  else if (userAgent.includes('Android')) os = 'Android'
  else if (userAgent.includes('iOS')) os = 'iOS'

  return {
    browser,
    os,
    deviceType: isTablet ? 'Tablet' : isMobile ? 'Mobile' : 'Desktop'
  }
}

function extractLocationInfo(metadata: any): any {
  if (!metadata) return null
  
  return {
    country: metadata.country || 'Unknown',
    city: metadata.city || 'Unknown',
    timezone: metadata.timezone || 'Unknown'
  }
}

function extractSessionId(request: NextRequest): string {
  // Extract session ID from cookies or headers
  const sessionCookie = request.cookies.get('session-id')
  return sessionCookie?.value || 'unknown'
}

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  return request.ip || 'unknown'
}

function getTimeAgo(date: Date): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) return 'Just now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`
  return date.toLocaleDateString()
}

function generateActivityInsights(data: any): string[] {
  const insights = []

  // Analyze trends
  if (data.trends.length > 1) {
    const recent = data.trends.slice(-3).reduce((sum: number, trend: any) => sum + trend.total, 0)
    const previous = data.trends.slice(-6, -3).reduce((sum: number, trend: any) => sum + trend.total, 0)
    
    if (recent > previous * 1.2) {
      insights.push('User activity has increased significantly in recent periods')
    } else if (recent < previous * 0.8) {
      insights.push('User activity has decreased in recent periods')
    }
  }

  // Risk analysis
  if (data.risks.distribution.high.percentage > 20) {
    insights.push('High percentage of high-risk activities detected')
  }

  // User activity patterns
  if (data.users.length > 0) {
    const topUser = data.users[0]
    if (topUser.activityCount > 100) {
      insights.push(`${topUser.user?.name || 'Top user'} shows unusually high activity levels`)
    }
  }

  return insights
}
