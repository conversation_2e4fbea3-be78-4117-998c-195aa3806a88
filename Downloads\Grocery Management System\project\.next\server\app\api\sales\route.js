"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/sales/route";
exports.ids = ["app/api/sales/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsales%2Froute&page=%2Fapi%2Fsales%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsales%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsales%2Froute&page=%2Fapi%2Fsales%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsales%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/node-polyfill-headers */ \"(rsc)/./node_modules/next/dist/server/node-polyfill-headers.js\");\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var C_Users_DELL_Downloads_Grocery_Management_System_project_app_api_sales_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/sales/route.ts */ \"(rsc)/./app/api/sales/route.ts\");\n\n// @ts-ignore this need to be imported from next/dist to be external\n\n\n// @ts-expect-error - replaced by webpack/turbopack loader\n\nconst AppRouteRouteModule = next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__.AppRouteRouteModule;\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__.RouteKind.APP_ROUTE,\n        page: \"/api/sales/route\",\n        pathname: \"/api/sales\",\n        filename: \"route\",\n        bundlePath: \"app/api/sales/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\api\\\\sales\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_DELL_Downloads_Grocery_Management_System_project_app_api_sales_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/sales/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsales%2Froute&page=%2Fapi%2Fsales%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsales%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/sales/route.ts":
/*!********************************!*\
  !*** ./app/api/sales/route.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_api_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api-utils */ \"(rsc)/./lib/api-utils.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\nconst saleItemSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    productId: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(1, \"Product is required\"),\n    quantity: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().min(1, \"Quantity must be at least 1\"),\n    unitPrice: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().min(0, \"Unit price must be positive\"),\n    discount: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().min(0).default(0),\n    taxRate: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().min(0).max(100).default(0)\n});\nconst saleSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    customerId: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    customerName: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    customerPhone: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    paymentMethod: zod__WEBPACK_IMPORTED_MODULE_2__.z[\"enum\"]([\n        \"CASH\",\n        \"CARD\",\n        \"UPI\",\n        \"BANK_TRANSFER\",\n        \"CREDIT\"\n    ]),\n    items: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(saleItemSchema).min(1, \"At least one item is required\"),\n    discount: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().min(0).default(0),\n    taxAmount: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().min(0).default(0),\n    notes: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional()\n});\nconst GET = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.withPermission)(\"SALES\", \"READ\", async (request, user)=>{\n    const storeId = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.validateStoreAccess)(user);\n    const { page, limit, skip, search, sortBy, sortOrder } = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.getPaginationParams)(request);\n    // Get URL parameters for additional filters\n    const url = new URL(request.url);\n    const startDate = url.searchParams.get(\"startDate\");\n    const endDate = url.searchParams.get(\"endDate\");\n    const status = url.searchParams.get(\"status\");\n    const paymentMethod = url.searchParams.get(\"paymentMethod\");\n    // Build filters\n    const where = {\n        storeId,\n        ...(0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.buildSearchFilter)(search, [\n            \"saleNo\",\n            \"customerName\",\n            \"customerPhone\"\n        ]),\n        ...(0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.buildDateRangeFilter)(startDate || undefined, endDate || undefined, \"createdAt\")\n    };\n    if (status) {\n        where.status = status;\n    }\n    if (paymentMethod) {\n        where.paymentMethod = paymentMethod;\n    }\n    // Get total count\n    const total = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.sale.count({\n        where\n    });\n    // Get sales with pagination\n    const sales = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.sale.findMany({\n        where,\n        include: {\n            customer: {\n                select: {\n                    id: true,\n                    name: true,\n                    phone: true,\n                    email: true\n                }\n            },\n            items: {\n                include: {\n                    product: {\n                        select: {\n                            id: true,\n                            name: true,\n                            sku: true,\n                            unit: true\n                        }\n                    }\n                }\n            },\n            createdBy: {\n                select: {\n                    id: true,\n                    name: true\n                }\n            }\n        },\n        orderBy: {\n            [sortBy]: sortOrder\n        },\n        skip,\n        take: limit\n    });\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createPaginatedResponse)(sales, page, limit, total);\n});\nconst POST = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.withPermission)(\"SALES\", \"CREATE\", async (request, user)=>{\n    const storeId = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.validateStoreAccess)(user);\n    const body = await request.json();\n    const data = saleSchema.parse(body);\n    // Start transaction\n    const result = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$transaction(async (tx)=>{\n        // Validate products and check inventory\n        const productIds = data.items.map((item)=>item.productId);\n        const products = await tx.product.findMany({\n            where: {\n                id: {\n                    in: productIds\n                },\n                storeId,\n                isActive: true\n            },\n            include: {\n                inventory: {\n                    where: {\n                        storeId\n                    }\n                }\n            }\n        });\n        if (products.length !== productIds.length) {\n            throw new Error(\"One or more products not found\");\n        }\n        // Check inventory availability\n        for (const item of data.items){\n            const product = products.find((p)=>p.id === item.productId);\n            if (!product) {\n                throw new Error(`Product not found: ${item.productId}`);\n            }\n            const inventory = product.inventory[0];\n            if (!inventory || inventory.quantity < item.quantity) {\n                throw new Error(`Insufficient stock for product: ${product.name}`);\n            }\n        }\n        // Generate sale number\n        const saleCount = await tx.sale.count({\n            where: {\n                storeId\n            }\n        });\n        const saleNo = `SAL-${String(saleCount + 1).padStart(6, \"0\")}`;\n        // Calculate totals\n        let subtotal = 0;\n        let totalTax = 0;\n        let totalDiscount = data.discount || 0;\n        const saleItems = data.items.map((item)=>{\n            const itemTotal = item.quantity * item.unitPrice;\n            const itemDiscount = item.discount || 0;\n            const itemTaxableAmount = itemTotal - itemDiscount;\n            const itemTax = itemTaxableAmount * item.taxRate / 100;\n            subtotal += itemTotal;\n            totalTax += itemTax;\n            totalDiscount += itemDiscount;\n            return {\n                productId: item.productId,\n                quantity: item.quantity,\n                unitPrice: item.unitPrice,\n                totalPrice: itemTaxableAmount + itemTax\n            };\n        });\n        const finalTotal = subtotal - totalDiscount + totalTax;\n        // Create customer if provided but doesn't exist\n        let customerId = data.customerId;\n        if (!customerId && (data.customerName || data.customerPhone)) {\n            const customer = await tx.customer.create({\n                data: {\n                    name: data.customerName || \"Walk-in Customer\",\n                    phone: data.customerPhone,\n                    storeId\n                }\n            });\n            customerId = customer.id;\n        }\n        // Create sale\n        const sale = await tx.sale.create({\n            data: {\n                saleNo,\n                customerId,\n                paymentMethod: data.paymentMethod,\n                discount: totalDiscount,\n                taxAmount: totalTax,\n                totalAmount: finalTotal,\n                paidAmount: finalTotal,\n                status: \"COMPLETED\",\n                notes: data.notes,\n                createdById: user.id,\n                storeId,\n                items: {\n                    create: saleItems\n                }\n            },\n            include: {\n                items: {\n                    include: {\n                        product: {\n                            select: {\n                                id: true,\n                                name: true,\n                                sku: true,\n                                unit: true\n                            }\n                        }\n                    }\n                },\n                customer: true\n            }\n        });\n        // Update inventory\n        for (const item of data.items){\n            await tx.inventory.updateMany({\n                where: {\n                    productId: item.productId,\n                    storeId\n                },\n                data: {\n                    quantity: {\n                        decrement: item.quantity\n                    },\n                    lastUpdated: new Date()\n                }\n            });\n        }\n        return sale;\n    });\n    // Create audit log\n    await (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createAuditLog)(\"CREATE\", \"SALES\", `Created sale: ${result.saleNo} - Total: ₹${result.totalAmount}`, user.id, storeId);\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)(result, \"Sale created successfully\");\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/sales/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/api-utils.ts":
/*!**************************!*\
  !*** ./lib/api-utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildDateRangeFilter: () => (/* binding */ buildDateRangeFilter),\n/* harmony export */   buildSearchFilter: () => (/* binding */ buildSearchFilter),\n/* harmony export */   createAuditLog: () => (/* binding */ createAuditLog),\n/* harmony export */   createErrorResponse: () => (/* binding */ createErrorResponse),\n/* harmony export */   createPaginatedResponse: () => (/* binding */ createPaginatedResponse),\n/* harmony export */   createResponse: () => (/* binding */ createResponse),\n/* harmony export */   createSuccessResponse: () => (/* binding */ createSuccessResponse),\n/* harmony export */   getPaginationParams: () => (/* binding */ getPaginationParams),\n/* harmony export */   validateRequiredFields: () => (/* binding */ validateRequiredFields),\n/* harmony export */   validateStoreAccess: () => (/* binding */ validateStoreAccess),\n/* harmony export */   withPermission: () => (/* binding */ withPermission)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n// Standard API response wrapper\nfunction createResponse(data, status = 200) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(data, {\n        status\n    });\n}\n// Error response wrapper\nfunction createErrorResponse(message, status = 400) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        error: message\n    }, {\n        status\n    });\n}\n// Success response wrapper\nfunction createSuccessResponse(data, message) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        success: true,\n        data,\n        message\n    });\n}\n// Paginated response wrapper\nfunction createPaginatedResponse(data, page, limit, total) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        data,\n        pagination: {\n            page,\n            limit,\n            total,\n            totalPages: Math.ceil(total / limit),\n            hasNext: page * limit < total,\n            hasPrev: page > 1\n        }\n    });\n}\n// Permission-based API handler wrapper\nfunction withPermission(module, permission, handler) {\n    return async (request)=>{\n        try {\n            const permissionCheck = await (0,_auth__WEBPACK_IMPORTED_MODULE_1__.checkPermission)(request, module, permission);\n            if (permissionCheck.error) {\n                return createErrorResponse(permissionCheck.error, permissionCheck.status);\n            }\n            return await handler(request, permissionCheck.user);\n        } catch (error) {\n            console.error(`API Error in ${module}:`, error);\n            return createErrorResponse(\"Internal server error\", 500);\n        }\n    };\n}\n// Audit log helper\nasync function createAuditLog(action, module, details, userId, storeId) {\n    try {\n        await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.auditLog.create({\n            data: {\n                action,\n                module,\n                details,\n                userId,\n                storeId\n            }\n        });\n    } catch (error) {\n        console.error(\"Failed to create audit log:\", error);\n    }\n}\n// Pagination helper\nfunction getPaginationParams(request) {\n    const url = new URL(request.url);\n    const page = parseInt(url.searchParams.get(\"page\") || \"1\");\n    const limit = parseInt(url.searchParams.get(\"limit\") || \"10\");\n    const search = url.searchParams.get(\"search\") || \"\";\n    const sortBy = url.searchParams.get(\"sortBy\") || \"createdAt\";\n    const sortOrder = url.searchParams.get(\"sortOrder\") || \"desc\";\n    return {\n        page: Math.max(1, page),\n        limit: Math.min(100, Math.max(1, limit)),\n        skip: (Math.max(1, page) - 1) * Math.min(100, Math.max(1, limit)),\n        search,\n        sortBy,\n        sortOrder: sortOrder === \"asc\" ? \"asc\" : \"desc\"\n    };\n}\n// Search filter helper\nfunction buildSearchFilter(search, fields) {\n    if (!search) return {};\n    return {\n        OR: fields.map((field)=>({\n                [field]: {\n                    contains: search,\n                    mode: \"insensitive\"\n                }\n            }))\n    };\n}\n// Date range filter helper\nfunction buildDateRangeFilter(startDate, endDate, field = \"createdAt\") {\n    const filter = {};\n    if (startDate) {\n        filter[field] = {\n            ...filter[field],\n            gte: new Date(startDate)\n        };\n    }\n    if (endDate) {\n        const end = new Date(endDate);\n        end.setHours(23, 59, 59, 999);\n        filter[field] = {\n            ...filter[field],\n            lte: end\n        };\n    }\n    return Object.keys(filter).length > 0 ? filter : {};\n}\n// Validation helper\nfunction validateRequiredFields(data, fields) {\n    const missing = fields.filter((field)=>!data[field]);\n    if (missing.length > 0) {\n        throw new Error(`Missing required fields: ${missing.join(\", \")}`);\n    }\n}\n// Store validation helper\nfunction validateStoreAccess(user) {\n    if (!user.storeId) {\n        throw new Error(\"User not associated with any store\");\n    }\n    return user.storeId;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/api-utils.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkPermission: () => (/* binding */ checkPermission),\n/* harmony export */   createAuthResponse: () => (/* binding */ createAuthResponse),\n/* harmony export */   getAuthUser: () => (/* binding */ getAuthUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   signToken: () => (/* binding */ signToken),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key\";\nasync function hashPassword(password) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(password, 12);\n}\nasync function verifyPassword(password, hashedPassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, hashedPassword);\n}\nfunction signToken(payload) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: \"7d\"\n    });\n}\nfunction verifyToken(token) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n}\nasync function getAuthUser(request) {\n    try {\n        const token = request.headers.get(\"authorization\")?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return null;\n        }\n        const payload = verifyToken(token);\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n            where: {\n                id: payload.userId\n            },\n            include: {\n                store: true\n            }\n        });\n        if (!user || !user.isActive) {\n            return null;\n        }\n        return user;\n    } catch (error) {\n        console.error(\"Auth error:\", error);\n        return null;\n    }\n}\nfunction createAuthResponse(user, token) {\n    return {\n        user: {\n            id: user.id,\n            email: user.email,\n            name: user.name,\n            role: user.role,\n            storeId: user.storeId,\n            store: user.store\n        },\n        token\n    };\n}\n// Permission-based middleware helper\nasync function checkPermission(request, module, permission) {\n    const user = await getAuthUser(request);\n    if (!user) {\n        return {\n            error: \"Unauthorized\",\n            status: 401\n        };\n    }\n    // Import permissions dynamically to avoid circular dependency\n    const { hasPermission } = await __webpack_require__.e(/*! import() */ \"_rsc_lib_permissions_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./permissions */ \"(rsc)/./lib/permissions.ts\"));\n    if (!hasPermission(user.role, module, permission)) {\n        return {\n            error: \"Insufficient permissions\",\n            status: 403\n        };\n    }\n    return {\n        user,\n        error: null\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2dyb2NlcnktbWFuYWdlbWVudC1zeXN0ZW0vLi9saWIvcHJpc21hLnRzPzk4MjIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsales%2Froute&page=%2Fapi%2Fsales%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsales%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();