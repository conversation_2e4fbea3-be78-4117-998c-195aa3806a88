import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  getPaginationParams,
  buildSearchFilter,
  createPaginatedResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const alertRuleSchema = z.object({
  name: z.string().min(1, 'Alert name is required'),
  description: z.string().optional(),
  alertType: z.enum(['INVENTORY', 'SALES', 'FINANCIAL', 'CUSTOMER', 'SYSTEM', 'SECURITY']),
  condition: z.object({
    metric: z.string().min(1, 'Metric is required'),
    operator: z.enum(['EQUALS', 'NOT_EQUALS', 'GREATER_THAN', 'LESS_THAN', 'GREATER_EQUAL', 'LESS_EQUAL', 'CONTAINS', 'NOT_CONTAINS']),
    value: z.union([z.string(), z.number()]),
    timeframe: z.enum(['REAL_TIME', 'HOURLY', 'DAILY', 'WEEKLY', 'MONTHLY']).default('REAL_TIME')
  }),
  actions: z.array(z.object({
    type: z.enum(['NOTIFICATION', 'EMAIL', 'SMS', 'WEBHOOK', 'AUTO_REORDER']),
    config: z.record(z.any())
  })),
  recipients: z.array(z.object({
    type: z.enum(['USER', 'ROLE', 'EMAIL']),
    value: z.string()
  })),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).default('MEDIUM'),
  isActive: z.boolean().default(true),
  cooldownMinutes: z.number().min(0).default(60),
  metadata: z.record(z.any()).optional()
})

// GET /api/alerts - Get all alert rules and recent alerts
export const GET = withPermission('ALERT', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { page, limit, skip, search, sortBy, sortOrder } = getPaginationParams(request)
  const { searchParams } = new URL(request.url)
  
  const alertType = searchParams.get('alertType')
  const priority = searchParams.get('priority')
  const isActive = searchParams.get('isActive')
  const includeTriggered = searchParams.get('includeTriggered') === 'true'

  // Build filters for alert rules
  const where: any = {
    storeId,
    ...buildSearchFilter(search, ['name', 'description'])
  }

  if (alertType) {
    where.alertType = alertType
  }

  if (priority) {
    where.priority = priority
  }

  if (isActive !== null) {
    where.isActive = isActive === 'true'
  }

  // Get total count
  const total = await prisma.alertRule.count({ where })

  // Get alert rules with pagination
  const alertRules = await prisma.alertRule.findMany({
    where,
    include: {
      createdBy: {
        select: {
          id: true,
          name: true
        }
      },
      _count: {
        select: {
          triggeredAlerts: true
        }
      }
    },
    orderBy: { [sortBy]: sortOrder },
    skip,
    take: limit
  })

  let response: any = {
    alertRules,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  }

  // Include recent triggered alerts if requested
  if (includeTriggered) {
    const recentAlerts = await prisma.triggeredAlert.findMany({
      where: { storeId },
      include: {
        alertRule: {
          select: {
            name: true,
            alertType: true,
            priority: true
          }
        }
      },
      orderBy: { triggeredAt: 'desc' },
      take: 50
    })

    response.recentAlerts = recentAlerts
  }

  return createSuccessResponse(response)
})

// POST /api/alerts - Create new alert rule
export const POST = withPermission('ALERT', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const data = alertRuleSchema.parse(body)

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Validate recipients
      for (const recipient of data.recipients) {
        if (recipient.type === 'USER') {
          const user = await tx.user.findFirst({
            where: { id: recipient.value, storeId, isActive: true }
          })
          if (!user) {
            throw new Error(`User not found: ${recipient.value}`)
          }
        } else if (recipient.type === 'ROLE') {
          const validRoles = ['FOUNDER', 'SUPER_ADMIN', 'ADMIN', 'STAFF', 'DISTRIBUTOR']
          if (!validRoles.includes(recipient.value)) {
            throw new Error(`Invalid role: ${recipient.value}`)
          }
        }
      }

      // Create alert rule
      const alertRule = await tx.alertRule.create({
        data: {
          name: data.name,
          description: data.description,
          alertType: data.alertType,
          condition: data.condition,
          actions: data.actions,
          recipients: data.recipients,
          priority: data.priority,
          isActive: data.isActive,
          cooldownMinutes: data.cooldownMinutes,
          metadata: data.metadata,
          createdById: user.id,
          storeId
        },
        include: {
          createdBy: {
            select: {
              id: true,
              name: true
            }
          }
        }
      })

      return alertRule
    })

    // Create audit log
    await createAuditLog(
      'CREATE',
      'ALERT_RULE',
      `Created alert rule: ${result.name} - Type: ${result.alertType}`,
      user.id,
      storeId
    )

    return createSuccessResponse(result, 'Alert rule created successfully')

  } catch (error) {
    console.error('Error creating alert rule:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to create alert rule',
      400
    )
  }
})

// POST /api/alerts/check - Manually trigger alert checks
export const PUT = withPermission('ALERT', 'UPDATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  
  const checkSchema = z.object({
    alertRuleId: z.string().optional(),
    alertType: z.string().optional()
  })

  const { alertRuleId, alertType } = checkSchema.parse(body)

  try {
    let alertRules: any[] = []

    if (alertRuleId) {
      // Check specific alert rule
      const rule = await prisma.alertRule.findFirst({
        where: { id: alertRuleId, storeId, isActive: true }
      })
      if (rule) alertRules = [rule]
    } else {
      // Check all active alert rules or by type
      const where: any = { storeId, isActive: true }
      if (alertType) where.alertType = alertType

      alertRules = await prisma.alertRule.findMany({ where })
    }

    const results = []
    for (const rule of alertRules) {
      const result = await checkAlertCondition(rule, storeId)
      results.push({
        alertRuleId: rule.id,
        alertName: rule.name,
        triggered: result.triggered,
        currentValue: result.currentValue,
        threshold: result.threshold
      })

      if (result.triggered) {
        await triggerAlert(rule, result, storeId, user.id)
      }
    }

    return createSuccessResponse({
      checkedRules: results.length,
      triggeredAlerts: results.filter(r => r.triggered).length,
      results
    }, 'Alert check completed')

  } catch (error) {
    console.error('Error checking alerts:', error)
    return createErrorResponse('Failed to check alerts', 500)
  }
})

// Helper functions for alert processing

async function checkAlertCondition(alertRule: any, storeId: string): Promise<any> {
  const { condition } = alertRule
  let currentValue: any = null
  let triggered = false

  try {
    switch (alertRule.alertType) {
      case 'INVENTORY':
        currentValue = await checkInventoryCondition(condition, storeId)
        break
      case 'SALES':
        currentValue = await checkSalesCondition(condition, storeId)
        break
      case 'FINANCIAL':
        currentValue = await checkFinancialCondition(condition, storeId)
        break
      case 'CUSTOMER':
        currentValue = await checkCustomerCondition(condition, storeId)
        break
      case 'SYSTEM':
        currentValue = await checkSystemCondition(condition, storeId)
        break
      default:
        currentValue = 0
    }

    // Evaluate condition
    triggered = evaluateCondition(currentValue, condition.operator, condition.value)

  } catch (error) {
    console.error('Error checking alert condition:', error)
    currentValue = null
  }

  return {
    triggered,
    currentValue,
    threshold: condition.value
  }
}

async function checkInventoryCondition(condition: any, storeId: string): Promise<number> {
  switch (condition.metric) {
    case 'low_stock_count':
      return await prisma.inventory.count({
        where: {
          storeId,
          quantity: { lte: prisma.inventory.fields.reorderLevel }
        }
      })
    
    case 'out_of_stock_count':
      return await prisma.inventory.count({
        where: { storeId, quantity: 0 }
      })
    
    case 'total_inventory_value':
      const inventoryItems = await prisma.inventory.findMany({
        where: { storeId },
        include: { product: { select: { costPrice: true } } }
      })
      return inventoryItems.reduce((total, item) => 
        total + (item.quantity * item.product.costPrice), 0)
    
    default:
      return 0
  }
}

async function checkSalesCondition(condition: any, storeId: string): Promise<number> {
  const timeframe = getTimeframeFilter(condition.timeframe)
  
  switch (condition.metric) {
    case 'daily_sales':
      const dailySales = await prisma.sale.aggregate({
        where: {
          storeId,
          status: 'COMPLETED',
          createdAt: timeframe
        },
        _sum: { total: true }
      })
      return dailySales._sum.total || 0
    
    case 'sales_count':
      return await prisma.sale.count({
        where: {
          storeId,
          status: 'COMPLETED',
          createdAt: timeframe
        }
      })
    
    default:
      return 0
  }
}

async function checkFinancialCondition(condition: any, storeId: string): Promise<number> {
  const timeframe = getTimeframeFilter(condition.timeframe)
  
  switch (condition.metric) {
    case 'pending_payments':
      const pendingPayments = await prisma.sale.aggregate({
        where: {
          storeId,
          paymentStatus: { in: ['PENDING', 'PARTIAL'] },
          createdAt: timeframe
        },
        _sum: { total: true }
      })
      return pendingPayments._sum.total || 0
    
    case 'expenses_total':
      const expenses = await prisma.expense.aggregate({
        where: {
          storeId,
          status: 'APPROVED',
          date: timeframe
        },
        _sum: { totalAmount: true }
      })
      return expenses._sum.totalAmount || 0
    
    default:
      return 0
  }
}

async function checkCustomerCondition(condition: any, storeId: string): Promise<number> {
  const timeframe = getTimeframeFilter(condition.timeframe)
  
  switch (condition.metric) {
    case 'new_customers':
      return await prisma.customer.count({
        where: {
          storeId,
          isActive: true,
          createdAt: timeframe
        }
      })
    
    case 'inactive_customers':
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      return await prisma.customer.count({
        where: {
          storeId,
          isActive: true,
          lastPurchaseDate: { lt: thirtyDaysAgo }
        }
      })
    
    default:
      return 0
  }
}

async function checkSystemCondition(condition: any, storeId: string): Promise<number> {
  switch (condition.metric) {
    case 'active_users':
      return await prisma.user.count({
        where: { storeId, isActive: true }
      })
    
    case 'failed_logins':
      // This would require login attempt tracking
      return 0
    
    default:
      return 0
  }
}

function evaluateCondition(currentValue: any, operator: string, threshold: any): boolean {
  switch (operator) {
    case 'EQUALS':
      return currentValue === threshold
    case 'NOT_EQUALS':
      return currentValue !== threshold
    case 'GREATER_THAN':
      return currentValue > threshold
    case 'LESS_THAN':
      return currentValue < threshold
    case 'GREATER_EQUAL':
      return currentValue >= threshold
    case 'LESS_EQUAL':
      return currentValue <= threshold
    case 'CONTAINS':
      return String(currentValue).includes(String(threshold))
    case 'NOT_CONTAINS':
      return !String(currentValue).includes(String(threshold))
    default:
      return false
  }
}

function getTimeframeFilter(timeframe: string): any {
  const now = new Date()
  
  switch (timeframe) {
    case 'HOURLY':
      return { gte: new Date(now.getTime() - 60 * 60 * 1000) }
    case 'DAILY':
      return { gte: new Date(now.getTime() - 24 * 60 * 60 * 1000) }
    case 'WEEKLY':
      return { gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) }
    case 'MONTHLY':
      return { gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) }
    default:
      return { gte: new Date(0) } // All time for REAL_TIME
  }
}

async function triggerAlert(alertRule: any, result: any, storeId: string, triggeredById: string) {
  // Check cooldown
  const lastTriggered = await prisma.triggeredAlert.findFirst({
    where: {
      alertRuleId: alertRule.id,
      triggeredAt: {
        gte: new Date(Date.now() - alertRule.cooldownMinutes * 60 * 1000)
      }
    }
  })

  if (lastTriggered) {
    return // Still in cooldown period
  }

  // Create triggered alert record
  const triggeredAlert = await prisma.triggeredAlert.create({
    data: {
      alertRuleId: alertRule.id,
      triggeredAt: new Date(),
      currentValue: result.currentValue,
      threshold: result.threshold,
      metadata: { result },
      storeId,
      triggeredById
    }
  })

  // Execute alert actions
  for (const action of alertRule.actions) {
    try {
      await executeAlertAction(action, alertRule, result, storeId)
    } catch (error) {
      console.error('Error executing alert action:', error)
    }
  }
}

async function executeAlertAction(action: any, alertRule: any, result: any, storeId: string) {
  switch (action.type) {
    case 'NOTIFICATION':
      await createAlertNotification(alertRule, result, storeId)
      break
    case 'EMAIL':
      await sendAlertEmail(alertRule, result, action.config)
      break
    case 'SMS':
      await sendAlertSMS(alertRule, result, action.config)
      break
    case 'WEBHOOK':
      await callAlertWebhook(alertRule, result, action.config)
      break
    case 'AUTO_REORDER':
      await executeAutoReorder(alertRule, result, storeId)
      break
  }
}

async function createAlertNotification(alertRule: any, result: any, storeId: string) {
  const message = `Alert: ${alertRule.name} - Current value: ${result.currentValue}, Threshold: ${result.threshold}`
  
  // Create notifications for all recipients
  for (const recipient of alertRule.recipients) {
    if (recipient.type === 'USER') {
      await prisma.notification.create({
        data: {
          title: `Alert: ${alertRule.name}`,
          message,
          type: 'ALERT',
          category: alertRule.alertType,
          priority: alertRule.priority,
          userId: recipient.value,
          storeId
        }
      })
    } else if (recipient.type === 'ROLE') {
      // Create notifications for all users with this role
      const users = await prisma.user.findMany({
        where: { storeId, role: recipient.value, isActive: true },
        select: { id: true }
      })
      
      for (const user of users) {
        await prisma.notification.create({
          data: {
            title: `Alert: ${alertRule.name}`,
            message,
            type: 'ALERT',
            category: alertRule.alertType,
            priority: alertRule.priority,
            userId: user.id,
            storeId
          }
        })
      }
    }
  }
}

async function sendAlertEmail(alertRule: any, result: any, config: any) {
  // Email sending logic
  console.log('Sending alert email:', alertRule.name)
}

async function sendAlertSMS(alertRule: any, result: any, config: any) {
  // SMS sending logic
  console.log('Sending alert SMS:', alertRule.name)
}

async function callAlertWebhook(alertRule: any, result: any, config: any) {
  // Webhook calling logic
  console.log('Calling alert webhook:', alertRule.name)
}

async function executeAutoReorder(alertRule: any, result: any, storeId: string) {
  // Auto reorder logic for inventory alerts
  console.log('Executing auto reorder:', alertRule.name)
}
