import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// GET /api/purchases/[id]/returns - Get purchase returns
export const GET = withPermission('PURCHASE_RETURN', 'READ', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const purchaseId = pathSegments[pathSegments.length - 2] // Get purchase ID from path

  // Check if purchase exists and belongs to store
  const purchase = await prisma.purchase.findFirst({
    where: {
      id: purchaseId,
      storeId
    }
  })

  if (!purchase) {
    return createErrorResponse('Purchase not found', 404)
  }

  const returns = await prisma.purchaseReturn.findMany({
    where: {
      purchaseId
    },
    include: {
      items: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
              unit: true
            }
          }
        }
      },
      createdBy: {
        select: {
          id: true,
          name: true
        }
      }
    },
    orderBy: { createdAt: 'desc' }
  })

  return createSuccessResponse(returns)
})

// POST /api/purchases/[id]/returns - Create purchase return
export const POST = withPermission('PURCHASE_RETURN', 'CREATE', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const purchaseId = pathSegments[pathSegments.length - 2] // Get purchase ID from path
  const body = await request.json()

  const returnSchema = z.object({
    reason: z.enum(['DAMAGED', 'EXPIRED', 'WRONG_ITEM', 'QUALITY_ISSUE', 'EXCESS_QUANTITY', 'OTHER']),
    notes: z.string().optional(),
    items: z.array(z.object({
      productId: z.string().min(1, 'Product is required'),
      quantity: z.number().min(1, 'Quantity must be at least 1'),
      unitPrice: z.number().min(0, 'Unit price must be positive'),
      reason: z.string().optional()
    })).min(1, 'At least one item is required')
  })

  const { reason, notes, items } = returnSchema.parse(body)

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Check if purchase exists and belongs to store
      const purchase = await tx.purchase.findFirst({
        where: {
          id: purchaseId,
          storeId
        },
        include: {
          items: true,
          supplier: true
        }
      })

      if (!purchase) {
        throw new Error('Purchase not found')
      }

      if (purchase.status !== 'RECEIVED' && purchase.status !== 'COMPLETED') {
        throw new Error('Can only return items from received purchases')
      }

      // Validate return items against purchase items
      for (const returnItem of items) {
        const purchaseItem = purchase.items.find(pi => pi.productId === returnItem.productId)
        if (!purchaseItem) {
          throw new Error(`Product ${returnItem.productId} was not in the original purchase`)
        }

        // Check if return quantity is valid
        const existingReturns = await tx.purchaseReturnItem.aggregate({
          where: {
            productId: returnItem.productId,
            purchaseReturn: {
              purchaseId
            }
          },
          _sum: { quantity: true }
        })

        const totalReturned = existingReturns._sum.quantity || 0
        const availableToReturn = purchaseItem.quantity - totalReturned

        if (returnItem.quantity > availableToReturn) {
          throw new Error(`Cannot return ${returnItem.quantity} units of product. Only ${availableToReturn} units available for return`)
        }
      }

      // Generate return number
      const returnCount = await tx.purchaseReturn.count({ where: { storeId } })
      const returnNo = `PR-${String(returnCount + 1).padStart(6, '0')}`

      // Calculate totals
      let totalAmount = 0
      const returnItems = items.map(item => {
        const itemTotal = item.quantity * item.unitPrice
        totalAmount += itemTotal

        return {
          productId: item.productId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalPrice: itemTotal,
          reason: item.reason
        }
      })

      // Create purchase return
      const purchaseReturn = await tx.purchaseReturn.create({
        data: {
          returnNo,
          purchaseId,
          reason,
          notes,
          totalAmount,
          status: 'PENDING',
          createdById: user.id,
          storeId,
          items: {
            create: returnItems
          }
        },
        include: {
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                  unit: true
                }
              }
            }
          },
          purchase: {
            select: {
              purchaseNo: true,
              supplier: {
                select: {
                  name: true,
                  email: true
                }
              }
            }
          },
          createdBy: {
            select: {
              id: true,
              name: true
            }
          }
        }
      })

      // Update inventory (reduce quantities for returned items)
      for (const item of items) {
        const inventory = await tx.inventory.findFirst({
          where: {
            productId: item.productId,
            storeId
          }
        })

        if (inventory) {
          await tx.inventory.update({
            where: { id: inventory.id },
            data: {
              quantity: Math.max(0, inventory.quantity - item.quantity)
            }
          })

          // Create inventory adjustment record
          await tx.inventoryAdjustment.create({
            data: {
              productId: item.productId,
              storeId,
              adjustmentType: 'OUT',
              quantityChanged: -item.quantity,
              reason: `Purchase return: ${returnNo}`,
              createdById: user.id
            }
          })
        }
      }

      // Create notification for supplier (if email exists)
      if (purchase.supplier.email) {
        await tx.notification.create({
          data: {
            title: `Purchase Return Created: ${returnNo}`,
            message: `A return has been created for purchase ${purchase.purchaseNo}. Return amount: ₹${totalAmount}`,
            type: 'WARNING',
            recipientEmail: purchase.supplier.email,
            storeId
          }
        })
      }

      return purchaseReturn
    })

    // Create audit log
    await createAuditLog(
      'CREATE',
      'PURCHASE_RETURN',
      `Created purchase return: ${result.returnNo} for purchase: ${result.purchase.purchaseNo} - Amount: ₹${result.totalAmount}`,
      user.id,
      storeId
    )

    return createSuccessResponse(result, 'Purchase return created successfully')

  } catch (error) {
    console.error('Error creating purchase return:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to create purchase return',
      400
    )
  }
})
