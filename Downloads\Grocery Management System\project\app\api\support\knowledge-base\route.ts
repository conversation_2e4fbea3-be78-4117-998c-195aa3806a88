import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  getPaginationParams,
  buildSearchFilter,
  createPaginatedResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const articleSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  content: z.string().min(1, 'Content is required'),
  summary: z.string().optional(),
  category: z.enum(['GETTING_STARTED', 'FEATURES', 'TROUBLESHOOTING', 'BILLING', 'INTEGRATIONS', 'API', 'BEST_PRACTICES']),
  subcategory: z.string().optional(),
  tags: z.array(z.string()).optional(),
  difficulty: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED']).default('BEGINNER'),
  estimatedReadTime: z.number().min(1).optional(),
  relatedArticles: z.array(z.string()).optional(),
  attachments: z.array(z.object({
    fileName: z.string(),
    fileUrl: z.string(),
    fileSize: z.number(),
    mimeType: z.string()
  })).optional(),
  isPublic: z.boolean().default(true),
  isFeatured: z.boolean().default(false),
  metadata: z.record(z.any()).optional()
})

const faqSchema = z.object({
  question: z.string().min(1, 'Question is required'),
  answer: z.string().min(1, 'Answer is required'),
  category: z.string().min(1, 'Category is required'),
  tags: z.array(z.string()).optional(),
  isPublic: z.boolean().default(true),
  sortOrder: z.number().default(0),
  relatedQuestions: z.array(z.string()).optional()
})

// GET /api/support/knowledge-base - Get knowledge base articles and FAQs
export const GET = async (request: NextRequest) => {
  const { page, limit, skip, search, sortBy, sortOrder } = getPaginationParams(request)
  const { searchParams } = new URL(request.url)
  
  const type = searchParams.get('type') || 'articles' // articles, faqs, or both
  const category = searchParams.get('category')
  const difficulty = searchParams.get('difficulty')
  const featured = searchParams.get('featured') === 'true'
  const publicOnly = searchParams.get('public') !== 'false' // Default to true
  const includeAnalytics = searchParams.get('analytics') === 'true'

  try {
    let response: any = {}

    // Get articles if requested
    if (type === 'articles' || type === 'both') {
      const articleWhere: any = {
        ...buildSearchFilter(search, ['title', 'content', 'summary'])
      }

      if (category) {
        articleWhere.category = category
      }

      if (difficulty) {
        articleWhere.difficulty = difficulty
      }

      if (featured) {
        articleWhere.isFeatured = true
      }

      if (publicOnly) {
        articleWhere.isPublic = true
      }

      const totalArticles = await prisma.knowledgeBaseArticle.count({ where: articleWhere })

      const articles = await prisma.knowledgeBaseArticle.findMany({
        where: articleWhere,
        include: {
          author: {
            select: {
              id: true,
              name: true
            }
          },
          _count: {
            select: {
              views: true,
              votes: true
            }
          }
        },
        orderBy: featured ? [
          { isFeatured: 'desc' },
          { [sortBy]: sortOrder }
        ] : { [sortBy]: sortOrder },
        skip: type === 'articles' ? skip : 0,
        take: type === 'articles' ? limit : undefined
      })

      // Add calculated fields
      const articlesWithCalculations = await Promise.all(
        articles.map(async (article) => {
          const [upvotes, downvotes, userViewed] = await Promise.all([
            prisma.knowledgeBaseVote.count({
              where: { articleId: article.id, voteType: 'UP' }
            }),
            prisma.knowledgeBaseVote.count({
              where: { articleId: article.id, voteType: 'DOWN' }
            }),
            // For logged-in users, check if they've viewed this article
            Promise.resolve(false) // Would check user session
          ])

          return {
            ...article,
            viewCount: article._count.views,
            voteCount: article._count.votes,
            upvotes,
            downvotes,
            helpfulnessRatio: article._count.votes > 0 ? (upvotes / article._count.votes) * 100 : 0,
            readTime: article.estimatedReadTime || estimateReadTime(article.content),
            lastUpdated: getTimeAgo(article.updatedAt),
            userViewed
          }
        })
      )

      response.articles = {
        data: articlesWithCalculations,
        pagination: type === 'articles' ? {
          page,
          limit,
          total: totalArticles,
          pages: Math.ceil(totalArticles / limit)
        } : undefined
      }
    }

    // Get FAQs if requested
    if (type === 'faqs' || type === 'both') {
      const faqWhere: any = {
        ...buildSearchFilter(search, ['question', 'answer'])
      }

      if (category) {
        faqWhere.category = category
      }

      if (publicOnly) {
        faqWhere.isPublic = true
      }

      const totalFaqs = await prisma.faq.count({ where: faqWhere })

      const faqs = await prisma.faq.findMany({
        where: faqWhere,
        include: {
          author: {
            select: {
              id: true,
              name: true
            }
          },
          _count: {
            select: {
              votes: true
            }
          }
        },
        orderBy: [
          { sortOrder: 'asc' },
          { [sortBy]: sortOrder }
        ],
        skip: type === 'faqs' ? skip : 0,
        take: type === 'faqs' ? limit : undefined
      })

      // Add calculated fields for FAQs
      const faqsWithCalculations = await Promise.all(
        faqs.map(async (faq) => {
          const [upvotes, downvotes] = await Promise.all([
            prisma.faqVote.count({
              where: { faqId: faq.id, voteType: 'UP' }
            }),
            prisma.faqVote.count({
              where: { faqId: faq.id, voteType: 'DOWN' }
            })
          ])

          return {
            ...faq,
            voteCount: faq._count.votes,
            upvotes,
            downvotes,
            helpfulnessRatio: faq._count.votes > 0 ? (upvotes / faq._count.votes) * 100 : 0,
            lastUpdated: getTimeAgo(faq.updatedAt)
          }
        })
      )

      response.faqs = {
        data: faqsWithCalculations,
        pagination: type === 'faqs' ? {
          page,
          limit,
          total: totalFaqs,
          pages: Math.ceil(totalFaqs / limit)
        } : undefined
      }
    }

    // Include analytics if requested
    if (includeAnalytics) {
      response.analytics = await getKnowledgeBaseAnalytics()
    }

    // Get categories and popular content
    response.categories = await getKnowledgeBaseCategories()
    response.popular = await getPopularContent()

    return createSuccessResponse(response)

  } catch (error) {
    console.error('Error fetching knowledge base:', error)
    return createErrorResponse('Failed to fetch knowledge base', 500)
  }
}

// POST /api/support/knowledge-base - Create new article or FAQ
export const POST = withPermission('SUPPORT', 'CREATE', async (request: NextRequest, user: any) => {
  const body = await request.json()
  const contentType = body.contentType || 'article' // article or faq

  // Only admins can create knowledge base content
  if (!['FOUNDER', 'SUPER_ADMIN', 'ADMIN'].includes(user.role)) {
    return createErrorResponse('Insufficient permissions to create knowledge base content', 403)
  }

  try {
    let result: any

    if (contentType === 'article') {
      const data = articleSchema.parse(body)
      
      result = await prisma.knowledgeBaseArticle.create({
        data: {
          title: data.title,
          content: data.content,
          summary: data.summary,
          category: data.category,
          subcategory: data.subcategory,
          tags: data.tags,
          difficulty: data.difficulty,
          estimatedReadTime: data.estimatedReadTime || estimateReadTime(data.content),
          relatedArticles: data.relatedArticles,
          attachments: data.attachments,
          isPublic: data.isPublic,
          isFeatured: data.isFeatured,
          metadata: data.metadata,
          authorId: user.id
        },
        include: {
          author: {
            select: {
              id: true,
              name: true
            }
          }
        }
      })

      // Create audit log
      await createAuditLog(
        'CREATE',
        'KNOWLEDGE_BASE_ARTICLE',
        `Created knowledge base article: ${result.title}`,
        user.id,
        'system'
      )

    } else if (contentType === 'faq') {
      const data = faqSchema.parse(body)
      
      result = await prisma.faq.create({
        data: {
          question: data.question,
          answer: data.answer,
          category: data.category,
          tags: data.tags,
          isPublic: data.isPublic,
          sortOrder: data.sortOrder,
          relatedQuestions: data.relatedQuestions,
          authorId: user.id
        },
        include: {
          author: {
            select: {
              id: true,
              name: true
            }
          }
        }
      })

      // Create audit log
      await createAuditLog(
        'CREATE',
        'FAQ',
        `Created FAQ: ${result.question}`,
        user.id,
        'system'
      )

    } else {
      throw new Error('Invalid content type')
    }

    return createSuccessResponse(result, `${contentType} created successfully`)

  } catch (error) {
    console.error(`Error creating ${contentType}:`, error)
    return createErrorResponse(
      error instanceof Error ? error.message : `Failed to create ${contentType}`,
      400
    )
  }
})

// PUT /api/support/knowledge-base/[id]/vote - Vote on article or FAQ
export const PUT = withPermission('USER', 'READ', async (request: NextRequest, user: any) => {
  const url = new URL(request.url)
  const pathParts = url.pathname.split('/')
  const contentId = pathParts[pathParts.length - 2] // Get content ID from path
  const body = await request.json()

  const voteSchema = z.object({
    voteType: z.enum(['UP', 'DOWN']),
    contentType: z.enum(['article', 'faq'])
  })

  const { voteType, contentType } = voteSchema.parse(body)

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Verify content exists
      let content: any
      if (contentType === 'article') {
        content = await tx.knowledgeBaseArticle.findUnique({
          where: { id: contentId }
        })
      } else {
        content = await tx.faq.findUnique({
          where: { id: contentId }
        })
      }

      if (!content) {
        throw new Error(`${contentType} not found`)
      }

      // Check for existing vote
      const voteTable = contentType === 'article' ? 'knowledgeBaseVote' : 'faqVote'
      const voteField = contentType === 'article' ? 'articleId' : 'faqId'
      
      const existingVote = await (tx as any)[voteTable].findFirst({
        where: {
          [voteField]: contentId,
          userId: user.id
        }
      })

      if (existingVote) {
        if (existingVote.voteType === voteType) {
          // Remove vote if same type
          await (tx as any)[voteTable].delete({
            where: { id: existingVote.id }
          })
          return { action: 'removed', voteType: null }
        } else {
          // Update vote type
          await (tx as any)[voteTable].update({
            where: { id: existingVote.id },
            data: { voteType }
          })
          return { action: 'updated', voteType }
        }
      } else {
        // Create new vote
        await (tx as any)[voteTable].create({
          data: {
            [voteField]: contentId,
            userId: user.id,
            voteType
          }
        })
        return { action: 'created', voteType }
      }
    })

    return createSuccessResponse(result, 'Vote recorded successfully')

  } catch (error) {
    console.error('Error recording vote:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to record vote',
      400
    )
  }
})

// Helper functions

async function getKnowledgeBaseAnalytics() {
  const [
    totalArticles,
    totalFaqs,
    totalViews,
    topArticles,
    topFaqs,
    categoryStats
  ] = await Promise.all([
    // Total articles
    prisma.knowledgeBaseArticle.count({ where: { isPublic: true } }),

    // Total FAQs
    prisma.faq.count({ where: { isPublic: true } }),

    // Total views
    prisma.knowledgeBaseView.count(),

    // Top articles by views
    prisma.knowledgeBaseArticle.findMany({
      where: { isPublic: true },
      include: {
        _count: {
          select: { views: true }
        }
      },
      orderBy: {
        views: {
          _count: 'desc'
        }
      },
      take: 5
    }),

    // Top FAQs by votes
    prisma.faq.findMany({
      where: { isPublic: true },
      include: {
        _count: {
          select: { votes: true }
        }
      },
      orderBy: {
        votes: {
          _count: 'desc'
        }
      },
      take: 5
    }),

    // Category statistics
    prisma.knowledgeBaseArticle.groupBy({
      by: ['category'],
      where: { isPublic: true },
      _count: true
    })
  ])

  return {
    summary: {
      totalArticles,
      totalFaqs,
      totalViews
    },
    topContent: {
      articles: topArticles.map(article => ({
        id: article.id,
        title: article.title,
        views: article._count.views
      })),
      faqs: topFaqs.map(faq => ({
        id: faq.id,
        question: faq.question,
        votes: faq._count.votes
      }))
    },
    categoryStats: categoryStats.map(stat => ({
      category: stat.category,
      count: stat._count
    }))
  }
}

async function getKnowledgeBaseCategories() {
  const [articleCategories, faqCategories] = await Promise.all([
    prisma.knowledgeBaseArticle.groupBy({
      by: ['category'],
      where: { isPublic: true },
      _count: true
    }),
    
    prisma.faq.groupBy({
      by: ['category'],
      where: { isPublic: true },
      _count: true
    })
  ])

  return {
    articles: articleCategories.map(cat => ({
      category: cat.category,
      count: cat._count
    })),
    faqs: faqCategories.map(cat => ({
      category: cat.category,
      count: cat._count
    }))
  }
}

async function getPopularContent() {
  const [popularArticles, popularFaqs] = await Promise.all([
    // Popular articles (by views in last 30 days)
    prisma.knowledgeBaseArticle.findMany({
      where: {
        isPublic: true,
        views: {
          some: {
            createdAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            }
          }
        }
      },
      include: {
        _count: {
          select: { views: true }
        }
      },
      orderBy: {
        views: {
          _count: 'desc'
        }
      },
      take: 5
    }),

    // Popular FAQs (by votes)
    prisma.faq.findMany({
      where: { isPublic: true },
      include: {
        _count: {
          select: { votes: true }
        }
      },
      orderBy: {
        votes: {
          _count: 'desc'
        }
      },
      take: 5
    })
  ])

  return {
    articles: popularArticles.map(article => ({
      id: article.id,
      title: article.title,
      category: article.category,
      views: article._count.views
    })),
    faqs: popularFaqs.map(faq => ({
      id: faq.id,
      question: faq.question,
      category: faq.category,
      votes: faq._count.votes
    }))
  }
}

function estimateReadTime(content: string): number {
  // Average reading speed: 200 words per minute
  const wordsPerMinute = 200
  const wordCount = content.split(/\s+/).length
  return Math.max(1, Math.ceil(wordCount / wordsPerMinute))
}

function getTimeAgo(date: Date): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) return 'Just now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`
  return date.toLocaleDateString()
}
