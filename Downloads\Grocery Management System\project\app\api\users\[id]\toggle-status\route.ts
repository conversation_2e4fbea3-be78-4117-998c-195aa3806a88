import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'

// POST /api/users/[id]/toggle-status - Toggle user active status
export const POST = withPermission('USER', 'UPDATE', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const id = pathSegments[pathSegments.length - 2] // Get id from path (before toggle-status)

  // Check if user exists
  const existingUser = await prisma.user.findFirst({
    where: {
      id,
      storeId
    }
  })

  if (!existingUser) {
    return createErrorResponse('User not found', 404)
  }

  // Prevent deactivating yourself
  if (existingUser.id === user.id) {
    return createErrorResponse('Cannot deactivate your own account', 400)
  }

  // Prevent deactivating founder
  if (existingUser.role === 'FOUNDER') {
    return createErrorResponse('Cannot deactivate founder account', 400)
  }

  // Toggle status
  const updatedUser = await prisma.user.update({
    where: { id },
    data: { isActive: !existingUser.isActive },
    select: {
      id: true,
      name: true,
      email: true,
      isActive: true
    }
  })

  // Create audit log
  await createAuditLog(
    updatedUser.isActive ? 'ACTIVATE' : 'DEACTIVATE',
    'USER',
    `${updatedUser.isActive ? 'Activated' : 'Deactivated'} user: ${updatedUser.name} (${updatedUser.email})`,
    user.id,
    storeId
  )

  return createSuccessResponse(updatedUser, `User ${updatedUser.isActive ? 'activated' : 'deactivated'} successfully`)
})
