export type Role =
  | 'FOUNDER'
  | 'SUPER_ADMIN'
  | 'ADMIN'
  | 'STAFF'
  | 'DISTRIBUTOR'

export type Module =
  | 'USER'
  | 'ROLE'
  | 'STORE'
  | 'PRODUCT'
  | 'CATEGORY'
  | 'INVENTORY'
  | 'PURCHASE'
  | 'PURCHASE_RETURN'
  | 'SALES'
  | 'SALES_RETURN'
  | 'CUSTOMER'
  | 'DISTRIBUTOR'
  | 'EXPENSE'
  | 'SUPPLIER'
  | 'PAYMENT'
  | 'B2B'
  | 'BILLING'
  | 'TAX'
  | 'NOTIFICATION'
  | 'DOCUMENT'
  | 'SETTINGS'
  | 'REPORTS'
  | 'DASHBOARD'
  | 'AUDIT_LOG'
  | 'SUBSCRIPTION'
  | 'FEEDBACK'
  | 'SUPPORT'

export type Permission =
  | 'CREATE'
  | 'READ'
  | 'UPDATE'
  | 'DELETE'
  | 'EXPORT'
  | 'IMPORT'
  | 'ASSIGN'
  | 'APPROVE'
  | 'REJECT'
  | 'MANAGE'
  | 'ACCESS_SETTINGS'
  | 'UPLOAD'
  | 'DOWNLOAD'
  | 'PRINT'
  | 'ARCHIVE'
  | 'RESTORE'

export interface RolePermission {
  role: Role
  module: Module
  permissions: Permission[]
}

export interface DashboardStats {
  totalSales: number
  totalPurchases: number
  totalCustomers: number
  totalProducts: number
  lowStockItems: number
  todaySales: number
  thisMonthSales: number
  pendingOrders: number
}

export interface SalesData {
  date: string
  sales: number
  purchases: number
}

export interface TopProduct {
  id: string
  name: string
  sales: number
  revenue: number
}

export interface RecentActivity {
  id: string
  type: 'sale' | 'purchase' | 'return' | 'expense'
  description: string
  amount: number
  timestamp: Date
}