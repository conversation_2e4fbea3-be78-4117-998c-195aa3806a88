'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { DashboardStats } from '@/lib/types'
import {
  TrendingUp,
  Users,
  Package,
  AlertTriangle,
  DollarSign,
  ShoppingCart,
  Calendar,
  Clock
} from 'lucide-react'

interface StatsCardsProps {
  stats: DashboardStats
}

export function StatsCards({ stats }: StatsCardsProps) {
  const cards = [
    {
      title: "Today's Sales",
      value: `₹${stats.todaySales.toLocaleString()}`,
      description: "Sales amount today",
      icon: DollarSign,
      trend: "+12%",
      color: "text-green-600"
    },
    {
      title: "This Month",
      value: `₹${stats.thisMonthSales.toLocaleString()}`,
      description: "Monthly sales",
      icon: Calendar,
      trend: "+8%",
      color: "text-blue-600"
    },
    {
      title: "Total Sales",
      value: stats.totalSales.toLocaleString(),
      description: "Total completed sales",
      icon: ShoppingCart,
      trend: "+23%",
      color: "text-purple-600"
    },
    {
      title: "Total Customers",
      value: stats.totalCustomers.toLocaleString(),
      description: "Registered customers",
      icon: Users,
      trend: "+5%",
      color: "text-indigo-600"
    },
    {
      title: "Total Products",
      value: stats.totalProducts.toLocaleString(),
      description: "Active products",
      icon: Package,
      trend: "+2%",
      color: "text-teal-600"
    },
    {
      title: "Low Stock Alert",
      value: stats.lowStockItems.toLocaleString(),
      description: "Items below reorder level",
      icon: AlertTriangle,
      trend: "-3",
      color: "text-orange-600"
    },
    {
      title: "Pending Orders",
      value: stats.pendingOrders.toLocaleString(),
      description: "Orders awaiting approval",
      icon: Clock,
      trend: "+1",
      color: "text-red-600"
    },
    {
      title: "Total Purchases",
      value: stats.totalPurchases.toLocaleString(),
      description: "Purchase orders placed",
      icon: TrendingUp,
      trend: "+4%",
      color: "text-cyan-600"
    }
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {cards.map((card) => (
        <Card key={card.title} className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {card.title}
            </CardTitle>
            <card.icon className={`h-4 w-4 ${card.color}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{card.value}</div>
            <div className="flex items-center space-x-1 text-xs text-muted-foreground">
              <span>{card.description}</span>
              <span className={`font-medium ${
                card.trend.startsWith('+') ? 'text-green-600' : 
                card.trend.startsWith('-') ? 'text-red-600' : 
                'text-gray-600'
              }`}>
                {card.trend}
              </span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}