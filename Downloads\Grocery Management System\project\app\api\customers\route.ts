import { NextRequest, NextResponse } from 'next/server'
import { 
  withPermission, 
  createSuccessResponse, 
  createErrorResponse,
  getPaginationParams,
  buildSearchFilter,
  createPaginatedResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const customerSchema = z.object({
  name: z.string().min(1, 'Customer name is required'),
  phone: z.string().optional(),
  email: z.string().email().optional().or(z.literal('')),
  address: z.string().optional(),
  gstNumber: z.string().optional(),
  creditLimit: z.number().min(0).default(0),
  isActive: z.boolean().default(true)
})

// GET /api/customers - Get all customers with pagination and filters
export const GET = withPermission('CUSTOMER', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { page, limit, skip, search, sortBy, sortOrder } = getPaginationParams(request)

  // Build filters
  const where: any = { 
    storeId,
    ...buildSearchFilter(search, ['name', 'phone', 'email', 'gstNumber'])
  }

  // Get total count
  const total = await prisma.customer.count({ where })

  // Get customers with pagination
  const customers = await prisma.customer.findMany({
    where,
    include: {
      _count: {
        select: {
          sales: true,
          b2bOrders: true
        }
      }
    },
    orderBy: { [sortBy]: sortOrder },
    skip,
    take: limit
  })

  return createPaginatedResponse(customers, page, limit, total)
})

// POST /api/customers - Create new customer
export const POST = withPermission('CUSTOMER', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const data = customerSchema.parse(body)

  // Check if phone already exists (if provided)
  if (data.phone) {
    const existingCustomer = await prisma.customer.findFirst({
      where: {
        phone: data.phone,
        storeId,
        isActive: true
      }
    })

    if (existingCustomer) {
      return createErrorResponse('Customer with this phone number already exists', 400)
    }
  }

  // Check if email already exists (if provided)
  if (data.email) {
    const existingCustomer = await prisma.customer.findFirst({
      where: {
        email: data.email,
        storeId,
        isActive: true
      }
    })

    if (existingCustomer) {
      return createErrorResponse('Customer with this email already exists', 400)
    }
  }

  const customer = await prisma.customer.create({
    data: {
      ...data,
      storeId
    },
    include: {
      _count: {
        select: {
          sales: true,
          b2bOrders: true
        }
      }
    }
  })

  // Create audit log
  await createAuditLog(
    'CREATE',
    'CUSTOMER',
    `Created customer: ${customer.name}`,
    user.id,
    storeId
  )

  return createSuccessResponse(customer, 'Customer created successfully')
})
