import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'

// GET /api/inventory/alerts - Get stock alerts and notifications
export const GET = withPermission('INVENTORY', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { searchParams } = new URL(request.url)
  const alertType = searchParams.get('type') || 'all' // all, low_stock, out_of_stock, expiring
  const severity = searchParams.get('severity') || 'all' // all, high, medium, low
  const limit = parseInt(searchParams.get('limit') || '50')

  try {
    const alerts = await generateStockAlerts(storeId, alertType, severity, limit)
    
    // Create notifications for critical alerts if they don't exist
    await createAlertNotifications(storeId, alerts.filter(alert => alert.severity === 'high'))

    return createSuccessResponse({
      alerts,
      summary: {
        total: alerts.length,
        high: alerts.filter(a => a.severity === 'high').length,
        medium: alerts.filter(a => a.severity === 'medium').length,
        low: alerts.filter(a => a.severity === 'low').length
      }
    }, 'Stock alerts retrieved successfully')

  } catch (error) {
    console.error('Error fetching stock alerts:', error)
    return createErrorResponse('Failed to fetch stock alerts', 500)
  }
})

// POST /api/inventory/alerts - Mark alerts as acknowledged
export const POST = withPermission('INVENTORY', 'UPDATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const { alertIds, action } = body // action: 'acknowledge', 'dismiss'

  if (!alertIds || !Array.isArray(alertIds)) {
    return createErrorResponse('Alert IDs are required', 400)
  }

  try {
    // For now, we'll just return success since we don't have a persistent alert table
    // In a real implementation, you'd update alert status in database
    
    return createSuccessResponse({
      acknowledgedCount: alertIds.length,
      action
    }, `${alertIds.length} alerts ${action}d successfully`)

  } catch (error) {
    console.error('Error updating alerts:', error)
    return createErrorResponse('Failed to update alerts', 500)
  }
})

async function generateStockAlerts(storeId: string, alertType: string, severity: string, limit: number) {
  const alerts: any[] = []

  // Low Stock Alerts
  if (alertType === 'all' || alertType === 'low_stock') {
    const lowStockItems = await prisma.inventory.findMany({
      where: {
        storeId,
        quantity: { lte: prisma.inventory.fields.reorderLevel },
        quantity: { gt: 0 }
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
            minStock: true,
            category: {
              select: {
                name: true
              }
            }
          }
        }
      },
      orderBy: { quantity: 'asc' }
    })

    lowStockItems.forEach(item => {
      const stockRatio = item.quantity / item.reorderLevel
      const alertSeverity = stockRatio <= 0.3 ? 'high' : stockRatio <= 0.6 ? 'medium' : 'low'
      
      if (severity === 'all' || severity === alertSeverity) {
        alerts.push({
          id: `low_stock_${item.productId}`,
          type: 'low_stock',
          severity: alertSeverity,
          title: `Low Stock: ${item.product.name}`,
          message: `Only ${item.quantity} units remaining (Reorder level: ${item.reorderLevel})`,
          productId: item.product.id,
          productName: item.product.name,
          sku: item.product.sku,
          category: item.product.category.name,
          currentQuantity: item.quantity,
          reorderLevel: item.reorderLevel,
          minStock: item.product.minStock,
          createdAt: new Date(),
          actionRequired: true,
          suggestedAction: 'Create purchase order'
        })
      }
    })
  }

  // Out of Stock Alerts
  if (alertType === 'all' || alertType === 'out_of_stock') {
    const outOfStockItems = await prisma.inventory.findMany({
      where: {
        storeId,
        quantity: 0
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
            category: {
              select: {
                name: true
              }
            }
          }
        }
      },
      orderBy: { lastUpdated: 'desc' }
    })

    outOfStockItems.forEach(item => {
      if (severity === 'all' || severity === 'high') {
        alerts.push({
          id: `out_of_stock_${item.productId}`,
          type: 'out_of_stock',
          severity: 'high',
          title: `Out of Stock: ${item.product.name}`,
          message: `Product is completely out of stock`,
          productId: item.product.id,
          productName: item.product.name,
          sku: item.product.sku,
          category: item.product.category.name,
          currentQuantity: 0,
          lastUpdated: item.lastUpdated,
          createdAt: new Date(),
          actionRequired: true,
          suggestedAction: 'Urgent restocking required'
        })
      }
    })
  }

  // Overstock Alerts (items with very high quantities)
  if (alertType === 'all' || alertType === 'overstock') {
    const overstockItems = await prisma.inventory.findMany({
      where: {
        storeId,
        quantity: { gte: 100 } // Configurable threshold
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
            minStock: true,
            category: {
              select: {
                name: true
              }
            }
          }
        }
      }
    })

    overstockItems.forEach(item => {
      const overstockRatio = item.quantity / (item.product.minStock || 1)
      if (overstockRatio > 10) { // More than 10x the minimum stock
        const alertSeverity = overstockRatio > 20 ? 'medium' : 'low'
        
        if (severity === 'all' || severity === alertSeverity) {
          alerts.push({
            id: `overstock_${item.productId}`,
            type: 'overstock',
            severity: alertSeverity,
            title: `Overstock: ${item.product.name}`,
            message: `High inventory level: ${item.quantity} units (${overstockRatio.toFixed(1)}x minimum)`,
            productId: item.product.id,
            productName: item.product.name,
            sku: item.product.sku,
            category: item.product.category.name,
            currentQuantity: item.quantity,
            minStock: item.product.minStock,
            overstockRatio: overstockRatio,
            createdAt: new Date(),
            actionRequired: false,
            suggestedAction: 'Consider promotional pricing'
          })
        }
      }
    })
  }

  // Slow Moving Items (items that haven't moved in a while)
  if (alertType === 'all' || alertType === 'slow_moving') {
    const slowMovingThreshold = new Date()
    slowMovingThreshold.setDate(slowMovingThreshold.getDate() - 30) // 30 days

    const slowMovingItems = await prisma.inventory.findMany({
      where: {
        storeId,
        lastUpdated: { lte: slowMovingThreshold },
        quantity: { gt: 0 }
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
            category: {
              select: {
                name: true
              }
            }
          }
        }
      },
      take: 20
    })

    slowMovingItems.forEach(item => {
      const daysSinceUpdate = Math.floor((Date.now() - item.lastUpdated.getTime()) / (1000 * 60 * 60 * 24))
      const alertSeverity = daysSinceUpdate > 60 ? 'medium' : 'low'
      
      if (severity === 'all' || severity === alertSeverity) {
        alerts.push({
          id: `slow_moving_${item.productId}`,
          type: 'slow_moving',
          severity: alertSeverity,
          title: `Slow Moving: ${item.product.name}`,
          message: `No movement for ${daysSinceUpdate} days`,
          productId: item.product.id,
          productName: item.product.name,
          sku: item.product.sku,
          category: item.product.category.name,
          currentQuantity: item.quantity,
          daysSinceUpdate: daysSinceUpdate,
          lastUpdated: item.lastUpdated,
          createdAt: new Date(),
          actionRequired: false,
          suggestedAction: 'Review pricing or promotion'
        })
      }
    })
  }

  // Sort by severity and limit results
  const severityOrder = { high: 3, medium: 2, low: 1 }
  return alerts
    .sort((a, b) => severityOrder[b.severity] - severityOrder[a.severity])
    .slice(0, limit)
}

async function createAlertNotifications(storeId: string, criticalAlerts: any[]) {
  // Get store users who should receive notifications
  const storeUsers = await prisma.user.findMany({
    where: {
      storeId,
      isActive: true,
      role: { in: ['FOUNDER', 'SUPER_ADMIN', 'ADMIN'] }
    },
    select: { id: true }
  })

  // Create notifications for critical alerts
  for (const alert of criticalAlerts) {
    for (const user of storeUsers) {
      // Check if notification already exists
      const existingNotification = await prisma.notification.findFirst({
        where: {
          userId: user.id,
          title: alert.title,
          createdAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Last 24 hours
        }
      })

      if (!existingNotification) {
        await prisma.notification.create({
          data: {
            title: alert.title,
            message: alert.message,
            type: 'WARNING',
            userId: user.id,
            storeId
          }
        })
      }
    }
  }
}
