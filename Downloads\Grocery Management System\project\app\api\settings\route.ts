import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  validateStoreAccess,
  createAuditLog
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const settingSchema = z.object({
  category: z.enum(['GENERAL', 'STORE', 'TAX', 'NOTIFICATION', 'SECURITY', 'INTEGRATION', 'APPEARANCE', 'BILLING']),
  key: z.string().min(1, 'Setting key is required'),
  value: z.any(),
  dataType: z.enum(['STRING', 'NUMBER', 'BOOLEAN', 'JSON', 'ARRAY']).default('STRING'),
  isPublic: z.boolean().default(false),
  description: z.string().optional(),
  validationRules: z.object({
    required: z.boolean().default(false),
    min: z.number().optional(),
    max: z.number().optional(),
    pattern: z.string().optional(),
    options: z.array(z.any()).optional()
  }).optional()
})

const bulkUpdateSchema = z.object({
  settings: z.array(z.object({
    category: z.string(),
    key: z.string(),
    value: z.any()
  }))
})

// GET /api/settings - Get all settings or by category
export const GET = withPermission('SETTINGS', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { searchParams } = new URL(request.url)
  const category = searchParams.get('category')
  const includeSystem = searchParams.get('includeSystem') === 'true'
  const publicOnly = searchParams.get('publicOnly') === 'true'

  try {
    const where: any = { storeId }
    
    if (category) {
      where.category = category
    }
    
    if (publicOnly) {
      where.isPublic = true
    }
    
    // Non-admin users can only see public settings
    if (!['FOUNDER', 'SUPER_ADMIN', 'ADMIN'].includes(user.role)) {
      where.isPublic = true
    }

    const settings = await prisma.setting.findMany({
      where,
      orderBy: [
        { category: 'asc' },
        { key: 'asc' }
      ]
    })

    // Group settings by category
    const groupedSettings = settings.reduce((acc: any, setting) => {
      if (!acc[setting.category]) {
        acc[setting.category] = []
      }
      
      acc[setting.category].push({
        key: setting.key,
        value: parseSettingValue(setting.value, setting.dataType),
        dataType: setting.dataType,
        isPublic: setting.isPublic,
        description: setting.description,
        validationRules: setting.validationRules,
        updatedAt: setting.updatedAt
      })
      
      return acc
    }, {})

    // Include system defaults if requested
    if (includeSystem) {
      const systemDefaults = getSystemDefaultSettings()
      Object.keys(systemDefaults).forEach(category => {
        if (!groupedSettings[category]) {
          groupedSettings[category] = []
        }
        
        systemDefaults[category].forEach((defaultSetting: any) => {
          const existing = groupedSettings[category].find((s: any) => s.key === defaultSetting.key)
          if (!existing) {
            groupedSettings[category].push({
              ...defaultSetting,
              value: defaultSetting.defaultValue,
              isDefault: true
            })
          }
        })
      })
    }

    return createSuccessResponse({
      settings: groupedSettings,
      categories: Object.keys(groupedSettings)
    })

  } catch (error) {
    console.error('Error fetching settings:', error)
    return createErrorResponse('Failed to fetch settings', 500)
  }
})

// POST /api/settings - Create or update setting
export const POST = withPermission('SETTINGS', 'UPDATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const data = settingSchema.parse(body)

  try {
    // Validate setting value
    const validationResult = validateSettingValue(data.value, data.dataType, data.validationRules)
    if (!validationResult.isValid) {
      return createErrorResponse(`Invalid setting value: ${validationResult.error}`, 400)
    }

    // Check if user has permission to update this setting
    if (!canUserUpdateSetting(user, data.category, data.key)) {
      return createErrorResponse('Insufficient permissions to update this setting', 403)
    }

    const serializedValue = serializeSettingValue(data.value, data.dataType)

    // Create or update setting
    const setting = await prisma.setting.upsert({
      where: {
        storeId_category_key: {
          storeId,
          category: data.category,
          key: data.key
        }
      },
      update: {
        value: serializedValue,
        dataType: data.dataType,
        isPublic: data.isPublic,
        description: data.description,
        validationRules: data.validationRules,
        updatedById: user.id
      },
      create: {
        storeId,
        category: data.category,
        key: data.key,
        value: serializedValue,
        dataType: data.dataType,
        isPublic: data.isPublic,
        description: data.description,
        validationRules: data.validationRules,
        createdById: user.id,
        updatedById: user.id
      }
    })

    // Create audit log
    await createAuditLog(
      'UPDATE',
      'SETTING',
      `Updated setting: ${data.category}.${data.key} = ${data.value}`,
      user.id,
      storeId
    )

    // Handle special settings that require immediate action
    await handleSpecialSetting(data.category, data.key, data.value, storeId)

    return createSuccessResponse({
      key: setting.key,
      value: parseSettingValue(setting.value, setting.dataType),
      category: setting.category
    }, 'Setting updated successfully')

  } catch (error) {
    console.error('Error updating setting:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to update setting',
      400
    )
  }
})

// PUT /api/settings - Bulk update settings
export const PUT = withPermission('SETTINGS', 'UPDATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const { settings } = bulkUpdateSchema.parse(body)

  try {
    const results = []
    const errors = []

    for (const settingData of settings) {
      try {
        // Check permissions for each setting
        if (!canUserUpdateSetting(user, settingData.category, settingData.key)) {
          errors.push(`No permission to update ${settingData.category}.${settingData.key}`)
          continue
        }

        // Get setting metadata for validation
        const existingSetting = await prisma.setting.findFirst({
          where: {
            storeId,
            category: settingData.category,
            key: settingData.key
          }
        })

        const dataType = existingSetting?.dataType || 'STRING'
        const validationRules = existingSetting?.validationRules

        // Validate value
        const validationResult = validateSettingValue(settingData.value, dataType, validationRules)
        if (!validationResult.isValid) {
          errors.push(`Invalid value for ${settingData.category}.${settingData.key}: ${validationResult.error}`)
          continue
        }

        const serializedValue = serializeSettingValue(settingData.value, dataType)

        // Update setting
        const setting = await prisma.setting.upsert({
          where: {
            storeId_category_key: {
              storeId,
              category: settingData.category,
              key: settingData.key
            }
          },
          update: {
            value: serializedValue,
            updatedById: user.id
          },
          create: {
            storeId,
            category: settingData.category,
            key: settingData.key,
            value: serializedValue,
            dataType,
            createdById: user.id,
            updatedById: user.id
          }
        })

        results.push({
          category: setting.category,
          key: setting.key,
          value: parseSettingValue(setting.value, setting.dataType),
          success: true
        })

        // Handle special settings
        await handleSpecialSetting(settingData.category, settingData.key, settingData.value, storeId)

      } catch (error) {
        errors.push(`Error updating ${settingData.category}.${settingData.key}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    // Create audit log for bulk update
    await createAuditLog(
      'UPDATE',
      'SETTING',
      `Bulk updated ${results.length} settings`,
      user.id,
      storeId
    )

    return createSuccessResponse({
      updated: results,
      errors,
      summary: {
        total: settings.length,
        successful: results.length,
        failed: errors.length
      }
    }, `Bulk update completed: ${results.length} successful, ${errors.length} failed`)

  } catch (error) {
    console.error('Error bulk updating settings:', error)
    return createErrorResponse('Failed to bulk update settings', 500)
  }
})

// DELETE /api/settings - Reset setting to default
export const DELETE = withPermission('SETTINGS', 'DELETE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { searchParams } = new URL(request.url)
  const category = searchParams.get('category')
  const key = searchParams.get('key')

  if (!category || !key) {
    return createErrorResponse('Category and key are required', 400)
  }

  try {
    // Check permissions
    if (!canUserUpdateSetting(user, category, key)) {
      return createErrorResponse('Insufficient permissions to reset this setting', 403)
    }

    // Delete custom setting (will fall back to system default)
    await prisma.setting.deleteMany({
      where: {
        storeId,
        category,
        key
      }
    })

    // Create audit log
    await createAuditLog(
      'DELETE',
      'SETTING',
      `Reset setting to default: ${category}.${key}`,
      user.id,
      storeId
    )

    return createSuccessResponse({}, 'Setting reset to default successfully')

  } catch (error) {
    console.error('Error resetting setting:', error)
    return createErrorResponse('Failed to reset setting', 500)
  }
})

// Helper functions

function parseSettingValue(value: string, dataType: string): any {
  try {
    switch (dataType) {
      case 'NUMBER':
        return parseFloat(value)
      case 'BOOLEAN':
        return value === 'true' || value === '1'
      case 'JSON':
      case 'ARRAY':
        return JSON.parse(value)
      default:
        return value
    }
  } catch {
    return value
  }
}

function serializeSettingValue(value: any, dataType: string): string {
  switch (dataType) {
    case 'JSON':
    case 'ARRAY':
      return JSON.stringify(value)
    case 'BOOLEAN':
      return value ? 'true' : 'false'
    default:
      return String(value)
  }
}

function validateSettingValue(value: any, dataType: string, rules?: any): { isValid: boolean; error?: string } {
  if (rules?.required && (value === null || value === undefined || value === '')) {
    return { isValid: false, error: 'Value is required' }
  }

  switch (dataType) {
    case 'NUMBER':
      const numValue = parseFloat(value)
      if (isNaN(numValue)) {
        return { isValid: false, error: 'Value must be a number' }
      }
      if (rules?.min !== undefined && numValue < rules.min) {
        return { isValid: false, error: `Value must be at least ${rules.min}` }
      }
      if (rules?.max !== undefined && numValue > rules.max) {
        return { isValid: false, error: `Value must be at most ${rules.max}` }
      }
      break

    case 'STRING':
      if (rules?.pattern && !new RegExp(rules.pattern).test(value)) {
        return { isValid: false, error: 'Value does not match required pattern' }
      }
      if (rules?.options && !rules.options.includes(value)) {
        return { isValid: false, error: `Value must be one of: ${rules.options.join(', ')}` }
      }
      break

    case 'ARRAY':
      if (!Array.isArray(value)) {
        return { isValid: false, error: 'Value must be an array' }
      }
      break

    case 'JSON':
      try {
        if (typeof value === 'string') {
          JSON.parse(value)
        }
      } catch {
        return { isValid: false, error: 'Value must be valid JSON' }
      }
      break
  }

  return { isValid: true }
}

function canUserUpdateSetting(user: any, category: string, key: string): boolean {
  // Founders and Super Admins can update all settings
  if (['FOUNDER', 'SUPER_ADMIN'].includes(user.role)) {
    return true
  }

  // Admins can update most settings except security and billing
  if (user.role === 'ADMIN') {
    return !['SECURITY', 'BILLING'].includes(category)
  }

  // Staff can only update appearance and notification settings
  if (user.role === 'STAFF') {
    return ['APPEARANCE', 'NOTIFICATION'].includes(category)
  }

  return false
}

async function handleSpecialSetting(category: string, key: string, value: any, storeId: string) {
  // Handle settings that require immediate action
  switch (`${category}.${key}`) {
    case 'TAX.DEFAULT_RATE':
      // Update default tax rate for new products
      await prisma.product.updateMany({
        where: { storeId, taxRate: 0 },
        data: { taxRate: parseFloat(value) }
      })
      break

    case 'STORE.CURRENCY':
      // Update store currency
      await prisma.store.update({
        where: { id: storeId },
        data: { currency: value }
      })
      break

    case 'NOTIFICATION.EMAIL_ENABLED':
      // Enable/disable email notifications
      if (!value) {
        await prisma.notificationSubscription.updateMany({
          where: { storeId },
          data: { channels: { not: { has: 'EMAIL' } } }
        })
      }
      break
  }
}

function getSystemDefaultSettings() {
  return {
    GENERAL: [
      { key: 'TIMEZONE', defaultValue: 'UTC', dataType: 'STRING', description: 'System timezone' },
      { key: 'DATE_FORMAT', defaultValue: 'YYYY-MM-DD', dataType: 'STRING', description: 'Date display format' },
      { key: 'LANGUAGE', defaultValue: 'en', dataType: 'STRING', description: 'System language' }
    ],
    STORE: [
      { key: 'NAME', defaultValue: 'My Store', dataType: 'STRING', description: 'Store name' },
      { key: 'CURRENCY', defaultValue: 'INR', dataType: 'STRING', description: 'Store currency' },
      { key: 'ADDRESS', defaultValue: '', dataType: 'STRING', description: 'Store address' },
      { key: 'PHONE', defaultValue: '', dataType: 'STRING', description: 'Store phone number' },
      { key: 'EMAIL', defaultValue: '', dataType: 'STRING', description: 'Store email' }
    ],
    TAX: [
      { key: 'DEFAULT_RATE', defaultValue: 18, dataType: 'NUMBER', description: 'Default tax rate (%)' },
      { key: 'INCLUSIVE', defaultValue: false, dataType: 'BOOLEAN', description: 'Tax inclusive pricing' },
      { key: 'GST_NUMBER', defaultValue: '', dataType: 'STRING', description: 'GST registration number' }
    ],
    NOTIFICATION: [
      { key: 'EMAIL_ENABLED', defaultValue: true, dataType: 'BOOLEAN', description: 'Enable email notifications' },
      { key: 'SMS_ENABLED', defaultValue: false, dataType: 'BOOLEAN', description: 'Enable SMS notifications' },
      { key: 'LOW_STOCK_THRESHOLD', defaultValue: 10, dataType: 'NUMBER', description: 'Low stock alert threshold' }
    ],
    SECURITY: [
      { key: 'SESSION_TIMEOUT', defaultValue: 3600, dataType: 'NUMBER', description: 'Session timeout (seconds)' },
      { key: 'PASSWORD_EXPIRY', defaultValue: 90, dataType: 'NUMBER', description: 'Password expiry (days)' },
      { key: 'MAX_LOGIN_ATTEMPTS', defaultValue: 5, dataType: 'NUMBER', description: 'Maximum login attempts' }
    ],
    APPEARANCE: [
      { key: 'THEME', defaultValue: 'light', dataType: 'STRING', description: 'UI theme' },
      { key: 'PRIMARY_COLOR', defaultValue: '#3b82f6', dataType: 'STRING', description: 'Primary brand color' },
      { key: 'LOGO_URL', defaultValue: '', dataType: 'STRING', description: 'Store logo URL' }
    ]
  }
}
