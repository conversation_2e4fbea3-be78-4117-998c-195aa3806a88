import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// GET /api/documents/[id]/access - Get document access permissions
export const GET = withPermission('DOCUMENT', 'READ', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const documentId = pathSegments[pathSegments.length - 2] // Get document ID from path

  // Check if document exists and user has access
  const document = await prisma.document.findFirst({
    where: {
      id: documentId,
      storeId
    },
    include: {
      uploadedBy: {
        select: {
          id: true,
          name: true
        }
      },
      documentAccess: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true
            }
          },
          grantedBy: {
            select: {
              id: true,
              name: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }
    }
  })

  if (!document) {
    return createErrorResponse('Document not found', 404)
  }

  // Check if user can view access permissions
  if (!canUserManageAccess(document, user)) {
    return createErrorResponse('Insufficient permissions to view document access', 403)
  }

  const accessInfo = {
    document: {
      id: document.id,
      name: document.name,
      accessLevel: document.accessLevel,
      uploadedBy: document.uploadedBy
    },
    permissions: document.documentAccess.map(access => ({
      id: access.id,
      user: access.user,
      accessType: access.accessType,
      grantedBy: access.grantedBy,
      grantedAt: access.createdAt,
      expiresAt: access.expiresAt,
      isExpired: access.expiresAt && new Date(access.expiresAt) < new Date()
    })),
    availableUsers: await getAvailableUsers(storeId, document.documentAccess.map(a => a.userId))
  }

  return createSuccessResponse(accessInfo)
})

// POST /api/documents/[id]/access - Grant document access
export const POST = withPermission('DOCUMENT', 'UPDATE', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const documentId = pathSegments[pathSegments.length - 2] // Get document ID from path
  const body = await request.json()

  const accessSchema = z.object({
    userId: z.string().min(1, 'User ID is required'),
    accessType: z.enum(['READ', 'WRITE', 'ADMIN']),
    expiresAt: z.string().optional(),
    notes: z.string().optional()
  })

  const { userId, accessType, expiresAt, notes } = accessSchema.parse(body)

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Check if document exists
      const document = await tx.document.findFirst({
        where: { id: documentId, storeId }
      })

      if (!document) {
        throw new Error('Document not found')
      }

      // Check if user can manage access
      if (!canUserManageAccess(document, user)) {
        throw new Error('Insufficient permissions to manage document access')
      }

      // Validate target user exists
      const targetUser = await tx.user.findFirst({
        where: { id: userId, storeId, isActive: true }
      })

      if (!targetUser) {
        throw new Error('Target user not found')
      }

      // Check if access already exists
      const existingAccess = await tx.documentAccess.findFirst({
        where: {
          documentId,
          userId
        }
      })

      if (existingAccess) {
        // Update existing access
        const updatedAccess = await tx.documentAccess.update({
          where: { id: existingAccess.id },
          data: {
            accessType,
            expiresAt: expiresAt ? new Date(expiresAt) : undefined,
            notes,
            grantedById: user.id
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true
              }
            },
            grantedBy: {
              select: {
                id: true,
                name: true
              }
            }
          }
        })

        return { access: updatedAccess, action: 'updated' }
      } else {
        // Create new access
        const newAccess = await tx.documentAccess.create({
          data: {
            documentId,
            userId,
            accessType,
            expiresAt: expiresAt ? new Date(expiresAt) : undefined,
            notes,
            grantedById: user.id,
            storeId
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true
              }
            },
            grantedBy: {
              select: {
                id: true,
                name: true
              }
            }
          }
        })

        return { access: newAccess, action: 'granted' }
      }
    })

    // Create audit log
    await createAuditLog(
      'UPDATE',
      'DOCUMENT',
      `${result.action === 'granted' ? 'Granted' : 'Updated'} ${accessType} access to document: ${documentId} for user: ${result.access.user.name}`,
      user.id,
      storeId
    )

    // Create notification for the user
    await prisma.notification.create({
      data: {
        title: 'Document Access Granted',
        message: `You have been granted ${accessType} access to document: ${result.access.document?.name || 'Document'}`,
        type: 'INFO',
        category: 'SYSTEM',
        userId: result.access.userId,
        storeId
      }
    })

    return createSuccessResponse(result, `Document access ${result.action} successfully`)

  } catch (error) {
    console.error('Error managing document access:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to manage document access',
      400
    )
  }
})

// DELETE /api/documents/[id]/access - Revoke document access
export const DELETE = withPermission('DOCUMENT', 'UPDATE', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const documentId = pathSegments[pathSegments.length - 2] // Get document ID from path
  const { searchParams } = url
  const accessId = searchParams.get('accessId')
  const userId = searchParams.get('userId')

  if (!accessId && !userId) {
    return createErrorResponse('Access ID or User ID is required', 400)
  }

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Check if document exists
      const document = await tx.document.findFirst({
        where: { id: documentId, storeId }
      })

      if (!document) {
        throw new Error('Document not found')
      }

      // Check if user can manage access
      if (!canUserManageAccess(document, user)) {
        throw new Error('Insufficient permissions to manage document access')
      }

      // Find access record
      const where: any = { documentId }
      if (accessId) {
        where.id = accessId
      } else {
        where.userId = userId
      }

      const access = await tx.documentAccess.findFirst({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true
            }
          }
        }
      })

      if (!access) {
        throw new Error('Access record not found')
      }

      // Delete access record
      await tx.documentAccess.delete({
        where: { id: access.id }
      })

      return access
    })

    // Create audit log
    await createAuditLog(
      'DELETE',
      'DOCUMENT',
      `Revoked access to document: ${documentId} for user: ${result.user.name}`,
      user.id,
      storeId
    )

    // Create notification for the user
    await prisma.notification.create({
      data: {
        title: 'Document Access Revoked',
        message: `Your access to a document has been revoked`,
        type: 'WARNING',
        category: 'SYSTEM',
        userId: result.userId,
        storeId
      }
    })

    return createSuccessResponse({}, 'Document access revoked successfully')

  } catch (error) {
    console.error('Error revoking document access:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to revoke document access',
      400
    )
  }
})

// Helper functions

function canUserManageAccess(document: any, user: any): boolean {
  // Founders and Super Admins can manage all document access
  if (['FOUNDER', 'SUPER_ADMIN'].includes(user.role)) {
    return true
  }

  // Document owner can manage access
  if (document.uploadedById === user.id) {
    return true
  }

  // Users with ADMIN access can manage access
  const userAccess = document.documentAccess?.find((access: any) => access.userId === user.id)
  return userAccess && userAccess.accessType === 'ADMIN'
}

async function getAvailableUsers(storeId: string, excludeUserIds: string[]) {
  return await prisma.user.findMany({
    where: {
      storeId,
      isActive: true,
      id: { notIn: excludeUserIds }
    },
    select: {
      id: true,
      name: true,
      email: true,
      role: true
    },
    orderBy: { name: 'asc' }
  })
}
