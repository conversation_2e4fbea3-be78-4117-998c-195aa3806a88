"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/reports/route";
exports.ids = ["app/api/reports/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Freports%2Froute&page=%2Fapi%2Freports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freports%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Freports%2Froute&page=%2Fapi%2Freports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freports%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/node-polyfill-headers */ \"(rsc)/./node_modules/next/dist/server/node-polyfill-headers.js\");\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var C_Users_DELL_Downloads_Grocery_Management_System_project_app_api_reports_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/reports/route.ts */ \"(rsc)/./app/api/reports/route.ts\");\n\n// @ts-ignore this need to be imported from next/dist to be external\n\n\n// @ts-expect-error - replaced by webpack/turbopack loader\n\nconst AppRouteRouteModule = next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__.AppRouteRouteModule;\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__.RouteKind.APP_ROUTE,\n        page: \"/api/reports/route\",\n        pathname: \"/api/reports\",\n        filename: \"route\",\n        bundlePath: \"app/api/reports/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\api\\\\reports\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_DELL_Downloads_Grocery_Management_System_project_app_api_reports_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/reports/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Freports%2Froute&page=%2Fapi%2Freports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freports%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/reports/route.ts":
/*!**********************************!*\
  !*** ./app/api/reports/route.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var _lib_api_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api-utils */ \"(rsc)/./lib/api-utils.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n// GET /api/reports - Get various reports based on type\nconst GET = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.withPermission)(\"REPORTS\", \"READ\", async (request, user)=>{\n    const storeId = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.validateStoreAccess)(user);\n    const url = new URL(request.url);\n    const reportType = url.searchParams.get(\"type\");\n    const startDate = url.searchParams.get(\"startDate\");\n    const endDate = url.searchParams.get(\"endDate\");\n    const dateFilter = startDate || endDate ? (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.buildDateRangeFilter)(startDate || undefined, endDate || undefined, \"createdAt\") : {};\n    try {\n        switch(reportType){\n            case \"sales-summary\":\n                return await getSalesSummary(storeId, dateFilter);\n            case \"sales-detailed\":\n                return await getSalesDetailed(storeId, dateFilter);\n            case \"inventory-summary\":\n                return await getInventorySummary(storeId);\n            case \"low-stock\":\n                return await getLowStockReport(storeId);\n            case \"top-products\":\n                return await getTopProductsReport(storeId, dateFilter);\n            case \"customer-summary\":\n                return await getCustomerSummary(storeId, dateFilter);\n            case \"financial-summary\":\n                return await getFinancialSummary(storeId, dateFilter);\n            default:\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createErrorResponse)(\"Invalid report type\", 400);\n        }\n    } catch (error) {\n        console.error(\"Report generation error:\", error);\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createErrorResponse)(\"Failed to generate report\", 500);\n    }\n});\nasync function getSalesSummary(storeId, dateFilter) {\n    const sales = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.sale.findMany({\n        where: {\n            storeId,\n            ...dateFilter\n        },\n        include: {\n            items: true\n        }\n    });\n    const summary = {\n        totalSales: sales.length,\n        totalRevenue: sales.reduce((sum, sale)=>sum + sale.totalAmount, 0),\n        totalTax: sales.reduce((sum, sale)=>sum + sale.taxAmount, 0),\n        totalDiscount: sales.reduce((sum, sale)=>sum + sale.discount, 0),\n        totalItems: sales.reduce((sum, sale)=>sum + sale.items.length, 0),\n        averageOrderValue: sales.length > 0 ? sales.reduce((sum, sale)=>sum + sale.totalAmount, 0) / sales.length : 0,\n        paymentMethods: sales.reduce((acc, sale)=>{\n            acc[sale.paymentMethod] = (acc[sale.paymentMethod] || 0) + 1;\n            return acc;\n        }, {}),\n        dailyBreakdown: sales.reduce((acc, sale)=>{\n            const date = sale.createdAt.toISOString().split(\"T\")[0];\n            if (!acc[date]) {\n                acc[date] = {\n                    sales: 0,\n                    revenue: 0\n                };\n            }\n            acc[date].sales += 1;\n            acc[date].revenue += sale.totalAmount;\n            return acc;\n        }, {})\n    };\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)(summary);\n}\nasync function getSalesDetailed(storeId, dateFilter) {\n    const sales = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.sale.findMany({\n        where: {\n            storeId,\n            ...dateFilter\n        },\n        include: {\n            customer: {\n                select: {\n                    name: true,\n                    phone: true\n                }\n            },\n            items: {\n                include: {\n                    product: {\n                        select: {\n                            name: true,\n                            sku: true,\n                            unit: true\n                        }\n                    }\n                }\n            },\n            createdBy: {\n                select: {\n                    name: true\n                }\n            }\n        },\n        orderBy: {\n            createdAt: \"desc\"\n        }\n    });\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)(sales);\n}\nasync function getInventorySummary(storeId) {\n    const products = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.product.findMany({\n        where: {\n            storeId,\n            isActive: true\n        },\n        include: {\n            category: {\n                select: {\n                    name: true\n                }\n            },\n            inventory: {\n                where: {\n                    storeId\n                },\n                select: {\n                    quantity: true,\n                    reorderLevel: true\n                }\n            }\n        }\n    });\n    const summary = {\n        totalProducts: products.length,\n        totalStockValue: products.reduce((sum, product)=>{\n            const inventory = product.inventory[0];\n            return sum + (inventory ? inventory.quantity * product.costPrice : 0);\n        }, 0),\n        lowStockItems: products.filter((product)=>{\n            const inventory = product.inventory[0];\n            return inventory && inventory.quantity <= product.minStock;\n        }).length,\n        outOfStockItems: products.filter((product)=>{\n            const inventory = product.inventory[0];\n            return !inventory || inventory.quantity === 0;\n        }).length,\n        categoryBreakdown: products.reduce((acc, product)=>{\n            const categoryName = product.category?.name || \"Uncategorized\";\n            if (!acc[categoryName]) {\n                acc[categoryName] = {\n                    products: 0,\n                    stockValue: 0\n                };\n            }\n            acc[categoryName].products += 1;\n            const inventory = product.inventory[0];\n            acc[categoryName].stockValue += inventory ? inventory.quantity * product.costPrice : 0;\n            return acc;\n        }, {})\n    };\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)(summary);\n}\nasync function getLowStockReport(storeId) {\n    const products = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.product.findMany({\n        where: {\n            storeId,\n            isActive: true\n        },\n        include: {\n            category: {\n                select: {\n                    name: true\n                }\n            },\n            inventory: {\n                where: {\n                    storeId\n                },\n                select: {\n                    quantity: true,\n                    reorderLevel: true,\n                    lastUpdated: true\n                }\n            }\n        }\n    });\n    const lowStockItems = products.filter((product)=>{\n        const inventory = product.inventory[0];\n        return inventory && inventory.quantity <= product.minStock;\n    }).map((product)=>({\n            id: product.id,\n            name: product.name,\n            sku: product.sku,\n            category: product.category?.name,\n            currentStock: product.inventory[0]?.quantity || 0,\n            minStock: product.minStock,\n            reorderLevel: product.inventory[0]?.reorderLevel || 0,\n            unit: product.unit,\n            costPrice: product.costPrice,\n            lastUpdated: product.inventory[0]?.lastUpdated\n        }));\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)(lowStockItems);\n}\nasync function getTopProductsReport(storeId, dateFilter) {\n    const saleItems = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.saleItem.findMany({\n        where: {\n            sale: {\n                storeId,\n                ...dateFilter\n            }\n        },\n        include: {\n            product: {\n                select: {\n                    name: true,\n                    sku: true,\n                    unit: true,\n                    category: {\n                        select: {\n                            name: true\n                        }\n                    }\n                }\n            }\n        }\n    });\n    const productStats = saleItems.reduce((acc, item)=>{\n        const productId = item.productId;\n        if (!acc[productId]) {\n            acc[productId] = {\n                product: item.product,\n                totalQuantity: 0,\n                totalRevenue: 0,\n                totalSales: 0\n            };\n        }\n        acc[productId].totalQuantity += item.quantity;\n        acc[productId].totalRevenue += item.totalPrice;\n        acc[productId].totalSales += 1;\n        return acc;\n    }, {});\n    const topProducts = Object.values(productStats).sort((a, b)=>b.totalRevenue - a.totalRevenue).slice(0, 20);\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)(topProducts);\n}\nasync function getCustomerSummary(storeId, dateFilter) {\n    const customers = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.customer.findMany({\n        where: {\n            storeId,\n            isActive: true\n        },\n        include: {\n            sales: {\n                where: dateFilter,\n                select: {\n                    totalAmount: true,\n                    createdAt: true\n                }\n            },\n            _count: {\n                select: {\n                    sales: true\n                }\n            }\n        }\n    });\n    const customerStats = customers.map((customer)=>({\n            id: customer.id,\n            name: customer.name,\n            phone: customer.phone,\n            email: customer.email,\n            totalOrders: customer._count.sales,\n            totalSpent: customer.sales.reduce((sum, sale)=>sum + sale.totalAmount, 0),\n            averageOrderValue: customer.sales.length > 0 ? customer.sales.reduce((sum, sale)=>sum + sale.totalAmount, 0) / customer.sales.length : 0,\n            lastOrderDate: customer.sales.length > 0 ? Math.max(...customer.sales.map((sale)=>new Date(sale.createdAt).getTime())) : null\n        })).sort((a, b)=>b.totalSpent - a.totalSpent);\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)(customerStats);\n}\nasync function getFinancialSummary(storeId, dateFilter) {\n    const [sales, purchases] = await Promise.all([\n        _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.sale.findMany({\n            where: {\n                storeId,\n                ...dateFilter\n            }\n        }),\n        _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.purchase.findMany({\n            where: {\n                storeId,\n                ...dateFilter\n            }\n        })\n    ]);\n    const summary = {\n        revenue: {\n            total: sales.reduce((sum, sale)=>sum + sale.totalAmount, 0),\n            tax: sales.reduce((sum, sale)=>sum + sale.taxAmount, 0),\n            discount: sales.reduce((sum, sale)=>sum + sale.discount, 0)\n        },\n        expenses: {\n            total: purchases.reduce((sum, purchase)=>sum + purchase.totalAmount, 0),\n            tax: purchases.reduce((sum, purchase)=>sum + purchase.taxAmount, 0),\n            discount: purchases.reduce((sum, purchase)=>sum + purchase.discount, 0)\n        },\n        profit: {\n            gross: sales.reduce((sum, sale)=>sum + sale.totalAmount, 0) - purchases.reduce((sum, purchase)=>sum + purchase.totalAmount, 0),\n            margin: sales.length > 0 ? (sales.reduce((sum, sale)=>sum + sale.totalAmount, 0) - purchases.reduce((sum, purchase)=>sum + purchase.totalAmount, 0)) / sales.reduce((sum, sale)=>sum + sale.totalAmount, 0) * 100 : 0\n        },\n        transactions: {\n            salesCount: sales.length,\n            purchasesCount: purchases.length,\n            averageSaleValue: sales.length > 0 ? sales.reduce((sum, sale)=>sum + sale.totalAmount, 0) / sales.length : 0,\n            averagePurchaseValue: purchases.length > 0 ? purchases.reduce((sum, purchase)=>sum + purchase.totalAmount, 0) / purchases.length : 0\n        }\n    };\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)(summary);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/reports/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/api-utils.ts":
/*!**************************!*\
  !*** ./lib/api-utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildDateRangeFilter: () => (/* binding */ buildDateRangeFilter),\n/* harmony export */   buildSearchFilter: () => (/* binding */ buildSearchFilter),\n/* harmony export */   createAuditLog: () => (/* binding */ createAuditLog),\n/* harmony export */   createErrorResponse: () => (/* binding */ createErrorResponse),\n/* harmony export */   createPaginatedResponse: () => (/* binding */ createPaginatedResponse),\n/* harmony export */   createResponse: () => (/* binding */ createResponse),\n/* harmony export */   createSuccessResponse: () => (/* binding */ createSuccessResponse),\n/* harmony export */   getPaginationParams: () => (/* binding */ getPaginationParams),\n/* harmony export */   validateRequiredFields: () => (/* binding */ validateRequiredFields),\n/* harmony export */   validateStoreAccess: () => (/* binding */ validateStoreAccess),\n/* harmony export */   withPermission: () => (/* binding */ withPermission)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n// Standard API response wrapper\nfunction createResponse(data, status = 200) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(data, {\n        status\n    });\n}\n// Error response wrapper\nfunction createErrorResponse(message, status = 400) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        error: message\n    }, {\n        status\n    });\n}\n// Success response wrapper\nfunction createSuccessResponse(data, message) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        success: true,\n        data,\n        message\n    });\n}\n// Paginated response wrapper\nfunction createPaginatedResponse(data, page, limit, total) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        data,\n        pagination: {\n            page,\n            limit,\n            total,\n            totalPages: Math.ceil(total / limit),\n            hasNext: page * limit < total,\n            hasPrev: page > 1\n        }\n    });\n}\n// Permission-based API handler wrapper\nfunction withPermission(module, permission, handler) {\n    return async (request)=>{\n        try {\n            const permissionCheck = await (0,_auth__WEBPACK_IMPORTED_MODULE_1__.checkPermission)(request, module, permission);\n            if (permissionCheck.error) {\n                return createErrorResponse(permissionCheck.error, permissionCheck.status);\n            }\n            return await handler(request, permissionCheck.user);\n        } catch (error) {\n            console.error(`API Error in ${module}:`, error);\n            return createErrorResponse(\"Internal server error\", 500);\n        }\n    };\n}\n// Audit log helper\nasync function createAuditLog(action, module, details, userId, storeId) {\n    try {\n        await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.auditLog.create({\n            data: {\n                action,\n                module,\n                details,\n                userId,\n                storeId\n            }\n        });\n    } catch (error) {\n        console.error(\"Failed to create audit log:\", error);\n    }\n}\n// Pagination helper\nfunction getPaginationParams(request) {\n    const url = new URL(request.url);\n    const page = parseInt(url.searchParams.get(\"page\") || \"1\");\n    const limit = parseInt(url.searchParams.get(\"limit\") || \"10\");\n    const search = url.searchParams.get(\"search\") || \"\";\n    const sortBy = url.searchParams.get(\"sortBy\") || \"createdAt\";\n    const sortOrder = url.searchParams.get(\"sortOrder\") || \"desc\";\n    return {\n        page: Math.max(1, page),\n        limit: Math.min(100, Math.max(1, limit)),\n        skip: (Math.max(1, page) - 1) * Math.min(100, Math.max(1, limit)),\n        search,\n        sortBy,\n        sortOrder: sortOrder === \"asc\" ? \"asc\" : \"desc\"\n    };\n}\n// Search filter helper\nfunction buildSearchFilter(search, fields) {\n    if (!search) return {};\n    return {\n        OR: fields.map((field)=>({\n                [field]: {\n                    contains: search,\n                    mode: \"insensitive\"\n                }\n            }))\n    };\n}\n// Date range filter helper\nfunction buildDateRangeFilter(startDate, endDate, field = \"createdAt\") {\n    const filter = {};\n    if (startDate) {\n        filter[field] = {\n            ...filter[field],\n            gte: new Date(startDate)\n        };\n    }\n    if (endDate) {\n        const end = new Date(endDate);\n        end.setHours(23, 59, 59, 999);\n        filter[field] = {\n            ...filter[field],\n            lte: end\n        };\n    }\n    return Object.keys(filter).length > 0 ? filter : {};\n}\n// Validation helper\nfunction validateRequiredFields(data, fields) {\n    const missing = fields.filter((field)=>!data[field]);\n    if (missing.length > 0) {\n        throw new Error(`Missing required fields: ${missing.join(\", \")}`);\n    }\n}\n// Store validation helper\nfunction validateStoreAccess(user) {\n    if (!user.storeId) {\n        throw new Error(\"User not associated with any store\");\n    }\n    return user.storeId;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/api-utils.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkPermission: () => (/* binding */ checkPermission),\n/* harmony export */   createAuthResponse: () => (/* binding */ createAuthResponse),\n/* harmony export */   getAuthUser: () => (/* binding */ getAuthUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   signToken: () => (/* binding */ signToken),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key\";\nasync function hashPassword(password) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(password, 12);\n}\nasync function verifyPassword(password, hashedPassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, hashedPassword);\n}\nfunction signToken(payload) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: \"7d\"\n    });\n}\nfunction verifyToken(token) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n}\nasync function getAuthUser(request) {\n    try {\n        const token = request.headers.get(\"authorization\")?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return null;\n        }\n        const payload = verifyToken(token);\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n            where: {\n                id: payload.userId\n            },\n            include: {\n                store: true\n            }\n        });\n        if (!user || !user.isActive) {\n            return null;\n        }\n        return user;\n    } catch (error) {\n        console.error(\"Auth error:\", error);\n        return null;\n    }\n}\nfunction createAuthResponse(user, token) {\n    return {\n        user: {\n            id: user.id,\n            email: user.email,\n            name: user.name,\n            role: user.role,\n            storeId: user.storeId,\n            store: user.store\n        },\n        token\n    };\n}\n// Permission-based middleware helper\nasync function checkPermission(request, module, permission) {\n    const user = await getAuthUser(request);\n    if (!user) {\n        return {\n            error: \"Unauthorized\",\n            status: 401\n        };\n    }\n    // Import permissions dynamically to avoid circular dependency\n    const { hasPermission } = await __webpack_require__.e(/*! import() */ \"_rsc_lib_permissions_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./permissions */ \"(rsc)/./lib/permissions.ts\"));\n    if (!hasPermission(user.role, module, permission)) {\n        return {\n            error: \"Insufficient permissions\",\n            status: 403\n        };\n    }\n    return {\n        user,\n        error: null\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2dyb2NlcnktbWFuYWdlbWVudC1zeXN0ZW0vLi9saWIvcHJpc21hLnRzPzk4MjIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Freports%2Froute&page=%2Fapi%2Freports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freports%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();