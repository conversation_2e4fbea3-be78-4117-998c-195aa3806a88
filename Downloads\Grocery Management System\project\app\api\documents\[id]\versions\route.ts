import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// GET /api/documents/[id]/versions - Get document version history
export const GET = withPermission('DOCUMENT', 'READ', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const documentId = pathSegments[pathSegments.length - 2] // Get document ID from path

  // Check if document exists and user has access
  const document = await prisma.document.findFirst({
    where: {
      id: documentId,
      storeId
    }
  })

  if (!document) {
    return createErrorResponse('Document not found', 404)
  }

  // Check user access to document
  if (!await canUserAccessDocument(documentId, user.id, storeId)) {
    return createErrorResponse('Insufficient permissions to view document', 403)
  }

  // Get version history
  const versions = await prisma.documentVersion.findMany({
    where: {
      documentId,
      storeId
    },
    include: {
      uploadedBy: {
        select: {
          id: true,
          name: true
        }
      }
    },
    orderBy: { createdAt: 'desc' }
  })

  const versionsWithCalculations = versions.map(version => ({
    ...version,
    isLatest: version.version === versions[0].version,
    fileSizeFormatted: formatFileSize(version.fileSize),
    uploadedAgo: getTimeAgo(version.createdAt)
  }))

  return createSuccessResponse({
    document: {
      id: document.id,
      name: document.name,
      category: document.category
    },
    versions: versionsWithCalculations,
    totalVersions: versions.length
  })
})

// POST /api/documents/[id]/versions - Upload new document version
export const POST = withPermission('DOCUMENT', 'UPDATE', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const documentId = pathSegments[pathSegments.length - 2] // Get document ID from path
  const body = await request.json()

  const versionSchema = z.object({
    fileUrl: z.string().min(1, 'File URL is required'),
    fileName: z.string().min(1, 'File name is required'),
    fileSize: z.number().min(0, 'File size must be positive'),
    mimeType: z.string().min(1, 'MIME type is required'),
    changeLog: z.string().min(1, 'Change log is required'),
    versionType: z.enum(['MAJOR', 'MINOR', 'PATCH']).default('MINOR')
  })

  const { fileUrl, fileName, fileSize, mimeType, changeLog, versionType } = versionSchema.parse(body)

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Check if document exists
      const document = await tx.document.findFirst({
        where: { id: documentId, storeId }
      })

      if (!document) {
        throw new Error('Document not found')
      }

      // Check if user can edit document
      if (!await canUserEditDocument(documentId, user.id, storeId, tx)) {
        throw new Error('Insufficient permissions to update document')
      }

      // Get latest version to calculate new version number
      const latestVersion = await tx.documentVersion.findFirst({
        where: { documentId },
        orderBy: { createdAt: 'desc' }
      })

      const newVersionNumber = calculateNewVersion(latestVersion?.version || '1.0', versionType)

      // Check file size limits
      const maxFileSize = 100 * 1024 * 1024 // 100MB
      if (fileSize > maxFileSize) {
        throw new Error('File size exceeds maximum limit of 100MB')
      }

      // Create new version
      const newVersion = await tx.documentVersion.create({
        data: {
          documentId,
          version: newVersionNumber,
          fileUrl,
          fileName,
          fileSize,
          mimeType,
          changeLog,
          uploadedById: user.id,
          storeId
        },
        include: {
          uploadedBy: {
            select: {
              id: true,
              name: true
            }
          }
        }
      })

      // Update document with latest version info
      await tx.document.update({
        where: { id: documentId },
        data: {
          fileUrl,
          fileName,
          fileSize,
          mimeType,
          updatedAt: new Date()
        }
      })

      // Extract and index content if text-based
      if (mimeType.startsWith('text/') || mimeType === 'application/pdf') {
        await extractAndIndexContent(documentId, fileUrl, mimeType, storeId, tx)
      }

      return newVersion
    })

    // Create audit log
    await createAuditLog(
      'UPDATE',
      'DOCUMENT',
      `Uploaded new version ${result.version} of document: ${documentId} - ${changeLog}`,
      user.id,
      storeId
    )

    // Notify users with access about new version
    await notifyUsersAboutNewVersion(documentId, result.version, changeLog, storeId, user.id)

    return createSuccessResponse(result, 'New document version uploaded successfully')

  } catch (error) {
    console.error('Error uploading document version:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to upload document version',
      400
    )
  }
})

// DELETE /api/documents/[id]/versions - Delete document version
export const DELETE = withPermission('DOCUMENT', 'DELETE', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const documentId = pathSegments[pathSegments.length - 2] // Get document ID from path
  const { searchParams } = url
  const versionId = searchParams.get('versionId')

  if (!versionId) {
    return createErrorResponse('Version ID is required', 400)
  }

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Check if document exists
      const document = await tx.document.findFirst({
        where: { id: documentId, storeId }
      })

      if (!document) {
        throw new Error('Document not found')
      }

      // Check if user can delete document versions
      if (!await canUserDeleteDocument(documentId, user.id, storeId, tx)) {
        throw new Error('Insufficient permissions to delete document version')
      }

      // Get version to delete
      const version = await tx.documentVersion.findFirst({
        where: {
          id: versionId,
          documentId
        }
      })

      if (!version) {
        throw new Error('Version not found')
      }

      // Check if this is the only version
      const versionCount = await tx.documentVersion.count({
        where: { documentId }
      })

      if (versionCount === 1) {
        throw new Error('Cannot delete the only version of a document')
      }

      // Check if this is the latest version
      const latestVersion = await tx.documentVersion.findFirst({
        where: { documentId },
        orderBy: { createdAt: 'desc' }
      })

      const isLatestVersion = latestVersion?.id === versionId

      // Delete version
      await tx.documentVersion.delete({
        where: { id: versionId }
      })

      // If deleted version was the latest, update document with previous version
      if (isLatestVersion) {
        const newLatestVersion = await tx.documentVersion.findFirst({
          where: { documentId },
          orderBy: { createdAt: 'desc' }
        })

        if (newLatestVersion) {
          await tx.document.update({
            where: { id: documentId },
            data: {
              fileUrl: newLatestVersion.fileUrl,
              fileName: newLatestVersion.fileName,
              fileSize: newLatestVersion.fileSize,
              mimeType: newLatestVersion.mimeType
            }
          })
        }
      }

      return { version, isLatestVersion }
    })

    // Create audit log
    await createAuditLog(
      'DELETE',
      'DOCUMENT',
      `Deleted version ${result.version.version} of document: ${documentId}`,
      user.id,
      storeId
    )

    return createSuccessResponse({}, 'Document version deleted successfully')

  } catch (error) {
    console.error('Error deleting document version:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to delete document version',
      400
    )
  }
})

// Helper functions

async function canUserAccessDocument(documentId: string, userId: string, storeId: string): Promise<boolean> {
  const document = await prisma.document.findFirst({
    where: { id: documentId, storeId },
    include: {
      documentAccess: {
        where: { userId }
      }
    }
  })

  if (!document) return false

  // Public documents can be accessed by anyone
  if (document.isPublic || document.accessLevel === 'PUBLIC') return true

  // Document owner can access
  if (document.uploadedById === userId) return true

  // Check specific access permissions
  return document.documentAccess.length > 0
}

async function canUserEditDocument(documentId: string, userId: string, storeId: string, tx: any): Promise<boolean> {
  const document = await tx.document.findFirst({
    where: { id: documentId, storeId },
    include: {
      documentAccess: {
        where: { userId }
      }
    }
  })

  if (!document) return false

  // Document owner can edit
  if (document.uploadedById === userId) return true

  // Check write/admin access
  const userAccess = document.documentAccess[0]
  return userAccess && ['WRITE', 'ADMIN'].includes(userAccess.accessType)
}

async function canUserDeleteDocument(documentId: string, userId: string, storeId: string, tx: any): Promise<boolean> {
  const document = await tx.document.findFirst({
    where: { id: documentId, storeId },
    include: {
      documentAccess: {
        where: { userId }
      }
    }
  })

  if (!document) return false

  // Document owner can delete
  if (document.uploadedById === userId) return true

  // Check admin access
  const userAccess = document.documentAccess[0]
  return userAccess && userAccess.accessType === 'ADMIN'
}

function calculateNewVersion(currentVersion: string, versionType: string): string {
  const [major, minor, patch] = currentVersion.split('.').map(Number)

  switch (versionType) {
    case 'MAJOR':
      return `${major + 1}.0.0`
    case 'MINOR':
      return `${major}.${minor + 1}.0`
    case 'PATCH':
      return `${major}.${minor}.${patch + 1}`
    default:
      return `${major}.${minor + 1}.0`
  }
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function getTimeAgo(date: Date): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) return 'Just now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`
  return date.toLocaleDateString()
}

async function extractAndIndexContent(documentId: string, fileUrl: string, mimeType: string, storeId: string, tx: any) {
  // Placeholder for content extraction and indexing
  // This would integrate with text extraction services
  console.log('Extracting content for document:', documentId)
}

async function notifyUsersAboutNewVersion(documentId: string, version: string, changeLog: string, storeId: string, uploaderId: string) {
  // Get users with access to this document
  const documentAccess = await prisma.documentAccess.findMany({
    where: { documentId },
    include: {
      user: {
        select: { id: true }
      }
    }
  })

  // Create notifications for users with access (except uploader)
  for (const access of documentAccess) {
    if (access.userId !== uploaderId) {
      await prisma.notification.create({
        data: {
          title: 'Document Updated',
          message: `A new version (${version}) of a document has been uploaded. Changes: ${changeLog}`,
          type: 'INFO',
          category: 'SYSTEM',
          userId: access.userId,
          storeId
        }
      })
    }
  }
}
