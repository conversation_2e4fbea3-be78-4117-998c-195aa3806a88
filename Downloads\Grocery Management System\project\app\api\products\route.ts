import { NextRequest, NextResponse } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  getPaginationParams,
  buildSearchFilter,
  createPaginatedResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const productSchema = z.object({
  name: z.string().min(1, 'Product name is required'),
  sku: z.string().min(1, 'SKU is required'),
  description: z.string().optional(),
  barcode: z.string().optional(),
  unit: z.string().default('pcs'),
  costPrice: z.number().min(0, 'Cost price must be positive'),
  sellingPrice: z.number().min(0, 'Selling price must be positive'),
  mrp: z.number().optional(),
  taxRate: z.number().min(0).max(100).default(0),
  minStock: z.number().min(0).default(0),
  categoryId: z.string().min(1, 'Category is required'),
})

export const GET = withPermission('PRODUCT', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { page, limit, skip, search, sortBy, sortOrder } = getPaginationParams(request)

  // Get URL parameters for additional filters
  const url = new URL(request.url)
  const categoryId = url.searchParams.get('categoryId')
  const lowStock = url.searchParams.get('lowStock') === 'true'

  // Build filters
  const where: any = {
    storeId,
    isActive: true,
    ...buildSearchFilter(search, ['name', 'sku', 'barcode', 'description'])
  }

  if (categoryId) {
    where.categoryId = categoryId
  }

  if (lowStock) {
    where.inventory = {
      some: {
        quantity: {
          lte: prisma.product.fields.minStock
        }
      }
    }
  }

  // Get total count
  const total = await prisma.product.count({ where })

  // Get products with pagination
  const products = await prisma.product.findMany({
    where,
    include: {
      category: {
        select: {
          id: true,
          name: true
        }
      },
      inventory: {
        select: {
          quantity: true,
          reorderLevel: true
        }
      }
    },
    orderBy: { [sortBy]: sortOrder },
    skip,
    take: limit
  })

  return createPaginatedResponse(products, page, limit, total)
})

export const POST = withPermission('PRODUCT', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const data = productSchema.parse(body)

  // Validate category exists
  const category = await prisma.category.findFirst({
    where: {
      id: data.categoryId,
      storeId,
      isActive: true
    }
  })

  if (!category) {
    return createErrorResponse('Invalid category selected', 400)
  }

  // Check if SKU already exists
  const existingProduct = await prisma.product.findFirst({
    where: {
      sku: data.sku,
      storeId,
      isActive: true
    }
  })

  if (existingProduct) {
    return createErrorResponse('SKU already exists', 400)
  }

  // Validate selling price vs cost price
  if (data.sellingPrice < data.costPrice) {
    return createErrorResponse('Selling price cannot be less than cost price', 400)
  }

  const product = await prisma.product.create({
    data: {
      ...data,
      storeId
    },
    include: {
      category: {
        select: {
          id: true,
          name: true
        }
      }
    }
  })

  // Create initial inventory record
  await prisma.inventory.create({
    data: {
      productId: product.id,
      storeId,
      quantity: 0,
      reorderLevel: data.minStock
    }
  })

  // Create audit log
  await createAuditLog(
    'CREATE',
    'PRODUCT',
    `Created product: ${product.name} (SKU: ${product.sku})`,
    user.id,
    storeId
  )

  return createSuccessResponse(product, 'Product created successfully')
})