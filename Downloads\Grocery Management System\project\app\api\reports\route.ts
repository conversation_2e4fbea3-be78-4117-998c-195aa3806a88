import { NextRequest, NextResponse } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  validateStoreAccess,
  buildDateRangeFilter
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'

// GET /api/reports - Get various reports based on type
export const GET = withPermission('REPORTS', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const reportType = url.searchParams.get('type')
  const startDate = url.searchParams.get('startDate')
  const endDate = url.searchParams.get('endDate')

  const dateFilter = startDate || endDate ? buildDateRangeFilter(startDate || undefined, endDate || undefined, 'createdAt') : {}

  try {
    switch (reportType) {
      case 'sales-summary':
        return await getSalesSummary(storeId, dateFilter)

      case 'sales-detailed':
        return await getSalesDetailed(storeId, dateFilter)

      case 'inventory-summary':
        return await getInventorySummary(storeId)

      case 'low-stock':
        return await getLowStockReport(storeId)

      case 'top-products':
        return await getTopProductsReport(storeId, dateFilter)

      case 'customer-summary':
        return await getCustomerSummary(storeId, dateFilter)

      case 'financial-summary':
        return await getFinancialSummary(storeId, dateFilter)

      default:
        return createErrorResponse('Invalid report type', 400)
    }
  } catch (error) {
    console.error('Report generation error:', error)
    return createErrorResponse('Failed to generate report', 500)
  }
})

async function getSalesSummary(storeId: string, dateFilter: any) {
  const sales = await prisma.sale.findMany({
    where: {
      storeId,
      ...dateFilter
    },
    include: {
      items: true
    }
  })

  const summary = {
    totalSales: sales.length,
    totalRevenue: sales.reduce((sum, sale) => sum + sale.totalAmount, 0),
    totalTax: sales.reduce((sum, sale) => sum + sale.taxAmount, 0),
    totalDiscount: sales.reduce((sum, sale) => sum + sale.discount, 0),
    totalItems: sales.reduce((sum, sale) => sum + sale.items.length, 0),
    averageOrderValue: sales.length > 0 ? sales.reduce((sum, sale) => sum + sale.totalAmount, 0) / sales.length : 0,
    paymentMethods: sales.reduce((acc: any, sale) => {
      acc[sale.paymentMethod] = (acc[sale.paymentMethod] || 0) + 1
      return acc
    }, {}),
    dailyBreakdown: sales.reduce((acc: any, sale) => {
      const date = sale.createdAt.toISOString().split('T')[0]
      if (!acc[date]) {
        acc[date] = { sales: 0, revenue: 0 }
      }
      acc[date].sales += 1
      acc[date].revenue += sale.totalAmount
      return acc
    }, {})
  }

  return createSuccessResponse(summary)
}

async function getSalesDetailed(storeId: string, dateFilter: any) {
  const sales = await prisma.sale.findMany({
    where: {
      storeId,
      ...dateFilter
    },
    include: {
      customer: {
        select: {
          name: true,
          phone: true
        }
      },
      items: {
        include: {
          product: {
            select: {
              name: true,
              sku: true,
              unit: true
            }
          }
        }
      },
      createdBy: {
        select: {
          name: true
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    }
  })

  return createSuccessResponse(sales)
}

async function getInventorySummary(storeId: string) {
  const products = await prisma.product.findMany({
    where: {
      storeId,
      isActive: true
    },
    include: {
      category: {
        select: {
          name: true
        }
      },
      inventory: {
        where: { storeId },
        select: {
          quantity: true,
          reorderLevel: true
        }
      }
    }
  })

  const summary = {
    totalProducts: products.length,
    totalStockValue: products.reduce((sum, product) => {
      const inventory = product.inventory[0]
      return sum + (inventory ? inventory.quantity * product.costPrice : 0)
    }, 0),
    lowStockItems: products.filter(product => {
      const inventory = product.inventory[0]
      return inventory && inventory.quantity <= product.minStock
    }).length,
    outOfStockItems: products.filter(product => {
      const inventory = product.inventory[0]
      return !inventory || inventory.quantity === 0
    }).length,
    categoryBreakdown: products.reduce((acc: any, product) => {
      const categoryName = product.category?.name || 'Uncategorized'
      if (!acc[categoryName]) {
        acc[categoryName] = { products: 0, stockValue: 0 }
      }
      acc[categoryName].products += 1
      const inventory = product.inventory[0]
      acc[categoryName].stockValue += inventory ? inventory.quantity * product.costPrice : 0
      return acc
    }, {})
  }

  return createSuccessResponse(summary)
}

async function getLowStockReport(storeId: string) {
  const products = await prisma.product.findMany({
    where: {
      storeId,
      isActive: true
    },
    include: {
      category: {
        select: {
          name: true
        }
      },
      inventory: {
        where: { storeId },
        select: {
          quantity: true,
          reorderLevel: true,
          lastUpdated: true
        }
      }
    }
  })

  const lowStockItems = products.filter(product => {
    const inventory = product.inventory[0]
    return inventory && inventory.quantity <= product.minStock
  }).map(product => ({
    id: product.id,
    name: product.name,
    sku: product.sku,
    category: product.category?.name,
    currentStock: product.inventory[0]?.quantity || 0,
    minStock: product.minStock,
    reorderLevel: product.inventory[0]?.reorderLevel || 0,
    unit: product.unit,
    costPrice: product.costPrice,
    lastUpdated: product.inventory[0]?.lastUpdated
  }))

  return createSuccessResponse(lowStockItems)
}

async function getTopProductsReport(storeId: string, dateFilter: any) {
  const saleItems = await prisma.saleItem.findMany({
    where: {
      sale: {
        storeId,
        ...dateFilter
      }
    },
    include: {
      product: {
        select: {
          name: true,
          sku: true,
          unit: true,
          category: {
            select: {
              name: true
            }
          }
        }
      }
    }
  })

  const productStats = saleItems.reduce((acc: any, item) => {
    const productId = item.productId
    if (!acc[productId]) {
      acc[productId] = {
        product: item.product,
        totalQuantity: 0,
        totalRevenue: 0,
        totalSales: 0
      }
    }
    acc[productId].totalQuantity += item.quantity
    acc[productId].totalRevenue += item.totalPrice
    acc[productId].totalSales += 1
    return acc
  }, {})

  const topProducts = Object.values(productStats)
    .sort((a: any, b: any) => b.totalRevenue - a.totalRevenue)
    .slice(0, 20)

  return createSuccessResponse(topProducts)
}

async function getCustomerSummary(storeId: string, dateFilter: any) {
  const customers = await prisma.customer.findMany({
    where: {
      storeId,
      isActive: true
    },
    include: {
      sales: {
        where: dateFilter,
        select: {
          totalAmount: true,
          createdAt: true
        }
      },
      _count: {
        select: {
          sales: true
        }
      }
    }
  })

  const customerStats = customers.map(customer => ({
    id: customer.id,
    name: customer.name,
    phone: customer.phone,
    email: customer.email,
    totalOrders: customer._count.sales,
    totalSpent: customer.sales.reduce((sum, sale) => sum + sale.totalAmount, 0),
    averageOrderValue: customer.sales.length > 0
      ? customer.sales.reduce((sum, sale) => sum + sale.totalAmount, 0) / customer.sales.length
      : 0,
    lastOrderDate: customer.sales.length > 0
      ? Math.max(...customer.sales.map(sale => new Date(sale.createdAt).getTime()))
      : null
  })).sort((a, b) => b.totalSpent - a.totalSpent)

  return createSuccessResponse(customerStats)
}

async function getFinancialSummary(storeId: string, dateFilter: any) {
  const [sales, purchases] = await Promise.all([
    prisma.sale.findMany({
      where: {
        storeId,
        ...dateFilter
      }
    }),
    prisma.purchase.findMany({
      where: {
        storeId,
        ...dateFilter
      }
    })
  ])

  const summary = {
    revenue: {
      total: sales.reduce((sum, sale) => sum + sale.totalAmount, 0),
      tax: sales.reduce((sum, sale) => sum + sale.taxAmount, 0),
      discount: sales.reduce((sum, sale) => sum + sale.discount, 0)
    },
    expenses: {
      total: purchases.reduce((sum, purchase) => sum + purchase.totalAmount, 0),
      tax: purchases.reduce((sum, purchase) => sum + purchase.taxAmount, 0),
      discount: purchases.reduce((sum, purchase) => sum + purchase.discount, 0)
    },
    profit: {
      gross: sales.reduce((sum, sale) => sum + sale.totalAmount, 0) - purchases.reduce((sum, purchase) => sum + purchase.totalAmount, 0),
      margin: sales.length > 0
        ? ((sales.reduce((sum, sale) => sum + sale.totalAmount, 0) - purchases.reduce((sum, purchase) => sum + purchase.totalAmount, 0)) / sales.reduce((sum, sale) => sum + sale.totalAmount, 0)) * 100
        : 0
    },
    transactions: {
      salesCount: sales.length,
      purchasesCount: purchases.length,
      averageSaleValue: sales.length > 0 ? sales.reduce((sum, sale) => sum + sale.totalAmount, 0) / sales.length : 0,
      averagePurchaseValue: purchases.length > 0 ? purchases.reduce((sum, purchase) => sum + purchase.totalAmount, 0) / purchases.length : 0
    }
  }

  return createSuccessResponse(summary)
}
