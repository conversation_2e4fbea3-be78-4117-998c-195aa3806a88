import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  console.log('Middleware: Processing path:', pathname)

  // Skip auth for public routes
  if (
    pathname.startsWith('/api/auth/login') ||
    pathname.startsWith('/api/auth/register') ||
    pathname === '/login' ||
    pathname === '/register' ||
    pathname === '/' ||
    pathname.startsWith('/_next') ||
    pathname.startsWith('/favicon')
  ) {
    console.log('Middleware: Allowing public route:', pathname)
    return NextResponse.next()
  }

  // Check for API routes
  if (pathname.startsWith('/api')) {
    const token = request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // For API routes, just check if token exists (detailed verification in each route)
    if (token && token.length > 10) {
      return NextResponse.next()
    } else {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      )
    }
  }

  // Check for protected pages
  const token = request.cookies.get('auth-token')?.value
  console.log('Middleware: Checking protected page:', pathname)
  console.log('Middleware: Token found in cookies:', !!token)

  if (!token) {
    console.log('Middleware: No token found, redirecting to login')
    return NextResponse.redirect(new URL('/login', request.url))
  }

  // For now, just check if token exists (detailed verification will happen in API routes)
  // This avoids Edge Runtime crypto module issues
  if (token && token.length > 10) {
    console.log('Middleware: Token exists, allowing access')
    return NextResponse.next()
  } else {
    console.log('Middleware: Invalid token, redirecting to login')
    return NextResponse.redirect(new URL('/login', request.url))
  }
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
}