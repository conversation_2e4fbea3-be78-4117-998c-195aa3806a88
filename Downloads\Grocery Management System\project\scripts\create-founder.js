const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createFounder() {
  console.log('🚀 Creating founder user...')

  try {
    // Check if founder already exists
    const existingFounder = await prisma.user.findFirst({
      where: { role: 'FOUNDER' }
    })

    if (existingFounder) {
      console.log('✅ Founder user already exists!')
      console.log('📧 Email:', existingFounder.email)
      return
    }

    // Update existing admin to founder
    const existingAdmin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (existingAdmin) {
      // Update existing admin to founder with full permissions
      const founderPermissions = [
        'DASHBOARD:READ',
        'USER:CREATE', 'USER:READ', 'USER:UPDATE', 'USER:DELETE',
        'STORE:CREATE', 'STORE:READ', 'STORE:UPDATE', 'STORE:DELETE',
        'PRODUCT:CREATE', 'PRODUCT:READ', 'PRODUCT:UPDATE', 'PRODUCT:DELETE',
        'CATEGORY:CREATE', 'CATEGORY:READ', 'CATEGORY:UPDATE', 'CATEGORY:DELETE',
        'INVENTORY:READ', 'INVENTORY:UPDATE',
        'SALES:CREATE', 'SALES:READ', 'SALES:UPDATE', 'SALES:DELETE',
        'PURCHASE:CREATE', 'PURCHASE:READ', 'PURCHASE:UPDATE', 'PURCHASE:DELETE',
        'CUSTOMER:CREATE', 'CUSTOMER:READ', 'CUSTOMER:UPDATE', 'CUSTOMER:DELETE',
        'SUPPLIER:CREATE', 'SUPPLIER:READ', 'SUPPLIER:UPDATE', 'SUPPLIER:DELETE',
        'EXPENSE:CREATE', 'EXPENSE:READ', 'EXPENSE:UPDATE', 'EXPENSE:DELETE',
        'REPORTS:READ',
        'SETTINGS:READ', 'SETTINGS:UPDATE',
        'AUDIT:READ'
      ]

      const updatedUser = await prisma.user.update({
        where: { id: existingAdmin.id },
        data: {
          role: 'FOUNDER',
          permissions: founderPermissions
        }
      })

      console.log('✅ Updated existing admin to founder!')
      console.log('📧 Email: <EMAIL>')
      console.log('🔑 Password: password123')
      console.log('👑 Role: FOUNDER')
      console.log('🔐 Permissions:', founderPermissions.length, 'permissions granted')
      return
    }

    // Create new founder if no existing user
    const hashedPassword = await bcrypt.hash('founder123', 12)

    // Create demo store first if it doesn't exist
    let demoStore = await prisma.store.findFirst()
    
    if (!demoStore) {
      demoStore = await prisma.store.create({
        data: {
          name: 'Demo Grocery Store',
          address: '123 Main Street, Demo City',
          phone: '******-0123',
          email: '<EMAIL>',
          gstNumber: 'GST123456789',
          isActive: true,
          createdBy: {
            create: {
              name: 'System Founder',
              email: '<EMAIL>',
              password: hashedPassword,
              phone: '******-0001',
              role: 'FOUNDER',
              isActive: true,
              permissions: [
                'DASHBOARD:READ',
                'USER:CREATE', 'USER:READ', 'USER:UPDATE', 'USER:DELETE',
                'STORE:CREATE', 'STORE:READ', 'STORE:UPDATE', 'STORE:DELETE',
                'PRODUCT:CREATE', 'PRODUCT:READ', 'PRODUCT:UPDATE', 'PRODUCT:DELETE',
                'CATEGORY:CREATE', 'CATEGORY:READ', 'CATEGORY:UPDATE', 'CATEGORY:DELETE',
                'INVENTORY:READ', 'INVENTORY:UPDATE',
                'SALES:CREATE', 'SALES:READ', 'SALES:UPDATE', 'SALES:DELETE',
                'PURCHASE:CREATE', 'PURCHASE:READ', 'PURCHASE:UPDATE', 'PURCHASE:DELETE',
                'CUSTOMER:CREATE', 'CUSTOMER:READ', 'CUSTOMER:UPDATE', 'CUSTOMER:DELETE',
                'SUPPLIER:CREATE', 'SUPPLIER:READ', 'SUPPLIER:UPDATE', 'SUPPLIER:DELETE',
                'EXPENSE:CREATE', 'EXPENSE:READ', 'EXPENSE:UPDATE', 'EXPENSE:DELETE',
                'REPORTS:READ',
                'SETTINGS:READ', 'SETTINGS:UPDATE',
                'AUDIT:READ'
              ]
            }
          }
        },
        include: {
          createdBy: true
        }
      })

      // Update the user to be associated with the store
      await prisma.user.update({
        where: { id: demoStore.createdById },
        data: {
          storeId: demoStore.id
        }
      })

      console.log('✅ Created new founder user!')
      console.log('📧 Email: <EMAIL>')
      console.log('🔑 Password: founder123')
      console.log('👑 Role: FOUNDER')
      console.log('🏪 Store: Demo Grocery Store')
    }

  } catch (error) {
    console.error('❌ Error creating founder:', error)
    throw error
  }
}

createFounder()
  .catch((e) => {
    console.error('❌ Error:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
