globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/(dashboard)/users/page"]={"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(ssr)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/auth/auth-provider.tsx":{"*":{"id":"(ssr)/./components/auth/auth-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/sonner.tsx":{"*":{"id":"(ssr)/./components/ui/sonner.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(dashboard)/layout.tsx":{"*":{"id":"(ssr)/./app/(dashboard)/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(dashboard)/users/page.tsx":{"*":{"id":"(ssr)/./app/(dashboard)/users/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals:static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals:static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals:static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals:static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals:static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals:static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals:static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals:static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals:static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals:static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\node_modules\\next\\dist\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals:static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\node_modules\\next\\dist\\esm\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals:static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\app\\page.tsx":{"id":"(app-pages-browser)/./app/page.tsx","name":"*","chunks":["app/page:static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout:static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\components\\auth\\auth-provider.tsx":{"id":"(app-pages-browser)/./components/auth/auth-provider.tsx","name":"*","chunks":["app/layout:static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\components\\ui\\sonner.tsx":{"id":"(app-pages-browser)/./components/ui/sonner.tsx","name":"*","chunks":["app/layout:static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout:static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\app\\(dashboard)\\layout.tsx":{"id":"(app-pages-browser)/./app/(dashboard)/layout.tsx","name":"*","chunks":["app/(dashboard)/layout:static/chunks/app/(dashboard)/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\app\\(dashboard)\\users\\page.tsx":{"id":"(app-pages-browser)/./app/(dashboard)/users/page.tsx","name":"*","chunks":["app/(dashboard)/users/page:static/chunks/app/(dashboard)/users/page.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\app\\page":[],"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\app\\(dashboard)\\layout":[],"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\app\\not-found":[],"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\app\\(dashboard)\\users\\page":[]}}