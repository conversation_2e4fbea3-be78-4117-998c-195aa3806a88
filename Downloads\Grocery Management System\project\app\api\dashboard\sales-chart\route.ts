import { NextRequest, NextResponse } from 'next/server'
import { getAuthUser } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const user = await getAuthUser(request)
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const storeId = user.storeId

    if (!storeId) {
      return NextResponse.json({ error: 'Store not found' }, { status: 400 })
    }

    // Get last 7 days
    const days = []
    for (let i = 6; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      date.setHours(0, 0, 0, 0)
      days.push(date)
    }

    const salesData = await Promise.all(
      days.map(async (date) => {
        const nextDay = new Date(date)
        nextDay.setDate(nextDay.getDate() + 1)

        const [sales, purchases] = await Promise.all([
          prisma.sale.aggregate({
            where: {
              storeId,
              status: 'COMPLETED',
              createdAt: {
                gte: date,
                lt: nextDay
              }
            },
            _sum: { totalAmount: true }
          }),
          prisma.purchase.aggregate({
            where: {
              storeId,
              createdAt: {
                gte: date,
                lt: nextDay
              }
            },
            _sum: { totalAmount: true }
          })
        ])

        return {
          date: date.toISOString().split('T')[0],
          sales: sales._sum.totalAmount || 0,
          purchases: purchases._sum.totalAmount || 0
        }
      })
    )

    return NextResponse.json(salesData)
  } catch (error) {
    console.error('Sales chart error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}