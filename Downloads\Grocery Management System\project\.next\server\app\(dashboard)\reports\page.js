/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(dashboard)/reports/page";
exports.ids = ["app/(dashboard)/reports/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(dashboard)%2Freports%2Fpage&page=%2F(dashboard)%2Freports%2Fpage&appPaths=%2F(dashboard)%2Freports%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Freports%2Fpage.tsx&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(dashboard)%2Freports%2Fpage&page=%2F(dashboard)%2Freports%2Fpage&appPaths=%2F(dashboard)%2Freports%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Freports%2Fpage.tsx&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?b6e7\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n// @ts-ignore this need to be imported from next/dist to be external\n\n\nconst AppPageRouteModule = next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule;\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(dashboard)',\n        {\n        children: [\n        'reports',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(dashboard)/reports/page.tsx */ \"(rsc)/./app/(dashboard)/reports/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(dashboard)/layout.tsx */ \"(rsc)/./app/(dashboard)/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\"];\n\n// @ts-expect-error - replaced by webpack/turbopack loader\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/(dashboard)/reports/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(dashboard)/reports/page\",\n        pathname: \"/reports\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(dashboard)%2Freports%2Fpage&page=%2F(dashboard)%2Freports%2Fpage&appPaths=%2F(dashboard)%2Freports%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Freports%2Fpage.tsx&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp%5C(dashboard)%5Clayout.tsx&server=true!":
/*!*************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp%5C(dashboard)%5Clayout.tsx&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(dashboard)/layout.tsx */ \"(ssr)/./app/(dashboard)/layout.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDREVMTCU1Q0Rvd25sb2FkcyU1Q0dyb2NlcnklMjBNYW5hZ2VtZW50JTIwU3lzdGVtJTVDcHJvamVjdCU1Q2FwcCU1QyhkYXNoYm9hcmQpJTVDbGF5b3V0LnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ncm9jZXJ5LW1hbmFnZW1lbnQtc3lzdGVtLz9jZTNjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcREVMTFxcXFxEb3dubG9hZHNcXFxcR3JvY2VyeSBNYW5hZ2VtZW50IFN5c3RlbVxcXFxwcm9qZWN0XFxcXGFwcFxcXFwoZGFzaGJvYXJkKVxcXFxsYXlvdXQudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp%5C(dashboard)%5Clayout.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp%5C(dashboard)%5Creports%5Cpage.tsx&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp%5C(dashboard)%5Creports%5Cpage.tsx&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(dashboard)/reports/page.tsx */ \"(ssr)/./app/(dashboard)/reports/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDREVMTCU1Q0Rvd25sb2FkcyU1Q0dyb2NlcnklMjBNYW5hZ2VtZW50JTIwU3lzdGVtJTVDcHJvamVjdCU1Q2FwcCU1QyhkYXNoYm9hcmQpJTVDcmVwb3J0cyU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2dyb2NlcnktbWFuYWdlbWVudC1zeXN0ZW0vPzAwMjkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxERUxMXFxcXERvd25sb2Fkc1xcXFxHcm9jZXJ5IE1hbmFnZW1lbnQgU3lzdGVtXFxcXHByb2plY3RcXFxcYXBwXFxcXChkYXNoYm9hcmQpXFxcXHJlcG9ydHNcXFxccGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp%5C(dashboard)%5Creports%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Ccomponents%5Cauth%5Cauth-provider.tsx&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Ccomponents%5Cui%5Csonner.tsx&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Ccomponents%5Cauth%5Cauth-provider.tsx&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Ccomponents%5Cui%5Csonner.tsx&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/auth/auth-provider.tsx */ \"(ssr)/./components/auth/auth-provider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/sonner.tsx */ \"(ssr)/./components/ui/sonner.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDREVMTCU1Q0Rvd25sb2FkcyU1Q0dyb2NlcnklMjBNYW5hZ2VtZW50JTIwU3lzdGVtJTVDcHJvamVjdCU1Q2FwcCU1Q2dsb2JhbHMuY3NzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDREVMTCU1Q0Rvd25sb2FkcyU1Q0dyb2NlcnklMjBNYW5hZ2VtZW50JTIwU3lzdGVtJTVDcHJvamVjdCU1Q2NvbXBvbmVudHMlNUNhdXRoJTVDYXV0aC1wcm92aWRlci50c3gmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNERUxMJTVDRG93bmxvYWRzJTVDR3JvY2VyeSUyME1hbmFnZW1lbnQlMjBTeXN0ZW0lNUNwcm9qZWN0JTVDY29tcG9uZW50cyU1Q3VpJTVDc29ubmVyLnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0RFTEwlNUNEb3dubG9hZHMlNUNHcm9jZXJ5JTIwTWFuYWdlbWVudCUyMFN5c3RlbSU1Q3Byb2plY3QlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZm9udCU1Q2dvb2dsZSU1Q3RhcmdldC5jc3MlM0YlN0IlMjJwYXRoJTIyJTNBJTIyYXBwJTVDJTVDbGF5b3V0LnRzeCUyMiUyQyUyMmltcG9ydCUyMiUzQSUyMkludGVyJTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmxhdGluJTIyJTVEJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtMQUF3STtBQUN4SSIsInNvdXJjZXMiOlsid2VicGFjazovL2dyb2NlcnktbWFuYWdlbWVudC1zeXN0ZW0vPzk5OGQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxERUxMXFxcXERvd25sb2Fkc1xcXFxHcm9jZXJ5IE1hbmFnZW1lbnQgU3lzdGVtXFxcXHByb2plY3RcXFxcY29tcG9uZW50c1xcXFxhdXRoXFxcXGF1dGgtcHJvdmlkZXIudHN4XCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxERUxMXFxcXERvd25sb2Fkc1xcXFxHcm9jZXJ5IE1hbmFnZW1lbnQgU3lzdGVtXFxcXHByb2plY3RcXFxcY29tcG9uZW50c1xcXFx1aVxcXFxzb25uZXIudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Ccomponents%5Cauth%5Cauth-provider.tsx&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Ccomponents%5Cui%5Csonner.tsx&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/(dashboard)/layout.tsx":
/*!************************************!*\
  !*** ./app/(dashboard)/layout.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/shared/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_auth_auth_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/auth/auth-provider */ \"(ssr)/./components/auth/auth-provider.tsx\");\n/* harmony import */ var _components_layout_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/sidebar */ \"(ssr)/./components/layout/sidebar.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DashboardLayout({ children }) {\n    const { user, logout, isLoading } = (0,_components_auth_auth_provider__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        console.log(\"Dashboard Layout: isLoading:\", isLoading, \"user:\", !!user);\n        if (!isLoading && !user) {\n            console.log(\"Dashboard Layout: No user found, redirecting to login\");\n            router.push(\"/login\");\n        }\n    }, [\n        user,\n        isLoading,\n        router\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-muted-foreground\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\layout.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\layout.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_sidebar__WEBPACK_IMPORTED_MODULE_2__.Sidebar, {\n                userRole: user.role,\n                onLogout: logout\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\layout.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 overflow-y-auto\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\layout.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\layout.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/(dashboard)/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./app/(dashboard)/reports/page.tsx":
/*!******************************************!*\
  !*** ./app/(dashboard)/reports/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReportsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/shared/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_auth_auth_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth/auth-provider */ \"(ssr)/./components/auth/auth-provider.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(ssr)/./components/ui/select.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_TrendingUp_Download_DollarSign_Package_Users_AlertTriangle_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,TrendingUp,Download,DollarSign,Package,Users,AlertTriangle,FileText!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_TrendingUp_Download_DollarSign_Package_Users_AlertTriangle_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,TrendingUp,Download,DollarSign,Package,Users,AlertTriangle,FileText!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_TrendingUp_Download_DollarSign_Package_Users_AlertTriangle_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,TrendingUp,Download,DollarSign,Package,Users,AlertTriangle,FileText!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_TrendingUp_Download_DollarSign_Package_Users_AlertTriangle_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,TrendingUp,Download,DollarSign,Package,Users,AlertTriangle,FileText!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_TrendingUp_Download_DollarSign_Package_Users_AlertTriangle_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,TrendingUp,Download,DollarSign,Package,Users,AlertTriangle,FileText!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_TrendingUp_Download_DollarSign_Package_Users_AlertTriangle_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,TrendingUp,Download,DollarSign,Package,Users,AlertTriangle,FileText!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_TrendingUp_Download_DollarSign_Package_Users_AlertTriangle_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,TrendingUp,Download,DollarSign,Package,Users,AlertTriangle,FileText!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_TrendingUp_Download_DollarSign_Package_Users_AlertTriangle_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,TrendingUp,Download,DollarSign,Package,Users,AlertTriangle,FileText!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction ReportsPage() {\n    const { token } = (0,_components_auth_auth_provider__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [reportType, setReportType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"sales-summary\");\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split(\"T\")[0],\n        endDate: new Date().toISOString().split(\"T\")[0]\n    });\n    const [reportData, setReportData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const reportTypes = [\n        {\n            value: \"sales-summary\",\n            label: \"Sales Summary\",\n            icon: _barrel_optimize_names_BarChart3_TrendingUp_Download_DollarSign_Package_Users_AlertTriangle_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            value: \"sales-detailed\",\n            label: \"Detailed Sales Report\",\n            icon: _barrel_optimize_names_BarChart3_TrendingUp_Download_DollarSign_Package_Users_AlertTriangle_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            value: \"inventory-summary\",\n            label: \"Inventory Summary\",\n            icon: _barrel_optimize_names_BarChart3_TrendingUp_Download_DollarSign_Package_Users_AlertTriangle_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        },\n        {\n            value: \"low-stock\",\n            label: \"Low Stock Report\",\n            icon: _barrel_optimize_names_BarChart3_TrendingUp_Download_DollarSign_Package_Users_AlertTriangle_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        },\n        {\n            value: \"top-products\",\n            label: \"Top Products\",\n            icon: _barrel_optimize_names_BarChart3_TrendingUp_Download_DollarSign_Package_Users_AlertTriangle_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            value: \"customer-summary\",\n            label: \"Customer Summary\",\n            icon: _barrel_optimize_names_BarChart3_TrendingUp_Download_DollarSign_Package_Users_AlertTriangle_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        },\n        {\n            value: \"financial-summary\",\n            label: \"Financial Summary\",\n            icon: _barrel_optimize_names_BarChart3_TrendingUp_Download_DollarSign_Package_Users_AlertTriangle_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n        }\n    ];\n    const generateReport = async ()=>{\n        if (!token) return;\n        try {\n            setLoading(true);\n            const params = new URLSearchParams({\n                type: reportType,\n                startDate: dateRange.startDate,\n                endDate: dateRange.endDate\n            });\n            const response = await fetch(`/api/reports?${params}`, {\n                headers: {\n                    Authorization: `Bearer ${token}`\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to generate report\");\n            }\n            const result = await response.json();\n            setReportData(result.data);\n        } catch (error) {\n            console.error(\"Error generating report:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Failed to generate report\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const exportReport = ()=>{\n        if (!reportData) return;\n        const dataStr = JSON.stringify(reportData, null, 2);\n        const dataBlob = new Blob([\n            dataStr\n        ], {\n            type: \"application/json\"\n        });\n        const url = URL.createObjectURL(dataBlob);\n        const link = document.createElement(\"a\");\n        link.href = url;\n        link.download = `${reportType}-${dateRange.startDate}-to-${dateRange.endDate}.json`;\n        link.click();\n        URL.revokeObjectURL(url);\n    };\n    const renderSalesSummary = (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"text-sm\",\n                                children: \"Total Sales\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: data.totalSales\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"transactions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"text-sm\",\n                                children: \"Total Revenue\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: [\n                                        \"₹\",\n                                        data.totalRevenue.toFixed(2)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"gross revenue\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"text-sm\",\n                                children: \"Average Order Value\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: [\n                                        \"₹\",\n                                        data.averageOrderValue.toFixed(2)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"per transaction\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"text-sm\",\n                                children: \"Total Tax\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: [\n                                        \"₹\",\n                                        data.totalTax.toFixed(2)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"tax collected\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"text-sm\",\n                                children: \"Total Discount\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: [\n                                        \"₹\",\n                                        data.totalDiscount.toFixed(2)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"discounts given\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"text-sm\",\n                                children: \"Items Sold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: data.totalItems\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"total items\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    className: \"md:col-span-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                children: \"Payment Methods\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                children: Object.entries(data.paymentMethods).map(([method, count])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-bold\",\n                                                children: count\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: method\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, method, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n            lineNumber: 92,\n            columnNumber: 5\n        }, this);\n    const renderInventorySummary = (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"text-sm\",\n                                children: \"Total Products\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: data.totalProducts\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"in inventory\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"text-sm\",\n                                children: \"Stock Value\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: [\n                                        \"₹\",\n                                        data.totalStockValue.toFixed(2)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"total value\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"text-sm\",\n                                children: \"Low Stock Items\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-orange-600\",\n                                    children: data.lowStockItems\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"need reorder\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"text-sm\",\n                                children: \"Out of Stock\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-red-600\",\n                                    children: data.outOfStockItems\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"zero quantity\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    className: \"md:col-span-2 lg:col-span-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                children: \"Category Breakdown\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: Object.entries(data.categoryBreakdown).map(([category, stats])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center p-2 border rounded\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: category\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            stats.products,\n                                                            \" products\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: [\n                                                            \"₹\",\n                                                            stats.stockValue.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, category, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n            lineNumber: 173,\n            columnNumber: 5\n        }, this);\n    const renderLowStockReport = (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                        children: \"Low Stock Items\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"border-b\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left p-2\",\n                                                children: \"Product\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left p-2\",\n                                                children: \"SKU\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left p-2\",\n                                                children: \"Category\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-right p-2\",\n                                                children: \"Current Stock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-right p-2\",\n                                                children: \"Min Stock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-right p-2\",\n                                                children: \"Cost Price\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: data.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"border-b\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"p-2 font-medium\",\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"p-2 text-sm font-mono\",\n                                                    children: item.sku\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"p-2 text-sm\",\n                                                    children: item.category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"p-2 text-right text-orange-600 font-medium\",\n                                                    children: [\n                                                        item.currentStock,\n                                                        \" \",\n                                                        item.unit\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"p-2 text-right\",\n                                                    children: [\n                                                        item.minStock,\n                                                        \" \",\n                                                        item.unit\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"p-2 text-right\",\n                                                    children: [\n                                                        \"₹\",\n                                                        item.costPrice.toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n            lineNumber: 237,\n            columnNumber: 5\n        }, this);\n    const renderTopProducts = (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                        children: \"Top Selling Products\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"border-b\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left p-2\",\n                                                children: \"Product\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left p-2\",\n                                                children: \"Category\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-right p-2\",\n                                                children: \"Quantity Sold\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-right p-2\",\n                                                children: \"Revenue\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-right p-2\",\n                                                children: \"Sales Count\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: data.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"border-b\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"p-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium\",\n                                                            children: item.product.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: item.product.sku\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"p-2 text-sm\",\n                                                    children: item.product.category?.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"p-2 text-right font-medium\",\n                                                    children: [\n                                                        item.totalQuantity,\n                                                        \" \",\n                                                        item.product.unit\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"p-2 text-right font-medium\",\n                                                    children: [\n                                                        \"₹\",\n                                                        item.totalRevenue.toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"p-2 text-right\",\n                                                    children: item.totalSales\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n            lineNumber: 275,\n            columnNumber: 5\n        }, this);\n    const renderFinancialSummary = (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                children: \"Revenue\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Total Revenue:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold\",\n                                            children: [\n                                                \"₹\",\n                                                data.revenue.total.toFixed(2)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Tax Collected:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"₹\",\n                                                data.revenue.tax.toFixed(2)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Discounts Given:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"₹\",\n                                                data.revenue.discount.toFixed(2)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                    lineNumber: 315,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                children: \"Expenses\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Total Expenses:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold\",\n                                            children: [\n                                                \"₹\",\n                                                data.expenses.total.toFixed(2)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Tax Paid:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"₹\",\n                                                data.expenses.tax.toFixed(2)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Discounts Received:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"₹\",\n                                                data.expenses.discount.toFixed(2)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                    lineNumber: 335,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                children: \"Profit\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Gross Profit:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `font-bold ${data.profit.gross >= 0 ? \"text-green-600\" : \"text-red-600\"}`,\n                                            children: [\n                                                \"₹\",\n                                                data.profit.gross.toFixed(2)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Profit Margin:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `font-bold ${data.profit.margin >= 0 ? \"text-green-600\" : \"text-red-600\"}`,\n                                            children: [\n                                                data.profit.margin.toFixed(2),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                    lineNumber: 355,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                children: \"Transactions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Sales Count:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold\",\n                                            children: data.transactions.salesCount\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Purchases Count:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold\",\n                                            children: data.transactions.purchasesCount\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Avg Sale Value:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"₹\",\n                                                data.transactions.averageSaleValue.toFixed(2)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Avg Purchase Value:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"₹\",\n                                                data.transactions.averagePurchaseValue.toFixed(2)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                    lineNumber: 375,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n            lineNumber: 314,\n            columnNumber: 5\n        }, this);\n    const renderReportData = ()=>{\n        if (!reportData) return null;\n        switch(reportType){\n            case \"sales-summary\":\n                return renderSalesSummary(reportData);\n            case \"inventory-summary\":\n                return renderInventorySummary(reportData);\n            case \"low-stock\":\n                return renderLowStockReport(reportData);\n            case \"top-products\":\n                return renderTopProducts(reportData);\n            case \"financial-summary\":\n                return renderFinancialSummary(reportData);\n            case \"sales-detailed\":\n            case \"customer-summary\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                children: \"Report Data\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"text-sm overflow-auto max-h-96\",\n                                children: JSON.stringify(reportData, null, 2)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                    lineNumber: 418,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        generateReport();\n    }, [\n        token\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold\",\n                            children: \"Reports\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 443,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Generate and view business reports\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                    lineNumber: 442,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                lineNumber: 441,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            children: \"Generate Report\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                        lineNumber: 450,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            htmlFor: \"reportType\",\n                                            children: \"Report Type\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                            value: reportType,\n                                            onValueChange: setReportType,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                    children: reportTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                            value: type.value,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(type.icon, {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                                        lineNumber: 465,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    type.label\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, type.value, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                            lineNumber: 463,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            htmlFor: \"startDate\",\n                                            children: \"Start Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            id: \"startDate\",\n                                            type: \"date\",\n                                            value: dateRange.startDate,\n                                            onChange: (e)=>setDateRange((prev)=>({\n                                                        ...prev,\n                                                        startDate: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            htmlFor: \"endDate\",\n                                            children: \"End Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            id: \"endDate\",\n                                            type: \"date\",\n                                            value: dateRange.endDate,\n                                            onChange: (e)=>setDateRange((prev)=>({\n                                                        ...prev,\n                                                        endDate: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-end gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: generateReport,\n                                            disabled: loading,\n                                            className: \"flex-1\",\n                                            children: loading ? \"Generating...\" : \"Generate Report\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 15\n                                        }, this),\n                                        reportData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            onClick: exportReport,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_TrendingUp_Download_DollarSign_Package_Users_AlertTriangle_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                lineNumber: 449,\n                columnNumber: 7\n            }, this),\n            reportData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold\",\n                                children: reportTypes.find((t)=>t.value === reportType)?.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: [\n                                    dateRange.startDate,\n                                    \" to \",\n                                    dateRange.endDate\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                                lineNumber: 515,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                        lineNumber: 511,\n                        columnNumber: 11\n                    }, this),\n                    renderReportData()\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n                lineNumber: 510,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\reports\\\\page.tsx\",\n        lineNumber: 439,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/(dashboard)/reports/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/auth/auth-provider.tsx":
/*!*******************************************!*\
  !*** ./components/auth/auth-provider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/shared/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"Auth: Initializing auth provider\");\n        const savedToken = localStorage.getItem(\"auth-token\");\n        const savedUser = localStorage.getItem(\"auth-user\");\n        console.log(\"Auth: Saved token exists:\", !!savedToken);\n        console.log(\"Auth: Saved user exists:\", !!savedUser);\n        if (savedToken && savedUser) {\n            console.log(\"Auth: Restoring user session\");\n            setToken(savedToken);\n            setUser(JSON.parse(savedUser));\n            // Ensure cookie is set if localStorage has token\n            document.cookie = `auth-token=${savedToken}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Lax`;\n        }\n        setIsLoading(false);\n        console.log(\"Auth: Auth provider initialized\");\n    }, []);\n    const login = async (email, password)=>{\n        try {\n            const response = await fetch(\"/api/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.error || \"Login failed\");\n            }\n            const data = await response.json();\n            console.log(\"Auth: Login successful, setting user and token\");\n            setUser(data.user);\n            setToken(data.token);\n            // Store in localStorage\n            localStorage.setItem(\"auth-token\", data.token);\n            localStorage.setItem(\"auth-user\", JSON.stringify(data.user));\n            // Also set cookie for middleware\n            const maxAge = 7 * 24 * 60 * 60 // 7 days\n            ;\n            const cookieValue = `auth-token=${data.token}; path=/; max-age=${maxAge}; SameSite=Lax`;\n            document.cookie = cookieValue;\n            console.log(\"Auth: Cookie set:\", cookieValue);\n            // Verify cookie was set\n            setTimeout(()=>{\n                const cookies = document.cookie;\n                console.log(\"Auth: All cookies after setting:\", cookies);\n                console.log(\"Auth: Token cookie exists:\", cookies.includes(\"auth-token\"));\n            }, 50);\n        } catch (error) {\n            throw error;\n        }\n    };\n    const logout = ()=>{\n        setUser(null);\n        setToken(null);\n        localStorage.removeItem(\"auth-token\");\n        localStorage.removeItem(\"auth-user\");\n        // Clear cookie\n        document.cookie = \"auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            token,\n            login,\n            logout,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\auth\\\\auth-provider.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/auth/auth-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/sidebar.tsx":
/*!***************************************!*\
  !*** ./components/layout/sidebar.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/shared/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(ssr)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,HelpCircle,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,HelpCircle,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,HelpCircle,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,HelpCircle,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,HelpCircle,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,HelpCircle,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/tags.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,HelpCircle,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/warehouse.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,HelpCircle,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,HelpCircle,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/receipt.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,HelpCircle,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,HelpCircle,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,HelpCircle,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,HelpCircle,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,HelpCircle,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,HelpCircle,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,HelpCircle,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,HelpCircle,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,HelpCircle,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,HelpCircle,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,HelpCircle,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,HelpCircle,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,HelpCircle,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,HelpCircle,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _lib_permissions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/permissions */ \"(ssr)/./lib/permissions.ts\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\n\n\n\n\nconst navigationItems = [\n    {\n        title: \"Dashboard\",\n        href: \"/dashboard\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        module: \"DASHBOARD\"\n    },\n    {\n        title: \"Store Management\",\n        href: \"/stores\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        module: \"STORE\"\n    },\n    {\n        title: \"User Management\",\n        href: \"/users\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        module: \"USER\"\n    },\n    {\n        title: \"Permissions\",\n        href: \"/permissions\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        module: \"ROLE\"\n    },\n    {\n        title: \"Products\",\n        href: \"/products\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        module: \"PRODUCT\",\n        children: [\n            {\n                title: \"All Products\",\n                href: \"/products\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                module: \"PRODUCT\"\n            },\n            {\n                title: \"Categories\",\n                href: \"/categories\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                module: \"CATEGORY\"\n            },\n            {\n                title: \"Inventory\",\n                href: \"/inventory\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                module: \"INVENTORY\"\n            }\n        ]\n    },\n    {\n        title: \"Sales\",\n        href: \"/sales\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        module: \"SALES\",\n        children: [\n            {\n                title: \"Point of Sale\",\n                href: \"/pos\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                module: \"SALES\"\n            },\n            {\n                title: \"Sales History\",\n                href: \"/sales\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                module: \"SALES\"\n            },\n            {\n                title: \"Sales Returns\",\n                href: \"/sales-returns\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                module: \"SALES_RETURN\"\n            }\n        ]\n    },\n    {\n        title: \"Purchases\",\n        href: \"/purchases\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n        module: \"PURCHASE\",\n        children: [\n            {\n                title: \"Purchase Orders\",\n                href: \"/purchases\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                module: \"PURCHASE\"\n            },\n            {\n                title: \"Purchase Returns\",\n                href: \"/purchase-returns\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                module: \"PURCHASE_RETURN\"\n            }\n        ]\n    },\n    {\n        title: \"Customers\",\n        href: \"/customers\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        module: \"CUSTOMER\"\n    },\n    {\n        title: \"Suppliers\",\n        href: \"/suppliers\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n        module: \"SUPPLIER\"\n    },\n    {\n        title: \"Expenses\",\n        href: \"/expenses\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n        module: \"EXPENSE\"\n    },\n    {\n        title: \"B2B Orders\",\n        href: \"/b2b\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n        module: \"B2B\"\n    },\n    {\n        title: \"Reports\",\n        href: \"/reports\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n        module: \"REPORTS\"\n    },\n    {\n        title: \"Notifications\",\n        href: \"/notifications\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n        module: \"NOTIFICATION\"\n    },\n    {\n        title: \"Documents\",\n        href: \"/documents\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n        module: \"DOCUMENT\"\n    },\n    {\n        title: \"Support\",\n        href: \"/support\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n        module: \"SUPPORT\"\n    },\n    {\n        title: \"Settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n        module: \"SETTINGS\"\n    }\n];\nfunction Sidebar({ userRole, onLogout }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [expandedItems, setExpandedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const userModules = (0,_lib_permissions__WEBPACK_IMPORTED_MODULE_7__.getUserModules)(userRole);\n    const filteredNavItems = navigationItems.filter((item)=>userModules.includes(item.module));\n    const toggleExpanded = (title)=>{\n        setExpandedItems((prev)=>prev.includes(title) ? prev.filter((item)=>item !== title) : [\n                ...prev,\n                title\n            ]);\n    };\n    const NavLink = ({ item, isChild = false })=>{\n        const isActive = pathname === item.href;\n        const hasChildren = item.children && item.children.length > 0;\n        const isExpanded = expandedItems.includes(item.title);\n        if (hasChildren) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"w-full justify-between h-12 px-4\", isChild && \"pl-8\", \"text-left font-normal hover:bg-accent hover:text-accent-foreground\"),\n                        onClick: ()=>toggleExpanded(item.title),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                        className: \"mr-3 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this),\n                                    item.title\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"h-4 w-4 transition-transform\", isExpanded && \"rotate-180\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this),\n                    isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1 ml-4\",\n                        children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                item: child,\n                                isChild: true\n                            }, child.href, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                lineNumber: 190,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n            variant: isActive ? \"secondary\" : \"ghost\",\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"w-full justify-start h-12 px-4\", isChild && \"pl-8\", isActive && \"bg-secondary text-secondary-foreground\", \"text-left font-normal hover:bg-accent hover:text-accent-foreground\"),\n            asChild: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                href: item.href,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                        className: \"mr-3 h-5 w-5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this),\n                    item.title\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                lineNumber: 231,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n            lineNumber: 221,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                variant: \"ghost\",\n                size: \"icon\",\n                className: \"fixed top-4 left-4 z-50 md:hidden\",\n                onClick: ()=>setIsOpen(!isOpen),\n                children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 19\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 47\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/20 z-40 md:hidden\",\n                onClick: ()=>setIsOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                lineNumber: 253,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"fixed left-0 top-0 z-40 h-full w-72 bg-background border-r transform transition-transform duration-200 ease-in-out md:relative md:transform-none\", isOpen ? \"translate-x-0\" : \"-translate-x-full md:translate-x-0\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-full flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex h-16 items-center border-b px-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-6 w-6 text-primary\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: \"GroceryPOS\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                            className: \"flex-1 px-4 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"space-y-2\",\n                                children: filteredNavItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                        item: item\n                                    }, item.href, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 w-8 rounded-full bg-primary flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                className: \"h-4 w-4 text-primary-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium truncate\",\n                                                    children: \"Current Role\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: userRole.replace(\"_\", \" \")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"w-full justify-start text-red-600 hover:text-red-600 hover:bg-red-50\",\n                                    onClick: onLogout,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_HelpCircle_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Sign Out\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                lineNumber: 260,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/shared/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/shared/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/shared/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQStCO0FBRUU7QUFLakMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLHFYQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ3JvY2VyeS1tYW5hZ2VtZW50LXN5c3RlbS8uL2NvbXBvbmVudHMvdWkvaW5wdXQudHN4P2RhNzkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuXG5pbXBvcnQgeyBjbiB9IGZyb20gJ0AvbGliL3V0aWxzJztcblxuZXhwb3J0IGludGVyZmFjZSBJbnB1dFByb3BzXG4gIGV4dGVuZHMgUmVhY3QuSW5wdXRIVE1MQXR0cmlidXRlczxIVE1MSW5wdXRFbGVtZW50PiB7fVxuXG5jb25zdCBJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgSW5wdXRQcm9wcz4oXG4gICh7IGNsYXNzTmFtZSwgdHlwZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxpbnB1dFxuICAgICAgICB0eXBlPXt0eXBlfVxuICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICdmbGV4IGgtMTAgdy1mdWxsIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1pbnB1dCBiZy1iYWNrZ3JvdW5kIHB4LTMgcHktMiB0ZXh0LXNtIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIGZpbGU6dGV4dC1mb3JlZ3JvdW5kIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTAnLFxuICAgICAgICAgIGNsYXNzTmFtZVxuICAgICAgICApfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApO1xuICB9XG4pO1xuSW5wdXQuZGlzcGxheU5hbWUgPSAnSW5wdXQnO1xuXG5leHBvcnQgeyBJbnB1dCB9O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJJbnB1dCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJ0eXBlIiwicHJvcHMiLCJyZWYiLCJpbnB1dCIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/label.tsx":
/*!*********************************!*\
  !*** ./components/ui/label.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/shared/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined));\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2xhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFK0I7QUFDeUI7QUFDVTtBQUVqQztBQUVqQyxNQUFNSSxnQkFBZ0JGLDZEQUFHQSxDQUN2QjtBQUdGLE1BQU1HLHNCQUFRTCw2Q0FBZ0IsQ0FJNUIsQ0FBQyxFQUFFTyxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNSLHVEQUFtQjtRQUNsQlEsS0FBS0E7UUFDTEYsV0FBV0osOENBQUVBLENBQUNDLGlCQUFpQkc7UUFDOUIsR0FBR0MsS0FBSzs7Ozs7O0FBR2JILE1BQU1NLFdBQVcsR0FBR1YsdURBQW1CLENBQUNVLFdBQVc7QUFFbEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ncm9jZXJ5LW1hbmFnZW1lbnQtc3lzdGVtLy4vY29tcG9uZW50cy91aS9sYWJlbC50c3g/ODhlZCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCAqIGFzIExhYmVsUHJpbWl0aXZlIGZyb20gJ0ByYWRpeC11aS9yZWFjdC1sYWJlbCc7XG5pbXBvcnQgeyBjdmEsIHR5cGUgVmFyaWFudFByb3BzIH0gZnJvbSAnY2xhc3MtdmFyaWFuY2UtYXV0aG9yaXR5JztcblxuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscyc7XG5cbmNvbnN0IGxhYmVsVmFyaWFudHMgPSBjdmEoXG4gICd0ZXh0LXNtIGZvbnQtbWVkaXVtIGxlYWRpbmctbm9uZSBwZWVyLWRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBwZWVyLWRpc2FibGVkOm9wYWNpdHktNzAnXG4pO1xuXG5jb25zdCBMYWJlbCA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIExhYmVsUHJpbWl0aXZlLlJvb3Q+LFxuICBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIExhYmVsUHJpbWl0aXZlLlJvb3Q+ICZcbiAgICBWYXJpYW50UHJvcHM8dHlwZW9mIGxhYmVsVmFyaWFudHM+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxMYWJlbFByaW1pdGl2ZS5Sb290XG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihsYWJlbFZhcmlhbnRzKCksIGNsYXNzTmFtZSl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSk7XG5MYWJlbC5kaXNwbGF5TmFtZSA9IExhYmVsUHJpbWl0aXZlLlJvb3QuZGlzcGxheU5hbWU7XG5cbmV4cG9ydCB7IExhYmVsIH07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJMYWJlbFByaW1pdGl2ZSIsImN2YSIsImNuIiwibGFiZWxWYXJpYW50cyIsIkxhYmVsIiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInByb3BzIiwicmVmIiwiUm9vdCIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/scroll-area.tsx":
/*!***************************************!*\
  !*** ./components/ui/scroll-area.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScrollArea: () => (/* binding */ ScrollArea),\n/* harmony export */   ScrollBar: () => (/* binding */ ScrollBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/shared/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-scroll-area */ \"(ssr)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ScrollArea,ScrollBar auto */ \n\n\n\nconst ScrollArea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative overflow-hidden\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Viewport, {\n                className: \"h-full w-full rounded-[inherit]\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 17,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScrollBar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 20,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Corner, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 21,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\scroll-area.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined));\nScrollArea.displayName = _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\nconst ScrollBar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, orientation = \"vertical\", ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaScrollbar, {\n        ref: ref,\n        orientation: orientation,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex touch-none select-none transition-colors\", orientation === \"vertical\" && \"h-full w-2.5 border-l border-l-transparent p-[1px]\", orientation === \"horizontal\" && \"h-2.5 flex-col border-t border-t-transparent p-[1px]\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaThumb, {\n            className: \"relative flex-1 rounded-full bg-border\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\scroll-area.tsx\",\n            lineNumber: 43,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\scroll-area.tsx\",\n        lineNumber: 30,\n        columnNumber: 3\n    }, undefined));\nScrollBar.displayName = _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaScrollbar.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/scroll-area.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/select.tsx":
/*!**********************************!*\
  !*** ./components/ui/select.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Select: () => (/* binding */ Select),\n/* harmony export */   SelectContent: () => (/* binding */ SelectContent),\n/* harmony export */   SelectGroup: () => (/* binding */ SelectGroup),\n/* harmony export */   SelectItem: () => (/* binding */ SelectItem),\n/* harmony export */   SelectLabel: () => (/* binding */ SelectLabel),\n/* harmony export */   SelectScrollDownButton: () => (/* binding */ SelectScrollDownButton),\n/* harmony export */   SelectScrollUpButton: () => (/* binding */ SelectScrollUpButton),\n/* harmony export */   SelectSeparator: () => (/* binding */ SelectSeparator),\n/* harmony export */   SelectTrigger: () => (/* binding */ SelectTrigger),\n/* harmony export */   SelectValue: () => (/* binding */ SelectValue)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/shared/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-select */ \"(ssr)/./node_modules/@radix-ui/react-select/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Select,SelectGroup,SelectValue,SelectTrigger,SelectContent,SelectLabel,SelectItem,SelectSeparator,SelectScrollUpButton,SelectScrollDownButton auto */ \n\n\n\n\nconst Select = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst SelectGroup = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Group;\nconst SelectValue = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Value;\nconst SelectTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Trigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 opacity-50\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\select.tsx\",\n                lineNumber: 28,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 19,\n        columnNumber: 3\n    }, undefined));\nSelectTrigger.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Trigger.displayName;\nconst SelectScrollUpButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollUpButton, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default items-center justify-center py-1\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\select.tsx\",\n            lineNumber: 47,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined));\nSelectScrollUpButton.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollUpButton.displayName;\nconst SelectScrollDownButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollDownButton, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default items-center justify-center py-1\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\select.tsx\",\n            lineNumber: 64,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 56,\n        columnNumber: 3\n    }, undefined));\nSelectScrollDownButton.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollDownButton.displayName;\nconst SelectContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, position = \"popper\", ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", position === \"popper\" && \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\", className),\n            position: position,\n            ...props,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectScrollUpButton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Viewport, {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-1\", position === \"popper\" && \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"),\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectScrollDownButton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\select.tsx\",\n            lineNumber: 75,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 74,\n        columnNumber: 3\n    }, undefined));\nSelectContent.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst SelectLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 106,\n        columnNumber: 3\n    }, undefined));\nSelectLabel.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Label.displayName;\nconst SelectItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\select.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\select.tsx\",\n                lineNumber: 126,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ItemText, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\select.tsx\",\n                lineNumber: 132,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 118,\n        columnNumber: 3\n    }, undefined));\nSelectItem.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Item.displayName;\nconst SelectSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 141,\n        columnNumber: 3\n    }, undefined));\nSelectSeparator.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Separator.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/select.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/sonner.tsx":
/*!**********************************!*\
  !*** ./components/ui/sonner.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/shared/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nconst Toaster = ({ ...props })=>{\n    const { theme = \"system\" } = (0,next_themes__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n        theme: theme,\n        className: \"toaster group\",\n        toastOptions: {\n            classNames: {\n                toast: \"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg\",\n                description: \"group-[.toast]:text-muted-foreground\",\n                actionButton: \"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground\",\n                cancelButton: \"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground\"\n            }\n        },\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\sonner.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/sonner.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/permissions.ts":
/*!****************************!*\
  !*** ./lib/permissions.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ROLE_PERMISSIONS: () => (/* binding */ ROLE_PERMISSIONS),\n/* harmony export */   getModulePermissions: () => (/* binding */ getModulePermissions),\n/* harmony export */   getUserModules: () => (/* binding */ getUserModules),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission)\n/* harmony export */ });\nconst ROLE_PERMISSIONS = [\n    // FOUNDER - Full access\n    {\n        role: \"FOUNDER\",\n        module: \"USER\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"ASSIGN\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"ROLE\",\n        permissions: [\n            \"MANAGE\",\n            \"ACCESS_SETTINGS\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"STORE\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"SETTINGS\",\n        permissions: [\n            \"ACCESS_SETTINGS\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"REPORTS\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\",\n            \"PRINT\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"SUBSCRIPTION\",\n        permissions: [\n            \"READ\",\n            \"UPDATE\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"AUDIT_LOG\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"DASHBOARD\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    // SUPER_ADMIN\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"USER\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"ASSIGN\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"STORE\",\n        permissions: [\n            \"READ\",\n            \"UPDATE\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"SETTINGS\",\n        permissions: [\n            \"ACCESS_SETTINGS\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"REPORTS\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\",\n            \"PRINT\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"AUDIT_LOG\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"SUPPORT\",\n        permissions: [\n            \"READ\",\n            \"CREATE\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"DASHBOARD\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    // ADMIN\n    {\n        role: \"ADMIN\",\n        module: \"PRODUCT\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"IMPORT\",\n            \"EXPORT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"CATEGORY\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"INVENTORY\",\n        permissions: [\n            \"READ\",\n            \"UPDATE\",\n            \"EXPORT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"PURCHASE\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"APPROVE\",\n            \"REJECT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"PURCHASE_RETURN\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"SALES\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"EXPORT\",\n            \"PRINT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"SALES_RETURN\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"CUSTOMER\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"SUPPLIER\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"DISTRIBUTOR\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"EXPENSE\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"PAYMENT\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"B2B\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"APPROVE\",\n            \"REJECT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"BILLING\",\n        permissions: [\n            \"READ\",\n            \"PRINT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"TAX\",\n        permissions: [\n            \"READ\",\n            \"UPDATE\",\n            \"ACCESS_SETTINGS\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"NOTIFICATION\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"DOCUMENT\",\n        permissions: [\n            \"UPLOAD\",\n            \"READ\",\n            \"DOWNLOAD\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"REPORTS\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\",\n            \"PRINT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"DASHBOARD\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"FEEDBACK\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"SUPPORT\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    // STAFF\n    {\n        role: \"STAFF\",\n        module: \"SALES\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"SALES_RETURN\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"INVENTORY\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"BILLING\",\n        permissions: [\n            \"READ\",\n            \"PRINT\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"DOCUMENT\",\n        permissions: [\n            \"UPLOAD\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"CUSTOMER\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"DASHBOARD\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    // DISTRIBUTOR\n    {\n        role: \"DISTRIBUTOR\",\n        module: \"PURCHASE\",\n        permissions: [\n            \"READ\",\n            \"APPROVE\",\n            \"REJECT\"\n        ]\n    },\n    {\n        role: \"DISTRIBUTOR\",\n        module: \"PURCHASE_RETURN\",\n        permissions: [\n            \"READ\",\n            \"APPROVE\"\n        ]\n    },\n    {\n        role: \"DISTRIBUTOR\",\n        module: \"DASHBOARD\",\n        permissions: [\n            \"READ\"\n        ]\n    }\n];\nfunction hasPermission(userRole, module, permission) {\n    const rolePermissions = ROLE_PERMISSIONS.filter((rp)=>rp.role === userRole && rp.module === module);\n    return rolePermissions.some((rp)=>rp.permissions.includes(permission));\n}\nfunction getUserModules(userRole) {\n    return ROLE_PERMISSIONS.filter((rp)=>rp.role === userRole).map((rp)=>rp.module);\n}\nfunction getModulePermissions(userRole, module) {\n    const rolePermission = ROLE_PERMISSIONS.find((rp)=>rp.role === userRole && rp.module === module);\n    return rolePermission?.permissions || [];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/permissions.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTZDO0FBQ0o7QUFFbEMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ3JvY2VyeS1tYW5hZ2VtZW50LXN5c3RlbS8uL2xpYi91dGlscy50cz9mNzQ1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gJ2Nsc3gnO1xuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gJ3RhaWx3aW5kLW1lcmdlJztcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSk7XG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d11a3318c6f5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ncm9jZXJ5LW1hbmFnZW1lbnQtc3lzdGVtLy4vYXBwL2dsb2JhbHMuY3NzPzk2MzIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkMTFhMzMxOGM2ZjVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/(dashboard)/layout.tsx":
/*!************************************!*\
  !*** ./app/(dashboard)/layout.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\Grocery Management System\project\app\(dashboard)\layout.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/(dashboard)/reports/page.tsx":
/*!******************************************!*\
  !*** ./app/(dashboard)/reports/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\Grocery Management System\project\app\(dashboard)\reports\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/shared/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_ui_sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/sonner */ \"(rsc)/./components/ui/sonner.tsx\");\n/* harmony import */ var _components_auth_auth_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/auth/auth-provider */ \"(rsc)/./components/auth/auth-provider.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"GroceryPOS - Complete Store Management System\",\n    description: \"Modern SaaS solution for grocery store management with POS, inventory, and analytics\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_auth_provider__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                        position: \"top-right\",\n                        richColors: true,\n                        expand: false,\n                        duration: 4000\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\layout.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/auth/auth-provider.tsx":
/*!*******************************************!*\
  !*** ./components/auth/auth-provider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\Grocery Management System\project\components\auth\auth-provider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = proxy["AuthProvider"];

const e1 = proxy["useAuth"];


/***/ }),

/***/ "(rsc)/./components/ui/sonner.tsx":
/*!**********************************!*\
  !*** ./components/ui/sonner.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\Grocery Management System\project\components\ui\sonner.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = proxy["Toaster"];


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/sonner","vendor-chunks/next-themes","vendor-chunks/@swc","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/clsx","vendor-chunks/@radix-ui","vendor-chunks/class-variance-authority","vendor-chunks/@floating-ui","vendor-chunks/tslib","vendor-chunks/react-remove-scroll","vendor-chunks/use-callback-ref","vendor-chunks/use-sidecar","vendor-chunks/aria-hidden","vendor-chunks/react-remove-scroll-bar","vendor-chunks/react-style-singleton","vendor-chunks/get-nonce","vendor-chunks/detect-node-es"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(dashboard)%2Freports%2Fpage&page=%2F(dashboard)%2Freports%2Fpage&appPaths=%2F(dashboard)%2Freports%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Freports%2Fpage.tsx&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();