'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/auth/auth-provider'
import { DataTable } from '@/components/ui/data-table'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { toast } from 'sonner'
import { 
  Plus, 
  Eye, 
  Trash2, 
  ShoppingCart, 
  Truck,
  Calendar,
  TrendingUp,
  DollarSign,
  Package
} from 'lucide-react'

interface Purchase {
  id: string
  purchaseNo: string
  supplierId: string
  supplier: {
    id: string
    name: string
    phone: string
  }
  subtotal: number
  discount: number
  taxAmount: number
  totalAmount: number
  status: string
  notes?: string
  createdAt: string
  items: Array<{
    id: string
    productId: string
    product: {
      id: string
      name: string
      sku: string
      unit: string
    }
    quantity: number
    unitPrice: number
    totalPrice: number
  }>
  createdBy: {
    id: string
    name: string
  }
}

interface Product {
  id: string
  name: string
  sku: string
  unit: string
  costPrice: number
  taxRate: number
}

interface Supplier {
  id: string
  name: string
  phone: string
}

export default function PurchasesPage() {
  const { token } = useAuth()
  const [purchases, setPurchases] = useState<Purchase[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [suppliers, setSuppliers] = useState<Supplier[]>([])
  const [loading, setLoading] = useState(true)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  })
  const [search, setSearch] = useState('')
  const [showDialog, setShowDialog] = useState(false)
  const [showViewDialog, setShowViewDialog] = useState(false)
  const [selectedPurchase, setSelectedPurchase] = useState<Purchase | null>(null)
  const [submitting, setSubmitting] = useState(false)

  // Purchase form state
  const [purchaseForm, setPurchaseForm] = useState({
    supplierId: '',
    purchaseDate: new Date().toISOString().split('T')[0],
    expectedDeliveryDate: '',
    items: [] as Array<{
      productId: string
      quantity: number
      unitPrice: number
      discount: number
      taxRate: number
    }>,
    discount: 0,
    notes: ''
  })

  // Stats
  const [stats, setStats] = useState({
    todayPurchases: 0,
    todayAmount: 0,
    monthPurchases: 0,
    monthAmount: 0
  })

  const columns = [
    {
      key: 'purchaseNo',
      label: 'Purchase No.',
      sortable: true,
      render: (value: string) => (
        <div className="font-mono text-sm">{value}</div>
      )
    },
    {
      key: 'supplier',
      label: 'Supplier',
      render: (value: any) => (
        <div>
          <div className="font-medium">{value.name}</div>
          <div className="text-sm text-muted-foreground">{value.phone}</div>
        </div>
      )
    },
    {
      key: 'items',
      label: 'Items',
      render: (value: any[]) => (
        <Badge variant="secondary">
          {value.length} item{value.length !== 1 ? 's' : ''}
        </Badge>
      )
    },
    {
      key: 'totalAmount',
      label: 'Total',
      render: (value: number) => (
        <div className="font-medium">₹{value.toFixed(2)}</div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: string) => (
        <Badge variant={
          value === 'COMPLETED' ? 'default' : 
          value === 'PENDING' ? 'secondary' : 
          value === 'CANCELLED' ? 'destructive' : 'outline'
        }>
          {value}
        </Badge>
      )
    },
    {
      key: 'createdAt',
      label: 'Date',
      render: (value: string) => (
        <div className="text-sm">
          {new Date(value).toLocaleDateString()}
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (value: any, row: Purchase) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleView(row)}
          >
            <Eye className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ]

  const fetchPurchases = async (page = 1, limit = 10, searchTerm = '') => {
    if (!token) return

    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        search: searchTerm
      })

      const response = await fetch(`/api/purchases?${params}`, {
        headers: { Authorization: `Bearer ${token}` }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch purchases')
      }

      const result = await response.json()
      setPurchases(result.data)
      setPagination(result.pagination)
    } catch (error) {
      console.error('Error fetching purchases:', error)
      toast.error('Failed to load purchases')
    } finally {
      setLoading(false)
    }
  }

  const fetchProducts = async () => {
    if (!token) return

    try {
      const response = await fetch('/api/products', {
        headers: { Authorization: `Bearer ${token}` }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch products')
      }

      const result = await response.json()
      setProducts(result.data)
    } catch (error) {
      console.error('Error fetching products:', error)
    }
  }

  const fetchSuppliers = async () => {
    if (!token) return

    try {
      const response = await fetch('/api/suppliers', {
        headers: { Authorization: `Bearer ${token}` }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch suppliers')
      }

      const result = await response.json()
      setSuppliers(result.data || [])
    } catch (error) {
      console.error('Error fetching suppliers:', error)
    }
  }

  const fetchStats = async () => {
    if (!token) return

    try {
      const today = new Date().toISOString().split('T')[0]
      const monthStart = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0]
      
      // Fetch today's purchases
      const todayResponse = await fetch(`/api/purchases?startDate=${today}&endDate=${today}`, {
        headers: { Authorization: `Bearer ${token}` }
      })
      
      // Fetch month's purchases
      const monthResponse = await fetch(`/api/purchases?startDate=${monthStart}`, {
        headers: { Authorization: `Bearer ${token}` }
      })

      if (todayResponse.ok && monthResponse.ok) {
        const todayData = await todayResponse.json()
        const monthData = await monthResponse.json()

        setStats({
          todayPurchases: todayData.pagination?.total || 0,
          todayAmount: todayData.data?.reduce((sum: number, purchase: Purchase) => sum + purchase.totalAmount, 0) || 0,
          monthPurchases: monthData.pagination?.total || 0,
          monthAmount: monthData.data?.reduce((sum: number, purchase: Purchase) => sum + purchase.totalAmount, 0) || 0
        })
      }
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const handleView = (purchase: Purchase) => {
    setSelectedPurchase(purchase)
    setShowViewDialog(true)
  }

  const handleAdd = () => {
    setPurchaseForm({
      supplierId: '',
      purchaseDate: new Date().toISOString().split('T')[0],
      expectedDeliveryDate: '',
      items: [],
      discount: 0,
      notes: ''
    })
    setShowDialog(true)
  }

  const addItem = () => {
    setPurchaseForm(prev => ({
      ...prev,
      items: [...prev.items, {
        productId: '',
        quantity: 1,
        unitPrice: 0,
        discount: 0,
        taxRate: 0
      }]
    }))
  }

  const removeItem = (index: number) => {
    setPurchaseForm(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }))
  }

  const updateItem = (index: number, field: string, value: any) => {
    setPurchaseForm(prev => ({
      ...prev,
      items: prev.items.map((item, i) => {
        if (i === index) {
          const updatedItem = { ...item, [field]: value }
          
          // Auto-fill price and tax when product is selected
          if (field === 'productId') {
            const product = products.find(p => p.id === value)
            if (product) {
              updatedItem.unitPrice = product.costPrice
              updatedItem.taxRate = product.taxRate
            }
          }
          
          return updatedItem
        }
        return item
      })
    }))
  }

  const calculateTotal = () => {
    const itemsTotal = purchaseForm.items.reduce((sum, item) => {
      const itemTotal = item.quantity * item.unitPrice
      const itemDiscount = item.discount || 0
      const taxableAmount = itemTotal - itemDiscount
      const tax = (taxableAmount * item.taxRate) / 100
      return sum + taxableAmount + tax
    }, 0)
    
    return Math.max(0, itemsTotal - purchaseForm.discount)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!token) return

    if (purchaseForm.items.length === 0) {
      toast.error('Please add at least one item')
      return
    }

    try {
      setSubmitting(true)

      const response = await fetch('/api/purchases', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify(purchaseForm)
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create purchase')
      }

      toast.success('Purchase created successfully')
      setShowDialog(false)
      fetchPurchases(pagination.page, pagination.limit, search)
      fetchStats()
    } catch (error: any) {
      toast.error(error.message)
    } finally {
      setSubmitting(false)
    }
  }

  useEffect(() => {
    fetchPurchases()
    fetchProducts()
    fetchSuppliers()
    fetchStats()
  }, [token])

  return (
    <div className="p-6 space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Purchases</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.todayPurchases}</div>
            <p className="text-xs text-muted-foreground">
              ₹{stats.todayAmount.toFixed(2)} spent
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Month's Purchases</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.monthPurchases}</div>
            <p className="text-xs text-muted-foreground">
              ₹{stats.monthAmount.toFixed(2)} spent
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Purchase Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ₹{stats.monthPurchases > 0 ? (stats.monthAmount / stats.monthPurchases).toFixed(2) : '0.00'}
            </div>
            <p className="text-xs text-muted-foreground">This month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Suppliers</CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{suppliers.length}</div>
            <p className="text-xs text-muted-foreground">Active suppliers</p>
          </CardContent>
        </Card>
      </div>

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Purchases</h1>
          <p className="text-muted-foreground">Manage your purchase orders</p>
        </div>
      </div>

      {/* Purchases Table */}
      <DataTable
        columns={columns}
        data={purchases}
        loading={loading}
        pagination={pagination}
        onPageChange={(page) => fetchPurchases(page, pagination.limit, search)}
        onLimitChange={(limit) => fetchPurchases(1, limit, search)}
        onSearch={(searchTerm) => {
          setSearch(searchTerm)
          fetchPurchases(1, pagination.limit, searchTerm)
        }}
        onAdd={handleAdd}
        searchPlaceholder="Search purchases..."
        title=""
        addButtonText="New Purchase"
      />

      {/* Create Purchase Dialog */}
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Purchase</DialogTitle>
            <DialogDescription>
              Add products and supplier details to create a new purchase order.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Supplier and Dates */}
            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="supplierId">Supplier *</Label>
                <Select 
                  value={purchaseForm.supplierId} 
                  onValueChange={(value) => setPurchaseForm(prev => ({ ...prev, supplierId: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select supplier" />
                  </SelectTrigger>
                  <SelectContent>
                    {suppliers.map((supplier) => (
                      <SelectItem key={supplier.id} value={supplier.id}>
                        {supplier.name} ({supplier.phone})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="purchaseDate">Purchase Date</Label>
                <Input
                  id="purchaseDate"
                  type="date"
                  value={purchaseForm.purchaseDate}
                  onChange={(e) => setPurchaseForm(prev => ({ ...prev, purchaseDate: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="expectedDeliveryDate">Expected Delivery</Label>
                <Input
                  id="expectedDeliveryDate"
                  type="date"
                  value={purchaseForm.expectedDeliveryDate}
                  onChange={(e) => setPurchaseForm(prev => ({ ...prev, expectedDeliveryDate: e.target.value }))}
                />
              </div>
            </div>

            {/* Items */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <Label>Items</Label>
                <Button type="button" onClick={addItem} size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Item
                </Button>
              </div>
              
              {purchaseForm.items.map((item, index) => (
                <div key={index} className="grid grid-cols-6 gap-2 mb-2 p-3 border rounded">
                  <div>
                    <Select 
                      value={item.productId} 
                      onValueChange={(value) => updateItem(index, 'productId', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Product" />
                      </SelectTrigger>
                      <SelectContent>
                        {products.map((product) => (
                          <SelectItem key={product.id} value={product.id}>
                            {product.name} ({product.sku})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Input
                      type="number"
                      min="1"
                      value={item.quantity}
                      onChange={(e) => updateItem(index, 'quantity', parseInt(e.target.value) || 1)}
                      placeholder="Qty"
                    />
                  </div>
                  <div>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      value={item.unitPrice}
                      onChange={(e) => updateItem(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                      placeholder="Price"
                    />
                  </div>
                  <div>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      value={item.discount}
                      onChange={(e) => updateItem(index, 'discount', parseFloat(e.target.value) || 0)}
                      placeholder="Discount"
                    />
                  </div>
                  <div>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      max="100"
                      value={item.taxRate}
                      onChange={(e) => updateItem(index, 'taxRate', parseFloat(e.target.value) || 0)}
                      placeholder="Tax %"
                    />
                  </div>
                  <div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeItem(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            {/* Discount and Notes */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="discount">Overall Discount</Label>
                <Input
                  id="discount"
                  type="number"
                  step="0.01"
                  min="0"
                  value={purchaseForm.discount}
                  onChange={(e) => setPurchaseForm(prev => ({ ...prev, discount: parseFloat(e.target.value) || 0 }))}
                  placeholder="0.00"
                />
              </div>
              <div>
                <Label>Total Amount</Label>
                <div className="text-2xl font-bold text-blue-600">
                  ₹{calculateTotal().toFixed(2)}
                </div>
              </div>
            </div>

            <div>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={purchaseForm.notes}
                onChange={(e) => setPurchaseForm(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Enter any notes"
                rows={2}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowDialog(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={submitting}>
                {submitting ? 'Creating...' : 'Create Purchase'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* View Purchase Dialog */}
      <Dialog open={showViewDialog} onOpenChange={setShowViewDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Purchase Details</DialogTitle>
            <DialogDescription>
              {selectedPurchase?.purchaseNo} - {new Date(selectedPurchase?.createdAt || '').toLocaleDateString()}
            </DialogDescription>
          </DialogHeader>
          {selectedPurchase && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Supplier</Label>
                  <div className="font-medium">{selectedPurchase.supplier.name}</div>
                  <div className="text-sm text-muted-foreground">{selectedPurchase.supplier.phone}</div>
                </div>
                <div>
                  <Label>Status</Label>
                  <div>
                    <Badge variant={
                      selectedPurchase.status === 'COMPLETED' ? 'default' : 
                      selectedPurchase.status === 'PENDING' ? 'secondary' : 
                      selectedPurchase.status === 'CANCELLED' ? 'destructive' : 'outline'
                    }>
                      {selectedPurchase.status}
                    </Badge>
                  </div>
                </div>
              </div>

              <div>
                <Label>Items</Label>
                <div className="border rounded-lg overflow-hidden">
                  <table className="w-full">
                    <thead className="bg-muted">
                      <tr>
                        <th className="text-left p-2">Product</th>
                        <th className="text-right p-2">Qty</th>
                        <th className="text-right p-2">Price</th>
                        <th className="text-right p-2">Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      {selectedPurchase.items.map((item) => (
                        <tr key={item.id} className="border-t">
                          <td className="p-2">
                            <div className="font-medium">{item.product.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {item.product.sku}
                            </div>
                          </td>
                          <td className="text-right p-2">
                            {item.quantity} {item.product.unit}
                          </td>
                          <td className="text-right p-2">
                            ₹{item.unitPrice.toFixed(2)}
                          </td>
                          <td className="text-right p-2">
                            ₹{item.totalPrice.toFixed(2)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              <div className="border-t pt-4">
                <div className="flex justify-between items-center">
                  <span>Subtotal:</span>
                  <span>₹{selectedPurchase.subtotal.toFixed(2)}</span>
                </div>
                {selectedPurchase.discount > 0 && (
                  <div className="flex justify-between items-center">
                    <span>Discount:</span>
                    <span>-₹{selectedPurchase.discount.toFixed(2)}</span>
                  </div>
                )}
                {selectedPurchase.taxAmount > 0 && (
                  <div className="flex justify-between items-center">
                    <span>Tax:</span>
                    <span>₹{selectedPurchase.taxAmount.toFixed(2)}</span>
                  </div>
                )}
                <div className="flex justify-between items-center font-bold text-lg border-t pt-2">
                  <span>Total:</span>
                  <span>₹{selectedPurchase.totalAmount.toFixed(2)}</span>
                </div>
              </div>

              {selectedPurchase.notes && (
                <div>
                  <Label>Notes</Label>
                  <div className="text-sm">{selectedPurchase.notes}</div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
