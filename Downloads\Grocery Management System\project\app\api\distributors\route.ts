import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  getPaginationParams,
  buildSearchFilter,
  createPaginatedResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const distributorSchema = z.object({
  name: z.string().min(1, 'Distributor name is required'),
  contactPerson: z.string().optional(),
  phone: z.string().min(1, 'Phone number is required'),
  email: z.string().email().optional().or(z.literal('')),
  address: z.string().min(1, 'Address is required'),
  gstNumber: z.string().optional(),
  panNumber: z.string().optional(),
  distributorType: z.enum(['REGIONAL', 'NATIONAL', 'EXCLUSIVE']).default('REGIONAL'),
  territory: z.string().optional(),
  commissionRate: z.number().min(0).max(100).default(0),
  creditLimit: z.number().min(0).default(0),
  paymentTerms: z.object({
    creditDays: z.number().min(0).default(30),
    paymentMethod: z.enum(['CASH', 'CREDIT', 'ADVANCE']).default('CREDIT')
  }).optional(),
  bankDetails: z.object({
    accountNumber: z.string().optional(),
    ifscCode: z.string().optional(),
    bankName: z.string().optional(),
    accountHolderName: z.string().optional()
  }).optional(),
  documents: z.array(z.object({
    type: z.string(),
    url: z.string(),
    expiryDate: z.string().optional()
  })).optional(),
  isActive: z.boolean().default(true)
})

// GET /api/distributors - Get all distributors with pagination and filters
export const GET = withPermission('DISTRIBUTOR', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { page, limit, skip, search, sortBy, sortOrder } = getPaginationParams(request)
  const { searchParams } = new URL(request.url)
  const distributorType = searchParams.get('type')
  const territory = searchParams.get('territory')
  const status = searchParams.get('status')

  // Build filters
  const where: any = {
    storeId,
    ...buildSearchFilter(search, ['name', 'contactPerson', 'phone', 'email', 'territory'])
  }

  if (distributorType) {
    where.distributorType = distributorType
  }

  if (territory) {
    where.territory = { contains: territory, mode: 'insensitive' }
  }

  if (status) {
    where.isActive = status === 'active'
  }

  // Get total count
  const total = await prisma.distributor.count({ where })

  // Get distributors with pagination
  const distributors = await prisma.distributor.findMany({
    where,
    include: {
      _count: {
        select: {
          purchases: true
        }
      }
    },
    orderBy: { [sortBy]: sortOrder },
    skip,
    take: limit
  })

  // Add performance metrics
  const distributorsWithMetrics = await Promise.all(
    distributors.map(async (distributor) => {
      const [totalPurchases, totalValue, lastPurchase] = await Promise.all([
        prisma.purchase.count({
          where: {
            distributorId: distributor.id,
            storeId
          }
        }),
        
        prisma.purchase.aggregate({
          where: {
            distributorId: distributor.id,
            storeId,
            status: { in: ['COMPLETED', 'RECEIVED'] }
          },
          _sum: { totalAmount: true }
        }),
        
        prisma.purchase.findFirst({
          where: {
            distributorId: distributor.id,
            storeId
          },
          orderBy: { createdAt: 'desc' },
          select: { createdAt: true }
        })
      ])

      return {
        ...distributor,
        metrics: {
          totalPurchases,
          totalValue: totalValue._sum.totalAmount || 0,
          lastPurchaseDate: lastPurchase?.createdAt
        }
      }
    })
  )

  return createPaginatedResponse(distributorsWithMetrics, page, limit, total)
})

// POST /api/distributors - Create new distributor
export const POST = withPermission('DISTRIBUTOR', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const data = distributorSchema.parse(body)

  // Check if phone already exists
  if (data.phone) {
    const existingDistributor = await prisma.distributor.findFirst({
      where: {
        phone: data.phone,
        storeId,
        isActive: true
      }
    })

    if (existingDistributor) {
      return createErrorResponse('Distributor with this phone number already exists', 400)
    }
  }

  // Check if email already exists
  if (data.email) {
    const existingDistributor = await prisma.distributor.findFirst({
      where: {
        email: data.email,
        storeId,
        isActive: true
      }
    })

    if (existingDistributor) {
      return createErrorResponse('Distributor with this email already exists', 400)
    }
  }

  // Generate distributor code
  const distributorCount = await prisma.distributor.count({ where: { storeId } })
  const distributorCode = `DIST-${String(distributorCount + 1).padStart(6, '0')}`

  const distributor = await prisma.distributor.create({
    data: {
      ...data,
      distributorCode,
      storeId
    },
    include: {
      _count: {
        select: {
          purchases: true
        }
      }
    }
  })

  // Create audit log
  await createAuditLog(
    'CREATE',
    'DISTRIBUTOR',
    `Created distributor: ${distributor.name} (${distributor.distributorCode})`,
    user.id,
    storeId
  )

  return createSuccessResponse(distributor, 'Distributor created successfully')
})
