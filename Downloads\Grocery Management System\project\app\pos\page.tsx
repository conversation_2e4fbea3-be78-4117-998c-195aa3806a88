'use client'

import React, { useState, useEffect } from 'react'
import { 
  Search, 
  ScanLine, 
  Plus, 
  Minus, 
  Trash2, 
  User, 
  CreditCard, 
  Banknote, 
  Smartphone,
  Calculator,
  Receipt,
  ShoppingCart,
  Tag,
  Percent,
  Gift,
  Clock,
  CheckCircle,
  X
} from 'lucide-react'

interface Product {
  id: string
  name: string
  sku: string
  barcode: string
  price: number
  stock: number
  category: string
  image?: string
  taxRate: number
}

interface CartItem {
  product: Product
  quantity: number
  unitPrice: number
  discount: number
  total: number
}

interface Customer {
  id: string
  name: string
  phone: string
  email: string
  loyaltyPoints: number
}

export default function POSPage() {
  const [cart, setCart] = useState<CartItem[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
  const [discount, setDiscount] = useState({ type: 'percentage', value: 0 })
  const [paymentMethod, setPaymentMethod] = useState<'CASH' | 'CARD' | 'UPI' | 'WALLET'>('CASH')
  const [amountReceived, setAmountReceived] = useState('')
  const [showPayment, setShowPayment] = useState(false)
  const [showCustomerModal, setShowCustomerModal] = useState(false)
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    fetchProducts()
  }, [])

  const fetchProducts = async () => {
    try {
      setLoading(true)
      // Mock data - replace with actual API call
      const mockProducts: Product[] = [
        {
          id: '1',
          name: 'Apple iPhone 14',
          sku: 'IPH14-128',
          barcode: '1234567890123',
          price: 79900,
          stock: 25,
          category: 'Electronics',
          taxRate: 18
        },
        {
          id: '2',
          name: 'Samsung Galaxy S23',
          sku: 'SGS23-256',
          barcode: '1234567890124',
          price: 74999,
          stock: 15,
          category: 'Electronics',
          taxRate: 18
        },
        {
          id: '3',
          name: 'Nike Air Max',
          sku: 'NAM-42',
          barcode: '1234567890125',
          price: 8999,
          stock: 50,
          category: 'Footwear',
          taxRate: 12
        }
      ]
      setProducts(mockProducts)
    } catch (error) {
      console.error('Error fetching products:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.barcode.includes(searchTerm)
  )

  const addToCart = (product: Product) => {
    const existingItem = cart.find(item => item.product.id === product.id)
    
    if (existingItem) {
      updateQuantity(product.id, existingItem.quantity + 1)
    } else {
      const newItem: CartItem = {
        product,
        quantity: 1,
        unitPrice: product.price,
        discount: 0,
        total: product.price
      }
      setCart([...cart, newItem])
    }
  }

  const updateQuantity = (productId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(productId)
      return
    }

    setCart(cart.map(item => {
      if (item.product.id === productId) {
        const total = (item.unitPrice * newQuantity) - item.discount
        return { ...item, quantity: newQuantity, total }
      }
      return item
    }))
  }

  const removeFromCart = (productId: string) => {
    setCart(cart.filter(item => item.product.id !== productId))
  }

  const updateItemDiscount = (productId: string, discountAmount: number) => {
    setCart(cart.map(item => {
      if (item.product.id === productId) {
        const total = (item.unitPrice * item.quantity) - discountAmount
        return { ...item, discount: discountAmount, total: Math.max(0, total) }
      }
      return item
    }))
  }

  const calculateSubtotal = () => {
    return cart.reduce((sum, item) => sum + item.total, 0)
  }

  const calculateTax = () => {
    return cart.reduce((sum, item) => {
      const taxableAmount = item.total
      const tax = (taxableAmount * item.product.taxRate) / 100
      return sum + tax
    }, 0)
  }

  const calculateDiscount = () => {
    const subtotal = calculateSubtotal()
    if (discount.type === 'percentage') {
      return (subtotal * discount.value) / 100
    }
    return discount.value
  }

  const calculateTotal = () => {
    const subtotal = calculateSubtotal()
    const tax = calculateTax()
    const discountAmount = calculateDiscount()
    return subtotal + tax - discountAmount
  }

  const calculateChange = () => {
    const total = calculateTotal()
    const received = parseFloat(amountReceived) || 0
    return Math.max(0, received - total)
  }

  const handleCheckout = async () => {
    if (cart.length === 0) {
      alert('Cart is empty')
      return
    }

    if (paymentMethod === 'CASH' && parseFloat(amountReceived) < calculateTotal()) {
      alert('Insufficient amount received')
      return
    }

    try {
      setLoading(true)
      
      const saleData = {
        items: cart.map(item => ({
          productId: item.product.id,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          discount: item.discount,
          total: item.total
        })),
        customerId: selectedCustomer?.id,
        subtotal: calculateSubtotal(),
        tax: calculateTax(),
        discount: calculateDiscount(),
        total: calculateTotal(),
        paymentMethod,
        amountReceived: parseFloat(amountReceived) || calculateTotal(),
        change: calculateChange()
      }

      // Mock API call - replace with actual implementation
      console.log('Processing sale:', saleData)
      
      // Reset cart and form
      setCart([])
      setSelectedCustomer(null)
      setDiscount({ type: 'percentage', value: 0 })
      setAmountReceived('')
      setShowPayment(false)
      
      alert('Sale completed successfully!')
      
    } catch (error) {
      console.error('Error processing sale:', error)
      alert('Error processing sale')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount)
  }

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Left Panel - Products */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="bg-white shadow-sm p-4 border-b">
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search products by name, SKU, or barcode..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
              <ScanLine className="w-4 h-4" />
              Scan
            </button>
          </div>
        </div>

        {/* Products Grid */}
        <div className="flex-1 overflow-y-auto p-4">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {filteredProducts.map((product) => (
              <div
                key={product.id}
                onClick={() => addToCart(product)}
                className="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer p-4"
              >
                <div className="aspect-square bg-gray-100 rounded-lg mb-3 flex items-center justify-center">
                  {product.image ? (
                    <img
                      src={product.image}
                      alt={product.name}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    <div className="text-gray-400">
                      <ShoppingCart className="w-8 h-8" />
                    </div>
                  )}
                </div>
                <h3 className="font-medium text-sm text-gray-900 mb-1 line-clamp-2">
                  {product.name}
                </h3>
                <p className="text-xs text-gray-500 mb-2">{product.sku}</p>
                <div className="flex justify-between items-center">
                  <span className="font-bold text-blue-600">{formatCurrency(product.price)}</span>
                  <span className="text-xs text-gray-500">Stock: {product.stock}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Right Panel - Cart */}
      <div className="w-96 bg-white shadow-lg flex flex-col">
        {/* Cart Header */}
        <div className="p-4 border-b">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Current Sale</h2>
            <div className="flex items-center gap-2">
              <ShoppingCart className="w-5 h-5 text-gray-600" />
              <span className="text-sm text-gray-600">{cart.length} items</span>
            </div>
          </div>

          {/* Customer Selection */}
          <button
            onClick={() => setShowCustomerModal(true)}
            className="w-full p-3 border border-gray-300 rounded-lg text-left hover:bg-gray-50 transition-colors"
          >
            <div className="flex items-center gap-2">
              <User className="w-4 h-4 text-gray-400" />
              <span className="text-sm">
                {selectedCustomer ? selectedCustomer.name : 'Select Customer (Optional)'}
              </span>
            </div>
          </button>
        </div>

        {/* Cart Items */}
        <div className="flex-1 overflow-y-auto p-4">
          {cart.length === 0 ? (
            <div className="text-center py-8">
              <ShoppingCart className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-500">Cart is empty</p>
              <p className="text-sm text-gray-400">Add products to start a sale</p>
            </div>
          ) : (
            <div className="space-y-3">
              {cart.map((item) => (
                <div key={item.product.id} className="bg-gray-50 rounded-lg p-3">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-medium text-sm text-gray-900 flex-1 mr-2">
                      {item.product.name}
                    </h4>
                    <button
                      onClick={() => removeFromCart(item.product.id)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                  
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => updateQuantity(item.product.id, item.quantity - 1)}
                        className="w-6 h-6 rounded bg-gray-200 hover:bg-gray-300 flex items-center justify-center"
                      >
                        <Minus className="w-3 h-3" />
                      </button>
                      <span className="w-8 text-center text-sm font-medium">{item.quantity}</span>
                      <button
                        onClick={() => updateQuantity(item.product.id, item.quantity + 1)}
                        className="w-6 h-6 rounded bg-gray-200 hover:bg-gray-300 flex items-center justify-center"
                      >
                        <Plus className="w-3 h-3" />
                      </button>
                    </div>
                    <span className="text-sm text-gray-600">
                      {formatCurrency(item.unitPrice)} each
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <button
                      onClick={() => {
                        const discount = prompt('Enter discount amount:')
                        if (discount) {
                          updateItemDiscount(item.product.id, parseFloat(discount))
                        }
                      }}
                      className="text-xs text-blue-600 hover:text-blue-800 flex items-center gap-1"
                    >
                      <Tag className="w-3 h-3" />
                      Discount
                    </button>
                    <span className="font-semibold text-gray-900">
                      {formatCurrency(item.total)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Cart Summary */}
        {cart.length > 0 && (
          <div className="border-t p-4">
            {/* Discount */}
            <div className="mb-4">
              <div className="flex items-center gap-2 mb-2">
                <Percent className="w-4 h-4 text-gray-600" />
                <span className="text-sm font-medium">Overall Discount</span>
              </div>
              <div className="flex gap-2">
                <select
                  value={discount.type}
                  onChange={(e) => setDiscount(prev => ({ ...prev, type: e.target.value as 'percentage' | 'amount' }))}
                  className="px-2 py-1 border border-gray-300 rounded text-sm"
                >
                  <option value="percentage">%</option>
                  <option value="amount">₹</option>
                </select>
                <input
                  type="number"
                  value={discount.value}
                  onChange={(e) => setDiscount(prev => ({ ...prev, value: parseFloat(e.target.value) || 0 }))}
                  className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm"
                  placeholder="0"
                />
              </div>
            </div>

            {/* Summary */}
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-sm">
                <span>Subtotal:</span>
                <span>{formatCurrency(calculateSubtotal())}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Tax:</span>
                <span>{formatCurrency(calculateTax())}</span>
              </div>
              {calculateDiscount() > 0 && (
                <div className="flex justify-between text-sm text-green-600">
                  <span>Discount:</span>
                  <span>-{formatCurrency(calculateDiscount())}</span>
                </div>
              )}
              <div className="flex justify-between text-lg font-bold border-t pt-2">
                <span>Total:</span>
                <span>{formatCurrency(calculateTotal())}</span>
              </div>
            </div>

            {/* Payment Button */}
            <button
              onClick={() => setShowPayment(true)}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-medium transition-colors"
            >
              Proceed to Payment
            </button>
          </div>
        )}
      </div>

      {/* Payment Modal */}
      {showPayment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold">Payment</h3>
                <button
                  onClick={() => setShowPayment(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              <div className="mb-6">
                <div className="text-2xl font-bold text-center mb-2">
                  {formatCurrency(calculateTotal())}
                </div>
                <p className="text-center text-gray-600">Total Amount</p>
              </div>

              {/* Payment Methods */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-3">Payment Method</label>
                <div className="grid grid-cols-2 gap-2">
                  {[
                    { id: 'CASH', label: 'Cash', icon: Banknote },
                    { id: 'CARD', label: 'Card', icon: CreditCard },
                    { id: 'UPI', label: 'UPI', icon: Smartphone },
                    { id: 'WALLET', label: 'Wallet', icon: Gift }
                  ].map((method) => (
                    <button
                      key={method.id}
                      onClick={() => setPaymentMethod(method.id as any)}
                      className={`p-3 border rounded-lg flex flex-col items-center gap-1 transition-colors ${
                        paymentMethod === method.id
                          ? 'border-blue-500 bg-blue-50 text-blue-700'
                          : 'border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      <method.icon className="w-5 h-5" />
                      <span className="text-sm">{method.label}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Amount Received (for cash) */}
              {paymentMethod === 'CASH' && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Amount Received</label>
                  <input
                    type="number"
                    value={amountReceived}
                    onChange={(e) => setAmountReceived(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter amount received"
                  />
                  {amountReceived && (
                    <div className="mt-2 text-sm">
                      <span className="text-gray-600">Change: </span>
                      <span className="font-medium text-green-600">
                        {formatCurrency(calculateChange())}
                      </span>
                    </div>
                  )}
                </div>
              )}

              {/* Complete Sale Button */}
              <button
                onClick={handleCheckout}
                disabled={loading || (paymentMethod === 'CASH' && parseFloat(amountReceived) < calculateTotal())}
                className="w-full bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Processing...
                  </>
                ) : (
                  <>
                    <CheckCircle className="w-5 h-5" />
                    Complete Sale
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
