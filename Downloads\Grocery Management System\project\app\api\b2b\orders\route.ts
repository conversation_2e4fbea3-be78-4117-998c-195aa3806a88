import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  getPaginationParams,
  buildSearchFilter,
  createPaginatedResponse,
  createAuditLog,
  validateStoreAccess,
  buildDateRangeFilter
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const b2bOrderSchema = z.object({
  customerId: z.string().min(1, 'Customer is required'),
  orderType: z.enum(['WHOLESALE', 'BULK', 'REGULAR']).default('WHOLESALE'),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).default('MEDIUM'),
  expectedDeliveryDate: z.string().optional(),
  paymentTerms: z.object({
    type: z.enum(['CASH', 'CREDIT', 'ADVANCE']).default('CREDIT'),
    creditDays: z.number().min(0).default(30),
    advancePercentage: z.number().min(0).max(100).default(0)
  }).optional(),
  items: z.array(z.object({
    productId: z.string().min(1, 'Product is required'),
    variantId: z.string().optional(),
    quantity: z.number().min(1, 'Quantity must be at least 1'),
    unitPrice: z.number().min(0, 'Unit price must be positive'),
    discount: z.number().min(0).default(0),
    discountType: z.enum(['AMOUNT', 'PERCENTAGE']).default('PERCENTAGE'),
    taxRate: z.number().min(0).max(100).default(0)
  })).min(1, 'At least one item is required'),
  discount: z.number().min(0).default(0),
  discountType: z.enum(['AMOUNT', 'PERCENTAGE']).default('AMOUNT'),
  shippingAddress: z.object({
    street: z.string(),
    city: z.string(),
    state: z.string(),
    pincode: z.string(),
    country: z.string().default('India')
  }).optional(),
  billingAddress: z.object({
    street: z.string(),
    city: z.string(),
    state: z.string(),
    pincode: z.string(),
    country: z.string().default('India')
  }).optional(),
  notes: z.string().optional(),
  internalNotes: z.string().optional()
})

// GET /api/b2b/orders - Get all B2B orders with advanced filtering
export const GET = withPermission('B2B', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { page, limit, skip, search, sortBy, sortOrder } = getPaginationParams(request)
  const { searchParams } = new URL(request.url)
  
  const status = searchParams.get('status')
  const orderType = searchParams.get('orderType')
  const priority = searchParams.get('priority')
  const customerId = searchParams.get('customerId')
  const startDate = searchParams.get('startDate')
  const endDate = searchParams.get('endDate')
  const minAmount = searchParams.get('minAmount')
  const maxAmount = searchParams.get('maxAmount')

  // Build filters
  const where: any = {
    storeId,
    ...buildSearchFilter(search, ['orderNo']),
    ...buildDateRangeFilter(startDate || undefined, endDate || undefined, 'createdAt')
  }

  if (status) {
    where.status = status
  }

  if (orderType) {
    where.orderType = orderType
  }

  if (priority) {
    where.priority = priority
  }

  if (customerId) {
    where.customerId = customerId
  }

  if (minAmount || maxAmount) {
    where.totalAmount = {}
    if (minAmount) where.totalAmount.gte = parseFloat(minAmount)
    if (maxAmount) where.totalAmount.lte = parseFloat(maxAmount)
  }

  // Get total count
  const total = await prisma.b2BOrder.count({ where })

  // Get orders with pagination
  const orders = await prisma.b2BOrder.findMany({
    where,
    include: {
      customer: {
        select: {
          id: true,
          name: true,
          phone: true,
          email: true,
          creditLimit: true,
          totalPurchases: true
        }
      },
      items: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
              unit: true,
              sellingPrice: true
            }
          },
          variant: {
            select: {
              id: true,
              name: true,
              sku: true,
              sellingPrice: true
            }
          }
        }
      },
      createdBy: {
        select: {
          id: true,
          name: true
        }
      },
      approvedBy: {
        select: {
          id: true,
          name: true
        }
      },
      _count: {
        select: {
          items: true
        }
      }
    },
    orderBy: { [sortBy]: sortOrder },
    skip,
    take: limit
  })

  // Add calculated fields
  const ordersWithCalculations = orders.map(order => ({
    ...order,
    itemsCount: order._count.items,
    balanceAmount: order.totalAmount - order.paidAmount,
    paymentStatus: order.paidAmount === 0 ? 'unpaid' : 
                   order.paidAmount >= order.totalAmount ? 'paid' : 'partial',
    isOverdue: order.status === 'APPROVED' && order.expectedDeliveryDate && 
               new Date(order.expectedDeliveryDate) < new Date(),
    creditUtilization: order.customer.creditLimit > 0 ? 
                      (order.customer.totalPurchases / order.customer.creditLimit) * 100 : 0
  }))

  return createPaginatedResponse(ordersWithCalculations, page, limit, total)
})

// POST /api/b2b/orders - Create new B2B order
export const POST = withPermission('B2B', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const data = b2bOrderSchema.parse(body)

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Validate customer exists and is B2B customer
      const customer = await tx.customer.findFirst({
        where: {
          id: data.customerId,
          storeId,
          isActive: true
        }
      })

      if (!customer) {
        throw new Error('Customer not found')
      }

      // Check credit limit if payment terms are credit
      if (data.paymentTerms?.type === 'CREDIT') {
        const pendingAmount = await tx.b2BOrder.aggregate({
          where: {
            customerId: data.customerId,
            storeId,
            status: { in: ['PENDING', 'APPROVED'] },
            paymentStatus: { in: ['UNPAID', 'PARTIAL'] }
          },
          _sum: { totalAmount: true }
        })

        const currentPending = pendingAmount._sum.totalAmount || 0
        const orderTotal = calculateOrderTotal(data.items, data.discount, data.discountType)

        if (customer.creditLimit > 0 && (currentPending + orderTotal) > customer.creditLimit) {
          throw new Error(`Order exceeds credit limit. Available credit: ₹${customer.creditLimit - currentPending}`)
        }
      }

      // Validate products and check inventory
      const productIds = data.items.map(item => item.productId)
      const products = await tx.product.findMany({
        where: {
          id: { in: productIds },
          storeId,
          isActive: true
        },
        include: {
          inventory: {
            where: { storeId }
          }
        }
      })

      if (products.length !== productIds.length) {
        throw new Error('One or more products not found')
      }

      // Check inventory availability
      for (const item of data.items) {
        const product = products.find(p => p.id === item.productId)
        if (!product) continue

        const inventory = product.inventory[0]
        if (!inventory || inventory.quantity < item.quantity) {
          throw new Error(`Insufficient stock for product: ${product.name}. Available: ${inventory?.quantity || 0}, Required: ${item.quantity}`)
        }
      }

      // Generate order number
      const orderCount = await tx.b2BOrder.count({ where: { storeId } })
      const orderNo = `B2B-${String(orderCount + 1).padStart(6, '0')}`

      // Calculate totals
      let subtotal = 0
      let totalTax = 0
      let totalItemDiscount = 0

      const orderItems = data.items.map(item => {
        const itemSubtotal = item.quantity * item.unitPrice
        let itemDiscount = 0
        
        if (item.discountType === 'PERCENTAGE') {
          itemDiscount = (itemSubtotal * item.discount) / 100
        } else {
          itemDiscount = item.discount
        }

        const itemTaxableAmount = itemSubtotal - itemDiscount
        const itemTax = (itemTaxableAmount * item.taxRate) / 100

        subtotal += itemSubtotal
        totalItemDiscount += itemDiscount
        totalTax += itemTax

        return {
          productId: item.productId,
          variantId: item.variantId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          discount: itemDiscount,
          taxRate: item.taxRate,
          taxAmount: itemTax,
          totalPrice: itemTaxableAmount + itemTax
        }
      })

      // Apply overall discount
      let overallDiscount = data.discount || 0
      if (data.discountType === 'PERCENTAGE') {
        overallDiscount = (subtotal * overallDiscount) / 100
      }

      const totalDiscount = totalItemDiscount + overallDiscount
      const finalTotal = subtotal - totalDiscount + totalTax

      // Create B2B order
      const order = await tx.b2BOrder.create({
        data: {
          orderNo,
          customerId: data.customerId,
          orderType: data.orderType,
          priority: data.priority,
          expectedDeliveryDate: data.expectedDeliveryDate ? new Date(data.expectedDeliveryDate) : undefined,
          paymentTerms: data.paymentTerms,
          subtotal,
          discount: totalDiscount,
          taxAmount: totalTax,
          totalAmount: finalTotal,
          status: 'PENDING',
          paymentStatus: 'UNPAID',
          shippingAddress: data.shippingAddress,
          billingAddress: data.billingAddress,
          notes: data.notes,
          internalNotes: data.internalNotes,
          createdById: user.id,
          storeId,
          items: {
            create: orderItems
          }
        },
        include: {
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                  unit: true
                }
              }
            }
          },
          customer: {
            select: {
              id: true,
              name: true,
              phone: true,
              email: true
            }
          }
        }
      })

      // Reserve inventory (optional - depends on business logic)
      for (const item of data.items) {
        await tx.inventoryReservation.create({
          data: {
            productId: item.productId,
            variantId: item.variantId,
            quantity: item.quantity,
            orderId: order.id,
            orderType: 'B2B',
            storeId
          }
        })
      }

      return order
    })

    // Create audit log
    await createAuditLog(
      'CREATE',
      'B2B_ORDER',
      `Created B2B order: ${result.orderNo} - Total: ₹${result.totalAmount} - Customer: ${result.customer.name}`,
      user.id,
      storeId
    )

    // Create notification for approval if required
    if (result.totalAmount > 50000) { // Configurable threshold
      const approvers = await prisma.user.findMany({
        where: {
          storeId,
          role: { in: ['FOUNDER', 'SUPER_ADMIN', 'ADMIN'] },
          isActive: true
        },
        select: { id: true }
      })

      for (const approver of approvers) {
        await prisma.notification.create({
          data: {
            title: 'B2B Order Approval Required',
            message: `B2B order ${result.orderNo} (₹${result.totalAmount}) requires approval`,
            type: 'WARNING',
            userId: approver.id,
            storeId
          }
        })
      }
    }

    return createSuccessResponse(result, 'B2B order created successfully')

  } catch (error) {
    console.error('Error creating B2B order:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to create B2B order',
      400
    )
  }
})

// Helper function to calculate order total
function calculateOrderTotal(items: any[], discount: number, discountType: string): number {
  let subtotal = 0
  let totalTax = 0
  let totalItemDiscount = 0

  items.forEach(item => {
    const itemSubtotal = item.quantity * item.unitPrice
    let itemDiscount = 0
    
    if (item.discountType === 'PERCENTAGE') {
      itemDiscount = (itemSubtotal * item.discount) / 100
    } else {
      itemDiscount = item.discount
    }

    const itemTaxableAmount = itemSubtotal - itemDiscount
    const itemTax = (itemTaxableAmount * item.taxRate) / 100

    subtotal += itemSubtotal
    totalItemDiscount += itemDiscount
    totalTax += itemTax
  })

  let overallDiscount = discount || 0
  if (discountType === 'PERCENTAGE') {
    overallDiscount = (subtotal * overallDiscount) / 100
  }

  const totalDiscount = totalItemDiscount + overallDiscount
  return subtotal - totalDiscount + totalTax
}
