import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  getPaginationParams,
  buildSearchFilter,
  createPaginatedResponse,
  createAuditLog,
  validateStoreAccess,
  buildDateRangeFilter
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const feedbackSchema = z.object({
  type: z.enum(['FEATURE_REQUEST', 'BUG_REPORT', 'IMPROVEMENT', 'COMPLAINT', 'COMPLIMENT', 'GENERAL']),
  category: z.enum(['PRODUCT', 'SERVICE', 'SUPPORT', 'BILLING', 'PERFORMANCE', 'UI_UX', 'INTEGRATION']),
  title: z.string().min(1, 'Title is required'),
  description: z.string().min(1, 'Description is required'),
  rating: z.number().min(1).max(5).optional(),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH']).default('MEDIUM'),
  tags: z.array(z.string()).optional(),
  attachments: z.array(z.object({
    fileName: z.string(),
    fileUrl: z.string(),
    fileSize: z.number(),
    mimeType: z.string()
  })).optional(),
  isAnonymous: z.boolean().default(false),
  contactEmail: z.string().email().optional(),
  expectedOutcome: z.string().optional(),
  stepsToReproduce: z.string().optional(),
  browserInfo: z.object({
    userAgent: z.string(),
    viewport: z.string(),
    url: z.string()
  }).optional(),
  metadata: z.record(z.any()).optional()
})

const responseSchema = z.object({
  message: z.string().min(1, 'Response message is required'),
  status: z.enum(['ACKNOWLEDGED', 'IN_REVIEW', 'PLANNED', 'IN_PROGRESS', 'COMPLETED', 'REJECTED']).optional(),
  estimatedCompletion: z.string().optional(),
  isPublic: z.boolean().default(true),
  attachments: z.array(z.object({
    fileName: z.string(),
    fileUrl: z.string(),
    fileSize: z.number(),
    mimeType: z.string()
  })).optional()
})

// GET /api/feedback - Get feedback with filtering and analytics
export const GET = withPermission('FEEDBACK', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { page, limit, skip, search, sortBy, sortOrder } = getPaginationParams(request)
  const { searchParams } = new URL(request.url)
  
  const type = searchParams.get('type')
  const category = searchParams.get('category')
  const status = searchParams.get('status')
  const priority = searchParams.get('priority')
  const rating = searchParams.get('rating')
  const startDate = searchParams.get('startDate')
  const endDate = searchParams.get('endDate')
  const includeResponses = searchParams.get('responses') === 'true'
  const includeAnalytics = searchParams.get('analytics') === 'true'

  try {
    // Build filters
    const where: any = {
      storeId,
      ...buildSearchFilter(search, ['title', 'description']),
      ...buildDateRangeFilter(startDate || undefined, endDate || undefined, 'createdAt')
    }

    if (type) {
      where.type = type
    }

    if (category) {
      where.category = category
    }

    if (status) {
      where.status = status
    }

    if (priority) {
      where.priority = priority
    }

    if (rating) {
      where.rating = parseInt(rating)
    }

    // Non-admin users can only see their own feedback or public responses
    if (!['FOUNDER', 'SUPER_ADMIN', 'ADMIN'].includes(user.role)) {
      where.OR = [
        { createdById: user.id },
        { isAnonymous: false, responses: { some: { isPublic: true } } }
      ]
    }

    // Get total count
    const total = await prisma.feedback.count({ where })

    // Get feedback with pagination
    const feedback = await prisma.feedback.findMany({
      where,
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        responses: includeResponses ? {
          include: {
            createdBy: {
              select: {
                id: true,
                name: true,
                role: true
              }
            }
          },
          orderBy: { createdAt: 'desc' }
        } : false,
        votes: {
          select: {
            userId: true,
            voteType: true
          }
        },
        _count: {
          select: {
            responses: true,
            votes: true
          }
        }
      },
      orderBy: { [sortBy]: sortOrder },
      skip,
      take: limit
    })

    // Add calculated fields
    const feedbackWithCalculations = feedback.map(item => ({
      ...item,
      responseCount: item._count.responses,
      voteCount: item._count.votes,
      upvotes: item.votes.filter(v => v.voteType === 'UP').length,
      downvotes: item.votes.filter(v => v.voteType === 'DOWN').length,
      userVote: item.votes.find(v => v.userId === user.id)?.voteType || null,
      timeAgo: getTimeAgo(item.createdAt),
      isResolved: item.status === 'COMPLETED',
      urgencyScore: calculateUrgencyScore(item),
      sentiment: analyzeSentiment(item.description)
    }))

    let response: any = {
      feedback: feedbackWithCalculations,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }

    // Include analytics if requested
    if (includeAnalytics) {
      response.analytics = await getFeedbackAnalytics(storeId, where)
    }

    return createSuccessResponse(response)

  } catch (error) {
    console.error('Error fetching feedback:', error)
    return createErrorResponse('Failed to fetch feedback', 500)
  }
})

// POST /api/feedback - Submit new feedback
export const POST = withPermission('FEEDBACK', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const data = feedbackSchema.parse(body)

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Generate feedback number
      const feedbackCount = await tx.feedback.count({ where: { storeId } })
      const feedbackNumber = `FB-${String(feedbackCount + 1).padStart(6, '0')}`

      // Create feedback
      const feedback = await tx.feedback.create({
        data: {
          feedbackNumber,
          type: data.type,
          category: data.category,
          title: data.title,
          description: data.description,
          rating: data.rating,
          priority: data.priority,
          tags: data.tags,
          attachments: data.attachments,
          isAnonymous: data.isAnonymous,
          contactEmail: data.contactEmail,
          expectedOutcome: data.expectedOutcome,
          stepsToReproduce: data.stepsToReproduce,
          browserInfo: data.browserInfo,
          metadata: data.metadata,
          status: 'SUBMITTED',
          createdById: data.isAnonymous ? null : user.id,
          storeId
        },
        include: {
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      })

      // Auto-categorize and prioritize based on content
      const autoAnalysis = await analyzeContent(feedback.description, feedback.type)
      if (autoAnalysis.suggestedPriority !== feedback.priority) {
        await tx.feedback.update({
          where: { id: feedback.id },
          data: {
            priority: autoAnalysis.suggestedPriority,
            metadata: {
              ...feedback.metadata,
              autoAnalysis
            }
          }
        })
      }

      return feedback
    })

    // Send notifications to support team
    await notifyFeedbackTeam(result)

    // Create audit log
    await createAuditLog(
      'CREATE',
      'FEEDBACK',
      `Submitted feedback: ${result.feedbackNumber} - ${result.title}`,
      user.id,
      storeId
    )

    return createSuccessResponse(result, 'Feedback submitted successfully')

  } catch (error) {
    console.error('Error submitting feedback:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to submit feedback',
      400
    )
  }
})

// POST /api/feedback/[id]/vote - Vote on feedback
export const PUT = withPermission('FEEDBACK', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathParts = url.pathname.split('/')
  const feedbackId = pathParts[pathParts.length - 2] // Get feedback ID from path
  const body = await request.json()

  const voteSchema = z.object({
    voteType: z.enum(['UP', 'DOWN'])
  })

  const { voteType } = voteSchema.parse(body)

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Check if feedback exists
      const feedback = await tx.feedback.findFirst({
        where: { id: feedbackId, storeId }
      })

      if (!feedback) {
        throw new Error('Feedback not found')
      }

      // Check if user already voted
      const existingVote = await tx.feedbackVote.findFirst({
        where: {
          feedbackId,
          userId: user.id
        }
      })

      if (existingVote) {
        if (existingVote.voteType === voteType) {
          // Remove vote if same type
          await tx.feedbackVote.delete({
            where: { id: existingVote.id }
          })
          return { action: 'removed', voteType: null }
        } else {
          // Update vote type
          await tx.feedbackVote.update({
            where: { id: existingVote.id },
            data: { voteType }
          })
          return { action: 'updated', voteType }
        }
      } else {
        // Create new vote
        await tx.feedbackVote.create({
          data: {
            feedbackId,
            userId: user.id,
            voteType,
            storeId
          }
        })
        return { action: 'created', voteType }
      }
    })

    return createSuccessResponse(result, 'Vote recorded successfully')

  } catch (error) {
    console.error('Error recording vote:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to record vote',
      400
    )
  }
})

// POST /api/feedback/[id]/respond - Respond to feedback
export const POST = withPermission('FEEDBACK', 'UPDATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathParts = url.pathname.split('/')
  const feedbackId = pathParts[pathParts.length - 2] // Get feedback ID from path
  const body = await request.json()
  const data = responseSchema.parse(body)

  // Only admins can respond to feedback
  if (!['FOUNDER', 'SUPER_ADMIN', 'ADMIN'].includes(user.role)) {
    return createErrorResponse('Insufficient permissions to respond to feedback', 403)
  }

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Verify feedback exists
      const feedback = await tx.feedback.findFirst({
        where: { id: feedbackId, storeId },
        include: {
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      })

      if (!feedback) {
        throw new Error('Feedback not found')
      }

      // Create response
      const response = await tx.feedbackResponse.create({
        data: {
          feedbackId,
          message: data.message,
          isPublic: data.isPublic,
          attachments: data.attachments,
          createdById: user.id,
          storeId
        },
        include: {
          createdBy: {
            select: {
              id: true,
              name: true,
              role: true
            }
          }
        }
      })

      // Update feedback status if provided
      if (data.status) {
        await tx.feedback.update({
          where: { id: feedbackId },
          data: {
            status: data.status,
            estimatedCompletion: data.estimatedCompletion ? new Date(data.estimatedCompletion) : undefined,
            updatedAt: new Date()
          }
        })
      }

      return { response, feedback }
    })

    // Send notification to feedback submitter
    if (result.feedback.createdById && data.isPublic) {
      await prisma.notification.create({
        data: {
          title: 'Response to Your Feedback',
          message: `We've responded to your feedback: ${result.feedback.title}`,
          type: 'INFO',
          category: 'FEEDBACK',
          userId: result.feedback.createdById,
          storeId
        }
      })
    }

    return createSuccessResponse(result.response, 'Response added successfully')

  } catch (error) {
    console.error('Error responding to feedback:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to respond to feedback',
      400
    )
  }
})

// Helper functions

async function getFeedbackAnalytics(storeId: string, baseWhere: any) {
  const [
    totalFeedback,
    byType,
    byCategory,
    byStatus,
    byRating,
    averageRating,
    responseTime,
    topTags
  ] = await Promise.all([
    // Total feedback count
    prisma.feedback.count({ where: baseWhere }),

    // Feedback by type
    prisma.feedback.groupBy({
      by: ['type'],
      where: baseWhere,
      _count: true
    }),

    // Feedback by category
    prisma.feedback.groupBy({
      by: ['category'],
      where: baseWhere,
      _count: true
    }),

    // Feedback by status
    prisma.feedback.groupBy({
      by: ['status'],
      where: baseWhere,
      _count: true
    }),

    // Feedback by rating
    prisma.feedback.groupBy({
      by: ['rating'],
      where: { ...baseWhere, rating: { not: null } },
      _count: true
    }),

    // Average rating
    prisma.feedback.aggregate({
      where: { ...baseWhere, rating: { not: null } },
      _avg: { rating: true }
    }),

    // Average response time
    prisma.feedback.findMany({
      where: {
        ...baseWhere,
        responses: { some: {} }
      },
      include: {
        responses: {
          orderBy: { createdAt: 'asc' },
          take: 1
        }
      }
    }).then(items => {
      const responseTimes = items.map(item => {
        if (item.responses.length === 0) return null
        const created = new Date(item.createdAt)
        const responded = new Date(item.responses[0].createdAt)
        return (responded.getTime() - created.getTime()) / (1000 * 60 * 60) // hours
      }).filter(Boolean)
      
      return responseTimes.length > 0 
        ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length 
        : 0
    }),

    // Top tags
    prisma.feedback.findMany({
      where: { ...baseWhere, tags: { not: null } },
      select: { tags: true }
    }).then(items => {
      const tagCounts = new Map()
      items.forEach(item => {
        if (item.tags) {
          item.tags.forEach(tag => {
            tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1)
          })
        }
      })
      
      return Array.from(tagCounts.entries())
        .sort(([, a], [, b]) => b - a)
        .slice(0, 10)
        .map(([tag, count]) => ({ tag, count }))
    })
  ])

  return {
    summary: {
      total: totalFeedback,
      averageRating: averageRating._avg.rating || 0,
      averageResponseTime: responseTime
    },
    distribution: {
      byType: byType.map(item => ({ type: item.type, count: item._count })),
      byCategory: byCategory.map(item => ({ category: item.category, count: item._count })),
      byStatus: byStatus.map(item => ({ status: item.status, count: item._count })),
      byRating: byRating.map(item => ({ rating: item.rating, count: item._count }))
    },
    topTags
  }
}

function calculateUrgencyScore(feedback: any): number {
  let score = 0
  
  // Priority weight
  const priorityWeights = { LOW: 1, MEDIUM: 2, HIGH: 3 }
  score += priorityWeights[feedback.priority as keyof typeof priorityWeights] || 0
  
  // Type weight
  const typeWeights = { BUG_REPORT: 3, COMPLAINT: 2, FEATURE_REQUEST: 1, IMPROVEMENT: 1, COMPLIMENT: 0, GENERAL: 1 }
  score += typeWeights[feedback.type as keyof typeof typeWeights] || 0
  
  // Rating weight (lower rating = higher urgency)
  if (feedback.rating) {
    score += (6 - feedback.rating)
  }
  
  // Time factor (older feedback gets higher urgency)
  const daysOld = (Date.now() - new Date(feedback.createdAt).getTime()) / (1000 * 60 * 60 * 24)
  score += Math.min(daysOld / 7, 2) // Max 2 points for age
  
  return Math.round(score * 10) / 10
}

function analyzeSentiment(text: string): 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL' {
  // Simple sentiment analysis based on keywords
  const positiveWords = ['good', 'great', 'excellent', 'amazing', 'love', 'perfect', 'awesome', 'fantastic']
  const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'horrible', 'worst', 'broken', 'useless']
  
  const lowerText = text.toLowerCase()
  const positiveCount = positiveWords.filter(word => lowerText.includes(word)).length
  const negativeCount = negativeWords.filter(word => lowerText.includes(word)).length
  
  if (positiveCount > negativeCount) return 'POSITIVE'
  if (negativeCount > positiveCount) return 'NEGATIVE'
  return 'NEUTRAL'
}

async function analyzeContent(description: string, type: string): Promise<any> {
  // Simple content analysis for auto-prioritization
  const urgentKeywords = ['urgent', 'critical', 'broken', 'not working', 'error', 'crash']
  const highKeywords = ['important', 'needed', 'asap', 'soon']
  
  const lowerDescription = description.toLowerCase()
  
  let suggestedPriority = 'MEDIUM'
  
  if (urgentKeywords.some(keyword => lowerDescription.includes(keyword))) {
    suggestedPriority = 'HIGH'
  } else if (highKeywords.some(keyword => lowerDescription.includes(keyword))) {
    suggestedPriority = 'MEDIUM'
  }
  
  // Bug reports are generally higher priority
  if (type === 'BUG_REPORT') {
    suggestedPriority = suggestedPriority === 'MEDIUM' ? 'HIGH' : suggestedPriority
  }
  
  return {
    suggestedPriority,
    sentiment: analyzeSentiment(description),
    keywordsFound: urgentKeywords.filter(keyword => lowerDescription.includes(keyword))
  }
}

async function notifyFeedbackTeam(feedback: any) {
  // Get feedback team members (admins)
  const feedbackTeam = await prisma.user.findMany({
    where: {
      storeId: feedback.storeId,
      role: { in: ['FOUNDER', 'SUPER_ADMIN', 'ADMIN'] },
      isActive: true
    }
  })

  // Create notifications
  for (const member of feedbackTeam) {
    await prisma.notification.create({
      data: {
        title: 'New Feedback Submitted',
        message: `${feedback.type}: ${feedback.title}`,
        type: 'INFO',
        category: 'FEEDBACK',
        priority: feedback.priority,
        userId: member.id,
        storeId: feedback.storeId
      }
    })
  }
}

function getTimeAgo(date: Date): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) return 'Just now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`
  return date.toLocaleDateString()
}
