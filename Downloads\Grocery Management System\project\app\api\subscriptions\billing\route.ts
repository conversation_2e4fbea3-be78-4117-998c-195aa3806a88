import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  getPaginationParams,
  buildSearchFilter,
  createPaginatedResponse,
  createAuditLog,
  validateStoreAccess,
  buildDateRangeFilter
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const paymentMethodSchema = z.object({
  type: z.enum(['CREDIT_CARD', 'DEBIT_CARD', 'BANK_TRANSFER', 'UPI', 'WALLET']),
  details: z.object({
    cardNumber: z.string().optional(),
    expiryMonth: z.number().min(1).max(12).optional(),
    expiryYear: z.number().min(2024).optional(),
    cardholderName: z.string().optional(),
    bankAccount: z.string().optional(),
    ifscCode: z.string().optional(),
    upiId: z.string().optional(),
    walletProvider: z.string().optional()
  }),
  isDefault: z.boolean().default(false),
  billingAddress: z.object({
    street: z.string(),
    city: z.string(),
    state: z.string(),
    postalCode: z.string(),
    country: z.string().default('IN')
  }).optional()
})

const invoicePaymentSchema = z.object({
  invoiceId: z.string().min(1, 'Invoice ID is required'),
  paymentMethodId: z.string().optional(),
  amount: z.number().min(0),
  paymentReference: z.string().optional(),
  notes: z.string().optional()
})

// GET /api/subscriptions/billing - Get billing information and invoices
export const GET = withPermission('BILLING', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { page, limit, skip, search, sortBy, sortOrder } = getPaginationParams(request)
  const { searchParams } = new URL(request.url)
  
  const status = searchParams.get('status')
  const startDate = searchParams.get('startDate')
  const endDate = searchParams.get('endDate')
  const includePaymentMethods = searchParams.get('paymentMethods') === 'true'
  const includeSummary = searchParams.get('summary') === 'true'

  try {
    // Build filters for invoices
    const where: any = {
      storeId,
      ...buildSearchFilter(search, ['invoiceNumber']),
      ...buildDateRangeFilter(startDate || undefined, endDate || undefined, 'createdAt')
    }

    if (status) {
      where.status = status
    }

    // Get total count
    const total = await prisma.subscriptionInvoice.count({ where })

    // Get invoices with pagination
    const invoices = await prisma.subscriptionInvoice.findMany({
      where,
      include: {
        subscription: {
          include: {
            plan: {
              select: {
                name: true,
                category: true
              }
            }
          }
        },
        payments: {
          include: {
            paymentMethod: {
              select: {
                type: true,
                details: true
              }
            }
          }
        }
      },
      orderBy: { [sortBy]: sortOrder },
      skip,
      take: limit
    })

    // Add calculated fields
    const invoicesWithCalculations = invoices.map(invoice => ({
      ...invoice,
      daysOverdue: calculateDaysOverdue(invoice),
      isOverdue: isInvoiceOverdue(invoice),
      paidAmount: invoice.payments.reduce((sum: number, payment: any) => sum + payment.amount, 0),
      remainingAmount: invoice.amount - invoice.payments.reduce((sum: number, payment: any) => sum + payment.amount, 0),
      paymentStatus: determinePaymentStatus(invoice)
    }))

    let response: any = {
      invoices: invoicesWithCalculations,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }

    // Include payment methods if requested
    if (includePaymentMethods) {
      const paymentMethods = await prisma.paymentMethod.findMany({
        where: { storeId, isActive: true },
        orderBy: [
          { isDefault: 'desc' },
          { createdAt: 'desc' }
        ]
      })

      response.paymentMethods = paymentMethods.map(method => ({
        ...method,
        details: maskSensitiveData(method.details, method.type)
      }))
    }

    // Include billing summary if requested
    if (includeSummary) {
      response.summary = await getBillingSummary(storeId)
    }

    return createSuccessResponse(response)

  } catch (error) {
    console.error('Error fetching billing information:', error)
    return createErrorResponse('Failed to fetch billing information', 500)
  }
})

// POST /api/subscriptions/billing/payment-methods - Add payment method
export const POST = withPermission('BILLING', 'UPDATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const data = paymentMethodSchema.parse(body)

  try {
    const result = await prisma.$transaction(async (tx) => {
      // If this is set as default, unset other default methods
      if (data.isDefault) {
        await tx.paymentMethod.updateMany({
          where: { storeId, isDefault: true },
          data: { isDefault: false }
        })
      }

      // Encrypt sensitive data before storing
      const encryptedDetails = encryptPaymentDetails(data.details, data.type)

      // Create payment method
      const paymentMethod = await tx.paymentMethod.create({
        data: {
          type: data.type,
          details: encryptedDetails,
          isDefault: data.isDefault,
          billingAddress: data.billingAddress,
          storeId,
          createdById: user.id
        }
      })

      return paymentMethod
    })

    // Create audit log
    await createAuditLog(
      'CREATE',
      'PAYMENT_METHOD',
      `Added payment method: ${data.type}`,
      user.id,
      storeId
    )

    return createSuccessResponse({
      ...result,
      details: maskSensitiveData(result.details, result.type)
    }, 'Payment method added successfully')

  } catch (error) {
    console.error('Error adding payment method:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to add payment method',
      400
    )
  }
})

// PUT /api/subscriptions/billing/pay - Process invoice payment
export const PUT = withPermission('BILLING', 'UPDATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const data = invoicePaymentSchema.parse(body)

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Get invoice
      const invoice = await tx.subscriptionInvoice.findFirst({
        where: {
          id: data.invoiceId,
          storeId
        },
        include: {
          subscription: true,
          payments: true
        }
      })

      if (!invoice) {
        throw new Error('Invoice not found')
      }

      if (invoice.status === 'PAID') {
        throw new Error('Invoice is already paid')
      }

      // Calculate remaining amount
      const paidAmount = invoice.payments.reduce((sum: number, payment: any) => sum + payment.amount, 0)
      const remainingAmount = invoice.amount - paidAmount

      if (data.amount > remainingAmount) {
        throw new Error('Payment amount exceeds remaining balance')
      }

      // Get payment method if specified
      let paymentMethod = null
      if (data.paymentMethodId) {
        paymentMethod = await tx.paymentMethod.findFirst({
          where: {
            id: data.paymentMethodId,
            storeId,
            isActive: true
          }
        })

        if (!paymentMethod) {
          throw new Error('Payment method not found')
        }
      }

      // Process payment (integrate with payment gateway here)
      const paymentResult = await processPayment({
        amount: data.amount,
        currency: invoice.currency,
        paymentMethod,
        reference: data.paymentReference
      })

      if (!paymentResult.success) {
        throw new Error(`Payment failed: ${paymentResult.error}`)
      }

      // Create payment record
      const payment = await tx.subscriptionPayment.create({
        data: {
          invoiceId: invoice.id,
          subscriptionId: invoice.subscriptionId,
          amount: data.amount,
          currency: invoice.currency,
          paymentMethodId: data.paymentMethodId,
          paymentReference: paymentResult.transactionId,
          gatewayResponse: paymentResult.gatewayResponse,
          status: 'COMPLETED',
          notes: data.notes,
          storeId,
          processedById: user.id
        }
      })

      // Update invoice status
      const newPaidAmount = paidAmount + data.amount
      const newStatus = newPaidAmount >= invoice.amount ? 'PAID' : 'PARTIAL'

      const updatedInvoice = await tx.subscriptionInvoice.update({
        where: { id: invoice.id },
        data: {
          status: newStatus,
          paidAt: newStatus === 'PAID' ? new Date() : undefined
        }
      })

      // Update subscription status if needed
      if (newStatus === 'PAID' && invoice.subscription.status === 'PAST_DUE') {
        await tx.subscription.update({
          where: { id: invoice.subscriptionId },
          data: { status: 'ACTIVE' }
        })
      }

      return { payment, invoice: updatedInvoice }
    })

    // Create audit log
    await createAuditLog(
      'CREATE',
      'SUBSCRIPTION_PAYMENT',
      `Processed payment: ${data.amount} for invoice ${data.invoiceId}`,
      user.id,
      storeId
    )

    return createSuccessResponse(result, 'Payment processed successfully')

  } catch (error) {
    console.error('Error processing payment:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to process payment',
      400
    )
  }
})

// Helper functions

async function getBillingSummary(storeId: string) {
  const [
    totalInvoices,
    paidInvoices,
    pendingInvoices,
    overdueInvoices,
    totalRevenue,
    pendingAmount,
    overdueAmount
  ] = await Promise.all([
    // Total invoices
    prisma.subscriptionInvoice.count({ where: { storeId } }),

    // Paid invoices
    prisma.subscriptionInvoice.count({
      where: { storeId, status: 'PAID' }
    }),

    // Pending invoices
    prisma.subscriptionInvoice.count({
      where: { storeId, status: { in: ['PENDING', 'PARTIAL'] } }
    }),

    // Overdue invoices
    prisma.subscriptionInvoice.count({
      where: {
        storeId,
        status: { in: ['PENDING', 'PARTIAL'] },
        dueDate: { lt: new Date() }
      }
    }),

    // Total revenue
    prisma.subscriptionInvoice.aggregate({
      where: { storeId, status: 'PAID' },
      _sum: { amount: true }
    }),

    // Pending amount
    prisma.subscriptionInvoice.aggregate({
      where: { storeId, status: { in: ['PENDING', 'PARTIAL'] } },
      _sum: { amount: true }
    }),

    // Overdue amount
    prisma.subscriptionInvoice.aggregate({
      where: {
        storeId,
        status: { in: ['PENDING', 'PARTIAL'] },
        dueDate: { lt: new Date() }
      },
      _sum: { amount: true }
    })
  ])

  return {
    invoices: {
      total: totalInvoices,
      paid: paidInvoices,
      pending: pendingInvoices,
      overdue: overdueInvoices
    },
    amounts: {
      totalRevenue: totalRevenue._sum.amount || 0,
      pendingAmount: pendingAmount._sum.amount || 0,
      overdueAmount: overdueAmount._sum.amount || 0
    },
    paymentRate: totalInvoices > 0 ? (paidInvoices / totalInvoices) * 100 : 0
  }
}

function calculateDaysOverdue(invoice: any): number {
  if (invoice.status === 'PAID' || !invoice.dueDate) return 0
  
  const now = new Date()
  const dueDate = new Date(invoice.dueDate)
  
  if (dueDate >= now) return 0
  
  return Math.floor((now.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24))
}

function isInvoiceOverdue(invoice: any): boolean {
  return calculateDaysOverdue(invoice) > 0
}

function determinePaymentStatus(invoice: any): string {
  if (invoice.status === 'PAID') return 'PAID'
  
  const paidAmount = invoice.payments.reduce((sum: number, payment: any) => sum + payment.amount, 0)
  
  if (paidAmount === 0) {
    return isInvoiceOverdue(invoice) ? 'OVERDUE' : 'PENDING'
  }
  
  return 'PARTIAL'
}

function maskSensitiveData(details: any, type: string): any {
  const masked = { ...details }
  
  switch (type) {
    case 'CREDIT_CARD':
    case 'DEBIT_CARD':
      if (masked.cardNumber) {
        masked.cardNumber = '**** **** **** ' + masked.cardNumber.slice(-4)
      }
      break
    case 'BANK_TRANSFER':
      if (masked.bankAccount) {
        masked.bankAccount = '****' + masked.bankAccount.slice(-4)
      }
      break
    case 'UPI':
      if (masked.upiId) {
        const [username, domain] = masked.upiId.split('@')
        masked.upiId = username.slice(0, 2) + '****@' + domain
      }
      break
  }
  
  return masked
}

function encryptPaymentDetails(details: any, type: string): any {
  // In a real implementation, encrypt sensitive data
  // For now, return as-is (would use crypto library)
  return details
}

async function processPayment(paymentData: any): Promise<any> {
  // Mock payment processing
  // In a real implementation, integrate with payment gateway (Stripe, Razorpay, etc.)
  
  try {
    // Simulate payment processing delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Mock successful payment
    return {
      success: true,
      transactionId: `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      gatewayResponse: {
        status: 'success',
        message: 'Payment processed successfully',
        timestamp: new Date().toISOString()
      }
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Payment processing failed',
      gatewayResponse: {
        status: 'failed',
        message: 'Payment failed',
        timestamp: new Date().toISOString()
      }
    }
  }
}
