"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jsonwebtoken";
exports.ids = ["vendor-chunks/jsonwebtoken"];
exports.modules = {

/***/ "(rsc)/./node_modules/jsonwebtoken/decode.js":
/*!*********************************************!*\
  !*** ./node_modules/jsonwebtoken/decode.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar jws = __webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\");\nmodule.exports = function(jwt, options) {\n    options = options || {};\n    var decoded = jws.decode(jwt, options);\n    if (!decoded) {\n        return null;\n    }\n    var payload = decoded.payload;\n    //try parse the payload\n    if (typeof payload === \"string\") {\n        try {\n            var obj = JSON.parse(payload);\n            if (obj !== null && typeof obj === \"object\") {\n                payload = obj;\n            }\n        } catch (e) {}\n    }\n    //return header if `complete` option is enabled.  header includes claims\n    //such as `kid` and `alg` used to select the key within a JWKS needed to\n    //verify the signature\n    if (options.complete === true) {\n        return {\n            header: decoded.header,\n            payload: payload,\n            signature: decoded.signature\n        };\n    }\n    return payload;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/decode.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/index.js":
/*!********************************************!*\
  !*** ./node_modules/jsonwebtoken/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nmodule.exports = {\n    decode: __webpack_require__(/*! ./decode */ \"(rsc)/./node_modules/jsonwebtoken/decode.js\"),\n    verify: __webpack_require__(/*! ./verify */ \"(rsc)/./node_modules/jsonwebtoken/verify.js\"),\n    sign: __webpack_require__(/*! ./sign */ \"(rsc)/./node_modules/jsonwebtoken/sign.js\"),\n    JsonWebTokenError: __webpack_require__(/*! ./lib/JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\"),\n    NotBeforeError: __webpack_require__(/*! ./lib/NotBeforeError */ \"(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\"),\n    TokenExpiredError: __webpack_require__(/*! ./lib/TokenExpiredError */ \"(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\")\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7QUFBQUEsT0FBT0MsT0FBTyxHQUFHO0lBQ2ZDLFFBQVFDLG1CQUFPQSxDQUFDO0lBQ2hCQyxRQUFRRCxtQkFBT0EsQ0FBQztJQUNoQkUsTUFBTUYsbUJBQU9BLENBQUM7SUFDZEcsbUJBQW1CSCxtQkFBT0EsQ0FBQztJQUMzQkksZ0JBQWdCSixtQkFBT0EsQ0FBQztJQUN4QkssbUJBQW1CTCxtQkFBT0EsQ0FBQztBQUM3QiIsInNvdXJjZXMiOlsid2VicGFjazovL2dyb2NlcnktbWFuYWdlbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2luZGV4LmpzPzliZTgiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSB7XG4gIGRlY29kZTogcmVxdWlyZSgnLi9kZWNvZGUnKSxcbiAgdmVyaWZ5OiByZXF1aXJlKCcuL3ZlcmlmeScpLFxuICBzaWduOiByZXF1aXJlKCcuL3NpZ24nKSxcbiAgSnNvbldlYlRva2VuRXJyb3I6IHJlcXVpcmUoJy4vbGliL0pzb25XZWJUb2tlbkVycm9yJyksXG4gIE5vdEJlZm9yZUVycm9yOiByZXF1aXJlKCcuL2xpYi9Ob3RCZWZvcmVFcnJvcicpLFxuICBUb2tlbkV4cGlyZWRFcnJvcjogcmVxdWlyZSgnLi9saWIvVG9rZW5FeHBpcmVkRXJyb3InKSxcbn07XG4iXSwibmFtZXMiOlsibW9kdWxlIiwiZXhwb3J0cyIsImRlY29kZSIsInJlcXVpcmUiLCJ2ZXJpZnkiLCJzaWduIiwiSnNvbldlYlRva2VuRXJyb3IiLCJOb3RCZWZvcmVFcnJvciIsIlRva2VuRXhwaXJlZEVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js":
/*!************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/JsonWebTokenError.js ***!
  \************************************************************/
/***/ ((module) => {

eval("\nvar JsonWebTokenError = function(message, error) {\n    Error.call(this, message);\n    if (Error.captureStackTrace) {\n        Error.captureStackTrace(this, this.constructor);\n    }\n    this.name = \"JsonWebTokenError\";\n    this.message = message;\n    if (error) this.inner = error;\n};\nJsonWebTokenError.prototype = Object.create(Error.prototype);\nJsonWebTokenError.prototype.constructor = JsonWebTokenError;\nmodule.exports = JsonWebTokenError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Kc29uV2ViVG9rZW5FcnJvci5qcyIsIm1hcHBpbmdzIjoiO0FBQUEsSUFBSUEsb0JBQW9CLFNBQVVDLE9BQU8sRUFBRUMsS0FBSztJQUM5Q0MsTUFBTUMsSUFBSSxDQUFDLElBQUksRUFBRUg7SUFDakIsSUFBR0UsTUFBTUUsaUJBQWlCLEVBQUU7UUFDMUJGLE1BQU1FLGlCQUFpQixDQUFDLElBQUksRUFBRSxJQUFJLENBQUNDLFdBQVc7SUFDaEQ7SUFDQSxJQUFJLENBQUNDLElBQUksR0FBRztJQUNaLElBQUksQ0FBQ04sT0FBTyxHQUFHQTtJQUNmLElBQUlDLE9BQU8sSUFBSSxDQUFDTSxLQUFLLEdBQUdOO0FBQzFCO0FBRUFGLGtCQUFrQlMsU0FBUyxHQUFHQyxPQUFPQyxNQUFNLENBQUNSLE1BQU1NLFNBQVM7QUFDM0RULGtCQUFrQlMsU0FBUyxDQUFDSCxXQUFXLEdBQUdOO0FBRTFDWSxPQUFPQyxPQUFPLEdBQUdiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ3JvY2VyeS1tYW5hZ2VtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9qc29ud2VidG9rZW4vbGliL0pzb25XZWJUb2tlbkVycm9yLmpzPzE2ZjIiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIEpzb25XZWJUb2tlbkVycm9yID0gZnVuY3Rpb24gKG1lc3NhZ2UsIGVycm9yKSB7XG4gIEVycm9yLmNhbGwodGhpcywgbWVzc2FnZSk7XG4gIGlmKEVycm9yLmNhcHR1cmVTdGFja1RyYWNlKSB7XG4gICAgRXJyb3IuY2FwdHVyZVN0YWNrVHJhY2UodGhpcywgdGhpcy5jb25zdHJ1Y3Rvcik7XG4gIH1cbiAgdGhpcy5uYW1lID0gJ0pzb25XZWJUb2tlbkVycm9yJztcbiAgdGhpcy5tZXNzYWdlID0gbWVzc2FnZTtcbiAgaWYgKGVycm9yKSB0aGlzLmlubmVyID0gZXJyb3I7XG59O1xuXG5Kc29uV2ViVG9rZW5FcnJvci5wcm90b3R5cGUgPSBPYmplY3QuY3JlYXRlKEVycm9yLnByb3RvdHlwZSk7XG5Kc29uV2ViVG9rZW5FcnJvci5wcm90b3R5cGUuY29uc3RydWN0b3IgPSBKc29uV2ViVG9rZW5FcnJvcjtcblxubW9kdWxlLmV4cG9ydHMgPSBKc29uV2ViVG9rZW5FcnJvcjtcbiJdLCJuYW1lcyI6WyJKc29uV2ViVG9rZW5FcnJvciIsIm1lc3NhZ2UiLCJlcnJvciIsIkVycm9yIiwiY2FsbCIsImNhcHR1cmVTdGFja1RyYWNlIiwiY29uc3RydWN0b3IiLCJuYW1lIiwiaW5uZXIiLCJwcm90b3R5cGUiLCJPYmplY3QiLCJjcmVhdGUiLCJtb2R1bGUiLCJleHBvcnRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js":
/*!*********************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/NotBeforeError.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar JsonWebTokenError = __webpack_require__(/*! ./JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\nvar NotBeforeError = function(message, date) {\n    JsonWebTokenError.call(this, message);\n    this.name = \"NotBeforeError\";\n    this.date = date;\n};\nNotBeforeError.prototype = Object.create(JsonWebTokenError.prototype);\nNotBeforeError.prototype.constructor = NotBeforeError;\nmodule.exports = NotBeforeError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Ob3RCZWZvcmVFcnJvci5qcyIsIm1hcHBpbmdzIjoiO0FBQUEsSUFBSUEsb0JBQW9CQyxtQkFBT0EsQ0FBQztBQUVoQyxJQUFJQyxpQkFBaUIsU0FBVUMsT0FBTyxFQUFFQyxJQUFJO0lBQzFDSixrQkFBa0JLLElBQUksQ0FBQyxJQUFJLEVBQUVGO0lBQzdCLElBQUksQ0FBQ0csSUFBSSxHQUFHO0lBQ1osSUFBSSxDQUFDRixJQUFJLEdBQUdBO0FBQ2Q7QUFFQUYsZUFBZUssU0FBUyxHQUFHQyxPQUFPQyxNQUFNLENBQUNULGtCQUFrQk8sU0FBUztBQUVwRUwsZUFBZUssU0FBUyxDQUFDRyxXQUFXLEdBQUdSO0FBRXZDUyxPQUFPQyxPQUFPLEdBQUdWIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ3JvY2VyeS1tYW5hZ2VtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9qc29ud2VidG9rZW4vbGliL05vdEJlZm9yZUVycm9yLmpzPzg2NjgiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIEpzb25XZWJUb2tlbkVycm9yID0gcmVxdWlyZSgnLi9Kc29uV2ViVG9rZW5FcnJvcicpO1xuXG52YXIgTm90QmVmb3JlRXJyb3IgPSBmdW5jdGlvbiAobWVzc2FnZSwgZGF0ZSkge1xuICBKc29uV2ViVG9rZW5FcnJvci5jYWxsKHRoaXMsIG1lc3NhZ2UpO1xuICB0aGlzLm5hbWUgPSAnTm90QmVmb3JlRXJyb3InO1xuICB0aGlzLmRhdGUgPSBkYXRlO1xufTtcblxuTm90QmVmb3JlRXJyb3IucHJvdG90eXBlID0gT2JqZWN0LmNyZWF0ZShKc29uV2ViVG9rZW5FcnJvci5wcm90b3R5cGUpO1xuXG5Ob3RCZWZvcmVFcnJvci5wcm90b3R5cGUuY29uc3RydWN0b3IgPSBOb3RCZWZvcmVFcnJvcjtcblxubW9kdWxlLmV4cG9ydHMgPSBOb3RCZWZvcmVFcnJvcjsiXSwibmFtZXMiOlsiSnNvbldlYlRva2VuRXJyb3IiLCJyZXF1aXJlIiwiTm90QmVmb3JlRXJyb3IiLCJtZXNzYWdlIiwiZGF0ZSIsImNhbGwiLCJuYW1lIiwicHJvdG90eXBlIiwiT2JqZWN0IiwiY3JlYXRlIiwiY29uc3RydWN0b3IiLCJtb2R1bGUiLCJleHBvcnRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js":
/*!************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/TokenExpiredError.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar JsonWebTokenError = __webpack_require__(/*! ./JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\nvar TokenExpiredError = function(message, expiredAt) {\n    JsonWebTokenError.call(this, message);\n    this.name = \"TokenExpiredError\";\n    this.expiredAt = expiredAt;\n};\nTokenExpiredError.prototype = Object.create(JsonWebTokenError.prototype);\nTokenExpiredError.prototype.constructor = TokenExpiredError;\nmodule.exports = TokenExpiredError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Ub2tlbkV4cGlyZWRFcnJvci5qcyIsIm1hcHBpbmdzIjoiO0FBQUEsSUFBSUEsb0JBQW9CQyxtQkFBT0EsQ0FBQztBQUVoQyxJQUFJQyxvQkFBb0IsU0FBVUMsT0FBTyxFQUFFQyxTQUFTO0lBQ2xESixrQkFBa0JLLElBQUksQ0FBQyxJQUFJLEVBQUVGO0lBQzdCLElBQUksQ0FBQ0csSUFBSSxHQUFHO0lBQ1osSUFBSSxDQUFDRixTQUFTLEdBQUdBO0FBQ25CO0FBRUFGLGtCQUFrQkssU0FBUyxHQUFHQyxPQUFPQyxNQUFNLENBQUNULGtCQUFrQk8sU0FBUztBQUV2RUwsa0JBQWtCSyxTQUFTLENBQUNHLFdBQVcsR0FBR1I7QUFFMUNTLE9BQU9DLE9BQU8sR0FBR1YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ncm9jZXJ5LW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL2pzb253ZWJ0b2tlbi9saWIvVG9rZW5FeHBpcmVkRXJyb3IuanM/OTBlYyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgSnNvbldlYlRva2VuRXJyb3IgPSByZXF1aXJlKCcuL0pzb25XZWJUb2tlbkVycm9yJyk7XG5cbnZhciBUb2tlbkV4cGlyZWRFcnJvciA9IGZ1bmN0aW9uIChtZXNzYWdlLCBleHBpcmVkQXQpIHtcbiAgSnNvbldlYlRva2VuRXJyb3IuY2FsbCh0aGlzLCBtZXNzYWdlKTtcbiAgdGhpcy5uYW1lID0gJ1Rva2VuRXhwaXJlZEVycm9yJztcbiAgdGhpcy5leHBpcmVkQXQgPSBleHBpcmVkQXQ7XG59O1xuXG5Ub2tlbkV4cGlyZWRFcnJvci5wcm90b3R5cGUgPSBPYmplY3QuY3JlYXRlKEpzb25XZWJUb2tlbkVycm9yLnByb3RvdHlwZSk7XG5cblRva2VuRXhwaXJlZEVycm9yLnByb3RvdHlwZS5jb25zdHJ1Y3RvciA9IFRva2VuRXhwaXJlZEVycm9yO1xuXG5tb2R1bGUuZXhwb3J0cyA9IFRva2VuRXhwaXJlZEVycm9yOyJdLCJuYW1lcyI6WyJKc29uV2ViVG9rZW5FcnJvciIsInJlcXVpcmUiLCJUb2tlbkV4cGlyZWRFcnJvciIsIm1lc3NhZ2UiLCJleHBpcmVkQXQiLCJjYWxsIiwibmFtZSIsInByb3RvdHlwZSIsIk9iamVjdCIsImNyZWF0ZSIsImNvbnN0cnVjdG9yIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js":
/*!************************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst semver = __webpack_require__(/*! semver */ \"(rsc)/./node_modules/semver/index.js\");\nmodule.exports = semver.satisfies(process.version, \">=15.7.0\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9hc3ltbWV0cmljS2V5RGV0YWlsc1N1cHBvcnRlZC5qcyIsIm1hcHBpbmdzIjoiO0FBQUEsTUFBTUEsU0FBU0MsbUJBQU9BLENBQUM7QUFFdkJDLE9BQU9DLE9BQU8sR0FBR0gsT0FBT0ksU0FBUyxDQUFDQyxRQUFRQyxPQUFPLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ncm9jZXJ5LW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL2pzb253ZWJ0b2tlbi9saWIvYXN5bW1ldHJpY0tleURldGFpbHNTdXBwb3J0ZWQuanM/NzNkOSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBzZW12ZXIgPSByZXF1aXJlKCdzZW12ZXInKTtcblxubW9kdWxlLmV4cG9ydHMgPSBzZW12ZXIuc2F0aXNmaWVzKHByb2Nlc3MudmVyc2lvbiwgJz49MTUuNy4wJyk7XG4iXSwibmFtZXMiOlsic2VtdmVyIiwicmVxdWlyZSIsIm1vZHVsZSIsImV4cG9ydHMiLCJzYXRpc2ZpZXMiLCJwcm9jZXNzIiwidmVyc2lvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js":
/*!******************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/psSupported.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar semver = __webpack_require__(/*! semver */ \"(rsc)/./node_modules/semver/index.js\");\nmodule.exports = semver.satisfies(process.version, \"^6.12.0 || >=8.0.0\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9wc1N1cHBvcnRlZC5qcyIsIm1hcHBpbmdzIjoiO0FBQUEsSUFBSUEsU0FBU0MsbUJBQU9BLENBQUM7QUFFckJDLE9BQU9DLE9BQU8sR0FBR0gsT0FBT0ksU0FBUyxDQUFDQyxRQUFRQyxPQUFPLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ncm9jZXJ5LW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL2pzb253ZWJ0b2tlbi9saWIvcHNTdXBwb3J0ZWQuanM/YzhkNCJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgc2VtdmVyID0gcmVxdWlyZSgnc2VtdmVyJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gc2VtdmVyLnNhdGlzZmllcyhwcm9jZXNzLnZlcnNpb24sICdeNi4xMi4wIHx8ID49OC4wLjAnKTtcbiJdLCJuYW1lcyI6WyJzZW12ZXIiLCJyZXF1aXJlIiwibW9kdWxlIiwiZXhwb3J0cyIsInNhdGlzZmllcyIsInByb2Nlc3MiLCJ2ZXJzaW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js":
/*!********************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst semver = __webpack_require__(/*! semver */ \"(rsc)/./node_modules/semver/index.js\");\nmodule.exports = semver.satisfies(process.version, \">=16.9.0\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9yc2FQc3NLZXlEZXRhaWxzU3VwcG9ydGVkLmpzIiwibWFwcGluZ3MiOiI7QUFBQSxNQUFNQSxTQUFTQyxtQkFBT0EsQ0FBQztBQUV2QkMsT0FBT0MsT0FBTyxHQUFHSCxPQUFPSSxTQUFTLENBQUNDLFFBQVFDLE9BQU8sRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL2dyb2NlcnktbWFuYWdlbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9yc2FQc3NLZXlEZXRhaWxzU3VwcG9ydGVkLmpzP2Y5MDgiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3Qgc2VtdmVyID0gcmVxdWlyZSgnc2VtdmVyJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gc2VtdmVyLnNhdGlzZmllcyhwcm9jZXNzLnZlcnNpb24sICc+PTE2LjkuMCcpO1xuIl0sIm5hbWVzIjpbInNlbXZlciIsInJlcXVpcmUiLCJtb2R1bGUiLCJleHBvcnRzIiwic2F0aXNmaWVzIiwicHJvY2VzcyIsInZlcnNpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/timespan.js":
/*!***************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/timespan.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar ms = __webpack_require__(/*! ms */ \"(rsc)/./node_modules/ms/index.js\");\nmodule.exports = function(time, iat) {\n    var timestamp = iat || Math.floor(Date.now() / 1000);\n    if (typeof time === \"string\") {\n        var milliseconds = ms(time);\n        if (typeof milliseconds === \"undefined\") {\n            return;\n        }\n        return Math.floor(timestamp + milliseconds / 1000);\n    } else if (typeof time === \"number\") {\n        return timestamp + time;\n    } else {\n        return;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi90aW1lc3Bhbi5qcyIsIm1hcHBpbmdzIjoiO0FBQUEsSUFBSUEsS0FBS0MsbUJBQU9BLENBQUM7QUFFakJDLE9BQU9DLE9BQU8sR0FBRyxTQUFVQyxJQUFJLEVBQUVDLEdBQUc7SUFDbEMsSUFBSUMsWUFBWUQsT0FBT0UsS0FBS0MsS0FBSyxDQUFDQyxLQUFLQyxHQUFHLEtBQUs7SUFFL0MsSUFBSSxPQUFPTixTQUFTLFVBQVU7UUFDNUIsSUFBSU8sZUFBZVgsR0FBR0k7UUFDdEIsSUFBSSxPQUFPTyxpQkFBaUIsYUFBYTtZQUN2QztRQUNGO1FBQ0EsT0FBT0osS0FBS0MsS0FBSyxDQUFDRixZQUFZSyxlQUFlO0lBQy9DLE9BQU8sSUFBSSxPQUFPUCxTQUFTLFVBQVU7UUFDbkMsT0FBT0UsWUFBWUY7SUFDckIsT0FBTztRQUNMO0lBQ0Y7QUFFRiIsInNvdXJjZXMiOlsid2VicGFjazovL2dyb2NlcnktbWFuYWdlbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi90aW1lc3Bhbi5qcz9jZWYwIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBtcyA9IHJlcXVpcmUoJ21zJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gKHRpbWUsIGlhdCkge1xuICB2YXIgdGltZXN0YW1wID0gaWF0IHx8IE1hdGguZmxvb3IoRGF0ZS5ub3coKSAvIDEwMDApO1xuXG4gIGlmICh0eXBlb2YgdGltZSA9PT0gJ3N0cmluZycpIHtcbiAgICB2YXIgbWlsbGlzZWNvbmRzID0gbXModGltZSk7XG4gICAgaWYgKHR5cGVvZiBtaWxsaXNlY29uZHMgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIHJldHVybiBNYXRoLmZsb29yKHRpbWVzdGFtcCArIG1pbGxpc2Vjb25kcyAvIDEwMDApO1xuICB9IGVsc2UgaWYgKHR5cGVvZiB0aW1lID09PSAnbnVtYmVyJykge1xuICAgIHJldHVybiB0aW1lc3RhbXAgKyB0aW1lO1xuICB9IGVsc2Uge1xuICAgIHJldHVybjtcbiAgfVxuXG59OyJdLCJuYW1lcyI6WyJtcyIsInJlcXVpcmUiLCJtb2R1bGUiLCJleHBvcnRzIiwidGltZSIsImlhdCIsInRpbWVzdGFtcCIsIk1hdGgiLCJmbG9vciIsIkRhdGUiLCJub3ciLCJtaWxsaXNlY29uZHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/timespan.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js":
/*!****************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js ***!
  \****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst ASYMMETRIC_KEY_DETAILS_SUPPORTED = __webpack_require__(/*! ./asymmetricKeyDetailsSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js\");\nconst RSA_PSS_KEY_DETAILS_SUPPORTED = __webpack_require__(/*! ./rsaPssKeyDetailsSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js\");\nconst allowedAlgorithmsForKeys = {\n    \"ec\": [\n        \"ES256\",\n        \"ES384\",\n        \"ES512\"\n    ],\n    \"rsa\": [\n        \"RS256\",\n        \"PS256\",\n        \"RS384\",\n        \"PS384\",\n        \"RS512\",\n        \"PS512\"\n    ],\n    \"rsa-pss\": [\n        \"PS256\",\n        \"PS384\",\n        \"PS512\"\n    ]\n};\nconst allowedCurves = {\n    ES256: \"prime256v1\",\n    ES384: \"secp384r1\",\n    ES512: \"secp521r1\"\n};\nmodule.exports = function(algorithm, key) {\n    if (!algorithm || !key) return;\n    const keyType = key.asymmetricKeyType;\n    if (!keyType) return;\n    const allowedAlgorithms = allowedAlgorithmsForKeys[keyType];\n    if (!allowedAlgorithms) {\n        throw new Error(`Unknown key type \"${keyType}\".`);\n    }\n    if (!allowedAlgorithms.includes(algorithm)) {\n        throw new Error(`\"alg\" parameter for \"${keyType}\" key type must be one of: ${allowedAlgorithms.join(\", \")}.`);\n    }\n    /*\n   * Ignore the next block from test coverage because it gets executed\n   * conditionally depending on the Node version. Not ignoring it would\n   * prevent us from reaching the target % of coverage for versions of\n   * Node under 15.7.0.\n   */ /* istanbul ignore next */ if (ASYMMETRIC_KEY_DETAILS_SUPPORTED) {\n        switch(keyType){\n            case \"ec\":\n                const keyCurve = key.asymmetricKeyDetails.namedCurve;\n                const allowedCurve = allowedCurves[algorithm];\n                if (keyCurve !== allowedCurve) {\n                    throw new Error(`\"alg\" parameter \"${algorithm}\" requires curve \"${allowedCurve}\".`);\n                }\n                break;\n            case \"rsa-pss\":\n                if (RSA_PSS_KEY_DETAILS_SUPPORTED) {\n                    const length = parseInt(algorithm.slice(-3), 10);\n                    const { hashAlgorithm, mgf1HashAlgorithm, saltLength } = key.asymmetricKeyDetails;\n                    if (hashAlgorithm !== `sha${length}` || mgf1HashAlgorithm !== hashAlgorithm) {\n                        throw new Error(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of \"alg\" ${algorithm}.`);\n                    }\n                    if (saltLength !== undefined && saltLength > length >> 3) {\n                        throw new Error(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of \"alg\" ${algorithm}.`);\n                    }\n                }\n                break;\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/sign.js":
/*!*******************************************!*\
  !*** ./node_modules/jsonwebtoken/sign.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst timespan = __webpack_require__(/*! ./lib/timespan */ \"(rsc)/./node_modules/jsonwebtoken/lib/timespan.js\");\nconst PS_SUPPORTED = __webpack_require__(/*! ./lib/psSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js\");\nconst validateAsymmetricKey = __webpack_require__(/*! ./lib/validateAsymmetricKey */ \"(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\");\nconst jws = __webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\");\nconst includes = __webpack_require__(/*! lodash.includes */ \"(rsc)/./node_modules/lodash.includes/index.js\");\nconst isBoolean = __webpack_require__(/*! lodash.isboolean */ \"(rsc)/./node_modules/lodash.isboolean/index.js\");\nconst isInteger = __webpack_require__(/*! lodash.isinteger */ \"(rsc)/./node_modules/lodash.isinteger/index.js\");\nconst isNumber = __webpack_require__(/*! lodash.isnumber */ \"(rsc)/./node_modules/lodash.isnumber/index.js\");\nconst isPlainObject = __webpack_require__(/*! lodash.isplainobject */ \"(rsc)/./node_modules/lodash.isplainobject/index.js\");\nconst isString = __webpack_require__(/*! lodash.isstring */ \"(rsc)/./node_modules/lodash.isstring/index.js\");\nconst once = __webpack_require__(/*! lodash.once */ \"(rsc)/./node_modules/lodash.once/index.js\");\nconst { KeyObject, createSecretKey, createPrivateKey } = __webpack_require__(/*! crypto */ \"crypto\");\nconst SUPPORTED_ALGS = [\n    \"RS256\",\n    \"RS384\",\n    \"RS512\",\n    \"ES256\",\n    \"ES384\",\n    \"ES512\",\n    \"HS256\",\n    \"HS384\",\n    \"HS512\",\n    \"none\"\n];\nif (PS_SUPPORTED) {\n    SUPPORTED_ALGS.splice(3, 0, \"PS256\", \"PS384\", \"PS512\");\n}\nconst sign_options_schema = {\n    expiresIn: {\n        isValid: function(value) {\n            return isInteger(value) || isString(value) && value;\n        },\n        message: '\"expiresIn\" should be a number of seconds or string representing a timespan'\n    },\n    notBefore: {\n        isValid: function(value) {\n            return isInteger(value) || isString(value) && value;\n        },\n        message: '\"notBefore\" should be a number of seconds or string representing a timespan'\n    },\n    audience: {\n        isValid: function(value) {\n            return isString(value) || Array.isArray(value);\n        },\n        message: '\"audience\" must be a string or array'\n    },\n    algorithm: {\n        isValid: includes.bind(null, SUPPORTED_ALGS),\n        message: '\"algorithm\" must be a valid string enum value'\n    },\n    header: {\n        isValid: isPlainObject,\n        message: '\"header\" must be an object'\n    },\n    encoding: {\n        isValid: isString,\n        message: '\"encoding\" must be a string'\n    },\n    issuer: {\n        isValid: isString,\n        message: '\"issuer\" must be a string'\n    },\n    subject: {\n        isValid: isString,\n        message: '\"subject\" must be a string'\n    },\n    jwtid: {\n        isValid: isString,\n        message: '\"jwtid\" must be a string'\n    },\n    noTimestamp: {\n        isValid: isBoolean,\n        message: '\"noTimestamp\" must be a boolean'\n    },\n    keyid: {\n        isValid: isString,\n        message: '\"keyid\" must be a string'\n    },\n    mutatePayload: {\n        isValid: isBoolean,\n        message: '\"mutatePayload\" must be a boolean'\n    },\n    allowInsecureKeySizes: {\n        isValid: isBoolean,\n        message: '\"allowInsecureKeySizes\" must be a boolean'\n    },\n    allowInvalidAsymmetricKeyTypes: {\n        isValid: isBoolean,\n        message: '\"allowInvalidAsymmetricKeyTypes\" must be a boolean'\n    }\n};\nconst registered_claims_schema = {\n    iat: {\n        isValid: isNumber,\n        message: '\"iat\" should be a number of seconds'\n    },\n    exp: {\n        isValid: isNumber,\n        message: '\"exp\" should be a number of seconds'\n    },\n    nbf: {\n        isValid: isNumber,\n        message: '\"nbf\" should be a number of seconds'\n    }\n};\nfunction validate(schema, allowUnknown, object, parameterName) {\n    if (!isPlainObject(object)) {\n        throw new Error('Expected \"' + parameterName + '\" to be a plain object.');\n    }\n    Object.keys(object).forEach(function(key) {\n        const validator = schema[key];\n        if (!validator) {\n            if (!allowUnknown) {\n                throw new Error('\"' + key + '\" is not allowed in \"' + parameterName + '\"');\n            }\n            return;\n        }\n        if (!validator.isValid(object[key])) {\n            throw new Error(validator.message);\n        }\n    });\n}\nfunction validateOptions(options) {\n    return validate(sign_options_schema, false, options, \"options\");\n}\nfunction validatePayload(payload) {\n    return validate(registered_claims_schema, true, payload, \"payload\");\n}\nconst options_to_payload = {\n    \"audience\": \"aud\",\n    \"issuer\": \"iss\",\n    \"subject\": \"sub\",\n    \"jwtid\": \"jti\"\n};\nconst options_for_objects = [\n    \"expiresIn\",\n    \"notBefore\",\n    \"noTimestamp\",\n    \"audience\",\n    \"issuer\",\n    \"subject\",\n    \"jwtid\"\n];\nmodule.exports = function(payload, secretOrPrivateKey, options, callback) {\n    if (typeof options === \"function\") {\n        callback = options;\n        options = {};\n    } else {\n        options = options || {};\n    }\n    const isObjectPayload = typeof payload === \"object\" && !Buffer.isBuffer(payload);\n    const header = Object.assign({\n        alg: options.algorithm || \"HS256\",\n        typ: isObjectPayload ? \"JWT\" : undefined,\n        kid: options.keyid\n    }, options.header);\n    function failure(err) {\n        if (callback) {\n            return callback(err);\n        }\n        throw err;\n    }\n    if (!secretOrPrivateKey && options.algorithm !== \"none\") {\n        return failure(new Error(\"secretOrPrivateKey must have a value\"));\n    }\n    if (secretOrPrivateKey != null && !(secretOrPrivateKey instanceof KeyObject)) {\n        try {\n            secretOrPrivateKey = createPrivateKey(secretOrPrivateKey);\n        } catch (_) {\n            try {\n                secretOrPrivateKey = createSecretKey(typeof secretOrPrivateKey === \"string\" ? Buffer.from(secretOrPrivateKey) : secretOrPrivateKey);\n            } catch (_) {\n                return failure(new Error(\"secretOrPrivateKey is not valid key material\"));\n            }\n        }\n    }\n    if (header.alg.startsWith(\"HS\") && secretOrPrivateKey.type !== \"secret\") {\n        return failure(new Error(`secretOrPrivateKey must be a symmetric key when using ${header.alg}`));\n    } else if (/^(?:RS|PS|ES)/.test(header.alg)) {\n        if (secretOrPrivateKey.type !== \"private\") {\n            return failure(new Error(`secretOrPrivateKey must be an asymmetric key when using ${header.alg}`));\n        }\n        if (!options.allowInsecureKeySizes && !header.alg.startsWith(\"ES\") && secretOrPrivateKey.asymmetricKeyDetails !== undefined && //KeyObject.asymmetricKeyDetails is supported in Node 15+\n        secretOrPrivateKey.asymmetricKeyDetails.modulusLength < 2048) {\n            return failure(new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`));\n        }\n    }\n    if (typeof payload === \"undefined\") {\n        return failure(new Error(\"payload is required\"));\n    } else if (isObjectPayload) {\n        try {\n            validatePayload(payload);\n        } catch (error) {\n            return failure(error);\n        }\n        if (!options.mutatePayload) {\n            payload = Object.assign({}, payload);\n        }\n    } else {\n        const invalid_options = options_for_objects.filter(function(opt) {\n            return typeof options[opt] !== \"undefined\";\n        });\n        if (invalid_options.length > 0) {\n            return failure(new Error(\"invalid \" + invalid_options.join(\",\") + \" option for \" + typeof payload + \" payload\"));\n        }\n    }\n    if (typeof payload.exp !== \"undefined\" && typeof options.expiresIn !== \"undefined\") {\n        return failure(new Error('Bad \"options.expiresIn\" option the payload already has an \"exp\" property.'));\n    }\n    if (typeof payload.nbf !== \"undefined\" && typeof options.notBefore !== \"undefined\") {\n        return failure(new Error('Bad \"options.notBefore\" option the payload already has an \"nbf\" property.'));\n    }\n    try {\n        validateOptions(options);\n    } catch (error) {\n        return failure(error);\n    }\n    if (!options.allowInvalidAsymmetricKeyTypes) {\n        try {\n            validateAsymmetricKey(header.alg, secretOrPrivateKey);\n        } catch (error) {\n            return failure(error);\n        }\n    }\n    const timestamp = payload.iat || Math.floor(Date.now() / 1000);\n    if (options.noTimestamp) {\n        delete payload.iat;\n    } else if (isObjectPayload) {\n        payload.iat = timestamp;\n    }\n    if (typeof options.notBefore !== \"undefined\") {\n        try {\n            payload.nbf = timespan(options.notBefore, timestamp);\n        } catch (err) {\n            return failure(err);\n        }\n        if (typeof payload.nbf === \"undefined\") {\n            return failure(new Error('\"notBefore\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n        }\n    }\n    if (typeof options.expiresIn !== \"undefined\" && typeof payload === \"object\") {\n        try {\n            payload.exp = timespan(options.expiresIn, timestamp);\n        } catch (err) {\n            return failure(err);\n        }\n        if (typeof payload.exp === \"undefined\") {\n            return failure(new Error('\"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n        }\n    }\n    Object.keys(options_to_payload).forEach(function(key) {\n        const claim = options_to_payload[key];\n        if (typeof options[key] !== \"undefined\") {\n            if (typeof payload[claim] !== \"undefined\") {\n                return failure(new Error('Bad \"options.' + key + '\" option. The payload already has an \"' + claim + '\" property.'));\n            }\n            payload[claim] = options[key];\n        }\n    });\n    const encoding = options.encoding || \"utf8\";\n    if (typeof callback === \"function\") {\n        callback = callback && once(callback);\n        jws.createSign({\n            header: header,\n            privateKey: secretOrPrivateKey,\n            payload: payload,\n            encoding: encoding\n        }).once(\"error\", callback).once(\"done\", function(signature) {\n            // TODO: Remove in favor of the modulus length check before signing once node 15+ is the minimum supported version\n            if (!options.allowInsecureKeySizes && /^(?:RS|PS)/.test(header.alg) && signature.length < 256) {\n                return callback(new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`));\n            }\n            callback(null, signature);\n        });\n    } else {\n        let signature = jws.sign({\n            header: header,\n            payload: payload,\n            secret: secretOrPrivateKey,\n            encoding: encoding\n        });\n        // TODO: Remove in favor of the modulus length check before signing once node 15+ is the minimum supported version\n        if (!options.allowInsecureKeySizes && /^(?:RS|PS)/.test(header.alg) && signature.length < 256) {\n            throw new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`);\n        }\n        return signature;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/sign.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/verify.js":
/*!*********************************************!*\
  !*** ./node_modules/jsonwebtoken/verify.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst JsonWebTokenError = __webpack_require__(/*! ./lib/JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\nconst NotBeforeError = __webpack_require__(/*! ./lib/NotBeforeError */ \"(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\");\nconst TokenExpiredError = __webpack_require__(/*! ./lib/TokenExpiredError */ \"(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\");\nconst decode = __webpack_require__(/*! ./decode */ \"(rsc)/./node_modules/jsonwebtoken/decode.js\");\nconst timespan = __webpack_require__(/*! ./lib/timespan */ \"(rsc)/./node_modules/jsonwebtoken/lib/timespan.js\");\nconst validateAsymmetricKey = __webpack_require__(/*! ./lib/validateAsymmetricKey */ \"(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\");\nconst PS_SUPPORTED = __webpack_require__(/*! ./lib/psSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js\");\nconst jws = __webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\");\nconst { KeyObject, createSecretKey, createPublicKey } = __webpack_require__(/*! crypto */ \"crypto\");\nconst PUB_KEY_ALGS = [\n    \"RS256\",\n    \"RS384\",\n    \"RS512\"\n];\nconst EC_KEY_ALGS = [\n    \"ES256\",\n    \"ES384\",\n    \"ES512\"\n];\nconst RSA_KEY_ALGS = [\n    \"RS256\",\n    \"RS384\",\n    \"RS512\"\n];\nconst HS_ALGS = [\n    \"HS256\",\n    \"HS384\",\n    \"HS512\"\n];\nif (PS_SUPPORTED) {\n    PUB_KEY_ALGS.splice(PUB_KEY_ALGS.length, 0, \"PS256\", \"PS384\", \"PS512\");\n    RSA_KEY_ALGS.splice(RSA_KEY_ALGS.length, 0, \"PS256\", \"PS384\", \"PS512\");\n}\nmodule.exports = function(jwtString, secretOrPublicKey, options, callback) {\n    if (typeof options === \"function\" && !callback) {\n        callback = options;\n        options = {};\n    }\n    if (!options) {\n        options = {};\n    }\n    //clone this object since we are going to mutate it.\n    options = Object.assign({}, options);\n    let done;\n    if (callback) {\n        done = callback;\n    } else {\n        done = function(err, data) {\n            if (err) throw err;\n            return data;\n        };\n    }\n    if (options.clockTimestamp && typeof options.clockTimestamp !== \"number\") {\n        return done(new JsonWebTokenError(\"clockTimestamp must be a number\"));\n    }\n    if (options.nonce !== undefined && (typeof options.nonce !== \"string\" || options.nonce.trim() === \"\")) {\n        return done(new JsonWebTokenError(\"nonce must be a non-empty string\"));\n    }\n    if (options.allowInvalidAsymmetricKeyTypes !== undefined && typeof options.allowInvalidAsymmetricKeyTypes !== \"boolean\") {\n        return done(new JsonWebTokenError(\"allowInvalidAsymmetricKeyTypes must be a boolean\"));\n    }\n    const clockTimestamp = options.clockTimestamp || Math.floor(Date.now() / 1000);\n    if (!jwtString) {\n        return done(new JsonWebTokenError(\"jwt must be provided\"));\n    }\n    if (typeof jwtString !== \"string\") {\n        return done(new JsonWebTokenError(\"jwt must be a string\"));\n    }\n    const parts = jwtString.split(\".\");\n    if (parts.length !== 3) {\n        return done(new JsonWebTokenError(\"jwt malformed\"));\n    }\n    let decodedToken;\n    try {\n        decodedToken = decode(jwtString, {\n            complete: true\n        });\n    } catch (err) {\n        return done(err);\n    }\n    if (!decodedToken) {\n        return done(new JsonWebTokenError(\"invalid token\"));\n    }\n    const header = decodedToken.header;\n    let getSecret;\n    if (typeof secretOrPublicKey === \"function\") {\n        if (!callback) {\n            return done(new JsonWebTokenError(\"verify must be called asynchronous if secret or public key is provided as a callback\"));\n        }\n        getSecret = secretOrPublicKey;\n    } else {\n        getSecret = function(header, secretCallback) {\n            return secretCallback(null, secretOrPublicKey);\n        };\n    }\n    return getSecret(header, function(err, secretOrPublicKey) {\n        if (err) {\n            return done(new JsonWebTokenError(\"error in secret or public key callback: \" + err.message));\n        }\n        const hasSignature = parts[2].trim() !== \"\";\n        if (!hasSignature && secretOrPublicKey) {\n            return done(new JsonWebTokenError(\"jwt signature is required\"));\n        }\n        if (hasSignature && !secretOrPublicKey) {\n            return done(new JsonWebTokenError(\"secret or public key must be provided\"));\n        }\n        if (!hasSignature && !options.algorithms) {\n            return done(new JsonWebTokenError('please specify \"none\" in \"algorithms\" to verify unsigned tokens'));\n        }\n        if (secretOrPublicKey != null && !(secretOrPublicKey instanceof KeyObject)) {\n            try {\n                secretOrPublicKey = createPublicKey(secretOrPublicKey);\n            } catch (_) {\n                try {\n                    secretOrPublicKey = createSecretKey(typeof secretOrPublicKey === \"string\" ? Buffer.from(secretOrPublicKey) : secretOrPublicKey);\n                } catch (_) {\n                    return done(new JsonWebTokenError(\"secretOrPublicKey is not valid key material\"));\n                }\n            }\n        }\n        if (!options.algorithms) {\n            if (secretOrPublicKey.type === \"secret\") {\n                options.algorithms = HS_ALGS;\n            } else if ([\n                \"rsa\",\n                \"rsa-pss\"\n            ].includes(secretOrPublicKey.asymmetricKeyType)) {\n                options.algorithms = RSA_KEY_ALGS;\n            } else if (secretOrPublicKey.asymmetricKeyType === \"ec\") {\n                options.algorithms = EC_KEY_ALGS;\n            } else {\n                options.algorithms = PUB_KEY_ALGS;\n            }\n        }\n        if (options.algorithms.indexOf(decodedToken.header.alg) === -1) {\n            return done(new JsonWebTokenError(\"invalid algorithm\"));\n        }\n        if (header.alg.startsWith(\"HS\") && secretOrPublicKey.type !== \"secret\") {\n            return done(new JsonWebTokenError(`secretOrPublicKey must be a symmetric key when using ${header.alg}`));\n        } else if (/^(?:RS|PS|ES)/.test(header.alg) && secretOrPublicKey.type !== \"public\") {\n            return done(new JsonWebTokenError(`secretOrPublicKey must be an asymmetric key when using ${header.alg}`));\n        }\n        if (!options.allowInvalidAsymmetricKeyTypes) {\n            try {\n                validateAsymmetricKey(header.alg, secretOrPublicKey);\n            } catch (e) {\n                return done(e);\n            }\n        }\n        let valid;\n        try {\n            valid = jws.verify(jwtString, decodedToken.header.alg, secretOrPublicKey);\n        } catch (e) {\n            return done(e);\n        }\n        if (!valid) {\n            return done(new JsonWebTokenError(\"invalid signature\"));\n        }\n        const payload = decodedToken.payload;\n        if (typeof payload.nbf !== \"undefined\" && !options.ignoreNotBefore) {\n            if (typeof payload.nbf !== \"number\") {\n                return done(new JsonWebTokenError(\"invalid nbf value\"));\n            }\n            if (payload.nbf > clockTimestamp + (options.clockTolerance || 0)) {\n                return done(new NotBeforeError(\"jwt not active\", new Date(payload.nbf * 1000)));\n            }\n        }\n        if (typeof payload.exp !== \"undefined\" && !options.ignoreExpiration) {\n            if (typeof payload.exp !== \"number\") {\n                return done(new JsonWebTokenError(\"invalid exp value\"));\n            }\n            if (clockTimestamp >= payload.exp + (options.clockTolerance || 0)) {\n                return done(new TokenExpiredError(\"jwt expired\", new Date(payload.exp * 1000)));\n            }\n        }\n        if (options.audience) {\n            const audiences = Array.isArray(options.audience) ? options.audience : [\n                options.audience\n            ];\n            const target = Array.isArray(payload.aud) ? payload.aud : [\n                payload.aud\n            ];\n            const match = target.some(function(targetAudience) {\n                return audiences.some(function(audience) {\n                    return audience instanceof RegExp ? audience.test(targetAudience) : audience === targetAudience;\n                });\n            });\n            if (!match) {\n                return done(new JsonWebTokenError(\"jwt audience invalid. expected: \" + audiences.join(\" or \")));\n            }\n        }\n        if (options.issuer) {\n            const invalid_issuer = typeof options.issuer === \"string\" && payload.iss !== options.issuer || Array.isArray(options.issuer) && options.issuer.indexOf(payload.iss) === -1;\n            if (invalid_issuer) {\n                return done(new JsonWebTokenError(\"jwt issuer invalid. expected: \" + options.issuer));\n            }\n        }\n        if (options.subject) {\n            if (payload.sub !== options.subject) {\n                return done(new JsonWebTokenError(\"jwt subject invalid. expected: \" + options.subject));\n            }\n        }\n        if (options.jwtid) {\n            if (payload.jti !== options.jwtid) {\n                return done(new JsonWebTokenError(\"jwt jwtid invalid. expected: \" + options.jwtid));\n            }\n        }\n        if (options.nonce) {\n            if (payload.nonce !== options.nonce) {\n                return done(new JsonWebTokenError(\"jwt nonce invalid. expected: \" + options.nonce));\n            }\n        }\n        if (options.maxAge) {\n            if (typeof payload.iat !== \"number\") {\n                return done(new JsonWebTokenError(\"iat required when maxAge is specified\"));\n            }\n            const maxAgeTimestamp = timespan(options.maxAge, payload.iat);\n            if (typeof maxAgeTimestamp === \"undefined\") {\n                return done(new JsonWebTokenError('\"maxAge\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n            }\n            if (clockTimestamp >= maxAgeTimestamp + (options.clockTolerance || 0)) {\n                return done(new TokenExpiredError(\"maxAge exceeded\", new Date(maxAgeTimestamp * 1000)));\n            }\n        }\n        if (options.complete === true) {\n            const signature = decodedToken.signature;\n            return done(null, {\n                header: header,\n                payload: payload,\n                signature: signature\n            });\n        }\n        return done(null, payload);\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/verify.js\n");

/***/ })

};
;