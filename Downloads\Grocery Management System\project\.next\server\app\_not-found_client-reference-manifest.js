globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/_not-found"]={"ssrModuleMapping":{"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(ssr)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/auth/auth-provider.tsx":{"*":{"id":"(ssr)/./components/auth/auth-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/sonner.tsx":{"*":{"id":"(ssr)/./components/ui/sonner.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(dashboard)/layout.tsx":{"*":{"id":"(ssr)/./app/(dashboard)/layout.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\app\\page.tsx":{"id":"(app-pages-browser)/./app/page.tsx","name":"*","chunks":["app/page:static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout:static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\components\\auth\\auth-provider.tsx":{"id":"(app-pages-browser)/./components/auth/auth-provider.tsx","name":"*","chunks":["app/layout:static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\components\\ui\\sonner.tsx":{"id":"(app-pages-browser)/./components/ui/sonner.tsx","name":"*","chunks":["app/layout:static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout:static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\app\\(dashboard)\\layout.tsx":{"id":"(app-pages-browser)/./app/(dashboard)/layout.tsx","name":"*","chunks":["app/(dashboard)/layout:static/chunks/app/(dashboard)/layout.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\app\\page":[],"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\app\\(dashboard)\\layout":[],"C:\\Users\\<USER>\\Downloads\\Grocery Management System\\project\\app\\not-found":[]}}