import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  getPaginationParams,
  buildSearchFilter,
  createPaginatedResponse,
  createAuditLog,
  validateStoreAccess,
  buildDateRangeFilter
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const documentSchema = z.object({
  name: z.string().min(1, 'Document name is required'),
  description: z.string().optional(),
  category: z.enum(['INVOICE', 'RECEIPT', 'CONTRACT', 'COMPLIANCE', 'PRODUCT', 'CUSTOMER', 'SUPPLIER', 'FINANCIAL', 'LEGAL', 'OTHER']),
  subcategory: z.string().optional(),
  fileUrl: z.string().min(1, 'File URL is required'),
  fileName: z.string().min(1, 'File name is required'),
  fileSize: z.number().min(0, 'File size must be positive'),
  mimeType: z.string().min(1, 'MIME type is required'),
  entityType: z.enum(['SALE', 'PURCHASE', 'CUSTOMER', 'SUPPLIER', 'PRODUCT', 'EXPENSE', 'GENERAL']).optional(),
  entityId: z.string().optional(),
  tags: z.array(z.string()).optional(),
  isPublic: z.boolean().default(false),
  expiryDate: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  accessLevel: z.enum(['PUBLIC', 'INTERNAL', 'RESTRICTED', 'CONFIDENTIAL']).default('INTERNAL')
})

// GET /api/documents - Get all documents with advanced filtering
export const GET = withPermission('DOCUMENT', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { page, limit, skip, search, sortBy, sortOrder } = getPaginationParams(request)
  const { searchParams } = new URL(request.url)
  
  const category = searchParams.get('category')
  const subcategory = searchParams.get('subcategory')
  const entityType = searchParams.get('entityType')
  const entityId = searchParams.get('entityId')
  const accessLevel = searchParams.get('accessLevel')
  const tags = searchParams.get('tags')?.split(',')
  const startDate = searchParams.get('startDate')
  const endDate = searchParams.get('endDate')
  const expired = searchParams.get('expired') === 'true'

  // Build filters
  const where: any = {
    storeId,
    ...buildSearchFilter(search, ['name', 'description', 'fileName']),
    ...buildDateRangeFilter(startDate || undefined, endDate || undefined, 'createdAt')
  }

  if (category) {
    where.category = category
  }

  if (subcategory) {
    where.subcategory = subcategory
  }

  if (entityType) {
    where.entityType = entityType
  }

  if (entityId) {
    where.entityId = entityId
  }

  if (accessLevel) {
    where.accessLevel = accessLevel
  }

  if (tags && tags.length > 0) {
    where.tags = { hasSome: tags }
  }

  if (expired) {
    where.expiryDate = { lt: new Date() }
  }

  // Check user access permissions
  if (!['FOUNDER', 'SUPER_ADMIN'].includes(user.role)) {
    where.OR = [
      { uploadedById: user.id },
      { accessLevel: { in: ['PUBLIC', 'INTERNAL'] } },
      { 
        AND: [
          { accessLevel: 'RESTRICTED' },
          { 
            documentAccess: {
              some: {
                userId: user.id,
                accessType: { in: ['READ', 'WRITE', 'ADMIN'] }
              }
            }
          }
        ]
      }
    ]
  }

  // Get total count
  const total = await prisma.document.count({ where })

  // Get documents with pagination
  const documents = await prisma.document.findMany({
    where,
    include: {
      uploadedBy: {
        select: {
          id: true,
          name: true
        }
      },
      documentAccess: {
        include: {
          user: {
            select: {
              id: true,
              name: true
            }
          }
        }
      },
      _count: {
        select: {
          documentAccess: true,
          documentVersions: true
        }
      }
    },
    orderBy: { [sortBy]: sortOrder },
    skip,
    take: limit
  })

  // Add calculated fields
  const documentsWithCalculations = documents.map(document => ({
    ...document,
    isExpired: document.expiryDate && new Date(document.expiryDate) < new Date(),
    daysToExpiry: document.expiryDate ? 
                  Math.ceil((new Date(document.expiryDate).getTime() - Date.now()) / (1000 * 60 * 60 * 24)) : null,
    accessCount: document._count.documentAccess,
    versionCount: document._count.documentVersions,
    canEdit: canUserEditDocument(document, user),
    canDelete: canUserDeleteDocument(document, user)
  }))

  return createPaginatedResponse(documentsWithCalculations, page, limit, total)
})

// POST /api/documents - Upload new document
export const POST = withPermission('DOCUMENT', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const data = documentSchema.parse(body)

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Validate entity exists if entityId provided
      if (data.entityId && data.entityType) {
        await validateEntityExists(data.entityType, data.entityId, storeId, tx)
      }

      // Check file size limits (100MB default)
      const maxFileSize = 100 * 1024 * 1024 // 100MB
      if (data.fileSize > maxFileSize) {
        throw new Error('File size exceeds maximum limit of 100MB')
      }

      // Check allowed file types
      const allowedMimeTypes = [
        'application/pdf',
        'image/jpeg',
        'image/png',
        'image/gif',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/plain',
        'text/csv'
      ]

      if (!allowedMimeTypes.includes(data.mimeType)) {
        throw new Error('File type not allowed')
      }

      // Generate document number
      const documentCount = await tx.document.count({ where: { storeId } })
      const documentNo = `DOC-${String(documentCount + 1).padStart(6, '0')}`

      // Create document
      const document = await tx.document.create({
        data: {
          documentNo,
          name: data.name,
          description: data.description,
          category: data.category,
          subcategory: data.subcategory,
          fileUrl: data.fileUrl,
          fileName: data.fileName,
          fileSize: data.fileSize,
          mimeType: data.mimeType,
          entityType: data.entityType,
          entityId: data.entityId,
          tags: data.tags,
          isPublic: data.isPublic,
          expiryDate: data.expiryDate ? new Date(data.expiryDate) : undefined,
          metadata: data.metadata,
          accessLevel: data.accessLevel,
          uploadedById: user.id,
          storeId
        },
        include: {
          uploadedBy: {
            select: {
              id: true,
              name: true
            }
          }
        }
      })

      // Create initial version
      await tx.documentVersion.create({
        data: {
          documentId: document.id,
          version: '1.0',
          fileUrl: data.fileUrl,
          fileName: data.fileName,
          fileSize: data.fileSize,
          changeLog: 'Initial upload',
          uploadedById: user.id,
          storeId
        }
      })

      // Extract and index document content for search (if text-based)
      if (data.mimeType.startsWith('text/') || data.mimeType === 'application/pdf') {
        await extractAndIndexContent(document.id, data.fileUrl, data.mimeType, storeId)
      }

      return document
    })

    // Create audit log
    await createAuditLog(
      'CREATE',
      'DOCUMENT',
      `Uploaded document: ${result.name} (${result.fileName}) - Category: ${result.category}`,
      user.id,
      storeId
    )

    return createSuccessResponse(result, 'Document uploaded successfully')

  } catch (error) {
    console.error('Error uploading document:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to upload document',
      400
    )
  }
})

// Helper functions

function canUserEditDocument(document: any, user: any): boolean {
  // Founders and Super Admins can edit all documents
  if (['FOUNDER', 'SUPER_ADMIN'].includes(user.role)) {
    return true
  }

  // Document owner can edit
  if (document.uploadedById === user.id) {
    return true
  }

  // Check specific access permissions
  const userAccess = document.documentAccess.find((access: any) => access.userId === user.id)
  return userAccess && ['WRITE', 'ADMIN'].includes(userAccess.accessType)
}

function canUserDeleteDocument(document: any, user: any): boolean {
  // Only Founders, Super Admins, and document owners can delete
  if (['FOUNDER', 'SUPER_ADMIN'].includes(user.role)) {
    return true
  }

  if (document.uploadedById === user.id) {
    return true
  }

  // Check admin access
  const userAccess = document.documentAccess.find((access: any) => access.userId === user.id)
  return userAccess && userAccess.accessType === 'ADMIN'
}

async function validateEntityExists(entityType: string, entityId: string, storeId: string, tx: any) {
  let entity = null

  switch (entityType) {
    case 'SALE':
      entity = await tx.sale.findFirst({
        where: { id: entityId, storeId }
      })
      break
    case 'PURCHASE':
      entity = await tx.purchase.findFirst({
        where: { id: entityId, storeId }
      })
      break
    case 'CUSTOMER':
      entity = await tx.customer.findFirst({
        where: { id: entityId, storeId }
      })
      break
    case 'SUPPLIER':
      entity = await tx.supplier.findFirst({
        where: { id: entityId, storeId }
      })
      break
    case 'PRODUCT':
      entity = await tx.product.findFirst({
        where: { id: entityId, storeId }
      })
      break
    case 'EXPENSE':
      entity = await tx.expense.findFirst({
        where: { id: entityId, storeId }
      })
      break
  }

  if (!entity) {
    throw new Error(`${entityType} not found`)
  }
}

async function extractAndIndexContent(documentId: string, fileUrl: string, mimeType: string, storeId: string) {
  try {
    // This would integrate with a text extraction service
    // For now, we'll create a placeholder
    let extractedText = ''

    // Extract text based on file type
    switch (mimeType) {
      case 'text/plain':
      case 'text/csv':
        // For text files, fetch and extract content
        extractedText = await extractTextFromUrl(fileUrl)
        break
      case 'application/pdf':
        // For PDFs, use a PDF text extraction library
        extractedText = await extractTextFromPDF(fileUrl)
        break
      default:
        // For other types, skip text extraction
        return
    }

    // Store extracted content for search indexing
    if (extractedText) {
      await prisma.documentContent.create({
        data: {
          documentId,
          content: extractedText,
          contentType: 'TEXT',
          storeId
        }
      })
    }

  } catch (error) {
    console.error('Error extracting document content:', error)
    // Don't fail the upload if content extraction fails
  }
}

async function extractTextFromUrl(url: string): Promise<string> {
  // Placeholder for text extraction from URL
  // In a real implementation, you would fetch the file and extract text
  return ''
}

async function extractTextFromPDF(url: string): Promise<string> {
  // Placeholder for PDF text extraction
  // In a real implementation, you would use a library like pdf-parse
  return ''
}
