import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// POST /api/products/bulk - Bulk import products
export const POST = withPermission('PRODUCT', 'IMPORT', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()

  const bulkProductSchema = z.object({
    products: z.array(z.object({
      name: z.string().min(1, 'Product name is required'),
      sku: z.string().min(1, 'SKU is required'),
      description: z.string().optional(),
      barcode: z.string().optional(),
      unit: z.string().default('pcs'),
      costPrice: z.number().min(0, 'Cost price must be positive'),
      sellingPrice: z.number().min(0, 'Selling price must be positive'),
      mrp: z.number().optional(),
      taxRate: z.number().min(0).max(100).default(0),
      minStock: z.number().min(0).default(0),
      categoryName: z.string().min(1, 'Category name is required'),
      initialStock: z.number().min(0).default(0)
    }))
  })

  const { products } = bulkProductSchema.parse(body)

  const results = {
    success: 0,
    failed: 0,
    errors: [] as string[]
  }

  // Process products in batches
  for (const productData of products) {
    try {
      // Find or create category
      let category = await prisma.category.findFirst({
        where: {
          name: productData.categoryName,
          storeId,
          isActive: true
        }
      })

      if (!category) {
        category = await prisma.category.create({
          data: {
            name: productData.categoryName,
            storeId,
            isActive: true
          }
        })
      }

      // Check if SKU already exists
      const existingProduct = await prisma.product.findFirst({
        where: {
          sku: productData.sku,
          storeId,
          isActive: true
        }
      })

      if (existingProduct) {
        results.failed++
        results.errors.push(`SKU ${productData.sku} already exists`)
        continue
      }

      // Validate selling price vs cost price
      if (productData.sellingPrice < productData.costPrice) {
        results.failed++
        results.errors.push(`Product ${productData.name}: Selling price cannot be less than cost price`)
        continue
      }

      // Create product
      const product = await prisma.product.create({
        data: {
          name: productData.name,
          sku: productData.sku,
          description: productData.description,
          barcode: productData.barcode,
          unit: productData.unit,
          costPrice: productData.costPrice,
          sellingPrice: productData.sellingPrice,
          mrp: productData.mrp,
          taxRate: productData.taxRate,
          minStock: productData.minStock,
          categoryId: category.id,
          storeId
        }
      })

      // Create initial inventory record
      await prisma.inventory.create({
        data: {
          productId: product.id,
          storeId,
          quantity: productData.initialStock,
          reorderLevel: productData.minStock
        }
      })

      results.success++

    } catch (error) {
      results.failed++
      results.errors.push(`Product ${productData.name}: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  // Create audit log
  await createAuditLog(
    'IMPORT',
    'PRODUCT',
    `Bulk imported products: ${results.success} successful, ${results.failed} failed`,
    user.id,
    storeId
  )

  return createSuccessResponse(results, `Bulk import completed: ${results.success} successful, ${results.failed} failed`)
})

// GET /api/products/bulk - Export products
export const GET = withPermission('PRODUCT', 'EXPORT', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { searchParams } = new URL(request.url)
  const format = searchParams.get('format') || 'json'
  const categoryId = searchParams.get('categoryId')

  const where: any = {
    storeId,
    isActive: true
  }

  if (categoryId) {
    where.categoryId = categoryId
  }

  const products = await prisma.product.findMany({
    where,
    include: {
      category: {
        select: {
          name: true
        }
      },
      inventory: {
        select: {
          quantity: true,
          reorderLevel: true
        }
      }
    },
    orderBy: { name: 'asc' }
  })

  const exportData = products.map(product => ({
    name: product.name,
    sku: product.sku,
    description: product.description || '',
    barcode: product.barcode || '',
    unit: product.unit,
    costPrice: product.costPrice,
    sellingPrice: product.sellingPrice,
    mrp: product.mrp || '',
    taxRate: product.taxRate,
    minStock: product.minStock,
    categoryName: product.category.name,
    currentStock: product.inventory?.quantity || 0,
    reorderLevel: product.inventory?.reorderLevel || 0,
    isActive: product.isActive
  }))

  if (format === 'csv') {
    // Convert to CSV format
    const headers = [
      'Name', 'SKU', 'Description', 'Barcode', 'Unit', 'Cost Price', 
      'Selling Price', 'MRP', 'Tax Rate', 'Min Stock', 'Category', 
      'Current Stock', 'Reorder Level', 'Active'
    ]
    
    const csvRows = [
      headers.join(','),
      ...exportData.map(row => [
        `"${row.name}"`,
        `"${row.sku}"`,
        `"${row.description}"`,
        `"${row.barcode}"`,
        `"${row.unit}"`,
        row.costPrice,
        row.sellingPrice,
        row.mrp,
        row.taxRate,
        row.minStock,
        `"${row.categoryName}"`,
        row.currentStock,
        row.reorderLevel,
        row.isActive
      ].join(','))
    ]

    const csvContent = csvRows.join('\n')
    
    return new Response(csvContent, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="products-export-${new Date().toISOString().split('T')[0]}.csv"`
      }
    })
  }

  // Create audit log
  await createAuditLog(
    'EXPORT',
    'PRODUCT',
    `Exported ${products.length} products`,
    user.id,
    storeId
  )

  return createSuccessResponse({
    products: exportData,
    count: products.length,
    exportedAt: new Date().toISOString()
  }, `Exported ${products.length} products successfully`)
})
