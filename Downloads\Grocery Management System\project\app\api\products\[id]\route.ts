import { NextRequest, NextResponse } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const productUpdateSchema = z.object({
  name: z.string().min(1, 'Product name is required').optional(),
  sku: z.string().min(1, 'SKU is required').optional(),
  description: z.string().optional(),
  barcode: z.string().optional(),
  unit: z.string().optional(),
  costPrice: z.number().min(0, 'Cost price must be positive').optional(),
  sellingPrice: z.number().min(0, 'Selling price must be positive').optional(),
  mrp: z.number().optional(),
  taxRate: z.number().min(0).max(100).optional(),
  minStock: z.number().min(0).optional(),
  categoryId: z.string().optional(),
  isActive: z.boolean().optional()
})

// GET /api/products/[id] - Get single product
export const GET = withPermission('PRODUCT', 'READ', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const id = pathSegments[pathSegments.length - 1]

  const product = await prisma.product.findFirst({
    where: {
      id,
      storeId
    },
    include: {
      category: {
        select: {
          id: true,
          name: true
        }
      },
      inventory: {
        select: {
          quantity: true,
          reorderLevel: true,
          lastUpdated: true
        }
      },
      purchaseItems: {
        include: {
          purchase: {
            select: {
              id: true,
              purchaseDate: true,
              status: true,
              supplier: {
                select: {
                  name: true
                }
              }
            }
          }
        },
        orderBy: {
          purchase: {
            purchaseDate: 'desc'
          }
        },
        take: 5
      },
      saleItems: {
        include: {
          sale: {
            select: {
              id: true,
              saleDate: true,
              status: true
            }
          }
        },
        orderBy: {
          sale: {
            saleDate: 'desc'
          }
        },
        take: 5
      }
    }
  })

  if (!product) {
    return createErrorResponse('Product not found', 404)
  }

  return createSuccessResponse(product)
})

// PUT /api/products/[id] - Update product
export const PUT = withPermission('PRODUCT', 'UPDATE', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const id = pathSegments[pathSegments.length - 1]
  const body = await request.json()
  const data = productUpdateSchema.parse(body)

  // Check if product exists
  const existingProduct = await prisma.product.findFirst({
    where: {
      id,
      storeId
    }
  })

  if (!existingProduct) {
    return createErrorResponse('Product not found', 404)
  }

  // Check if SKU already exists (if SKU is being updated)
  if (data.sku && data.sku !== existingProduct.sku) {
    const skuExists = await prisma.product.findFirst({
      where: {
        sku: data.sku,
        storeId,
        isActive: true,
        id: { not: id }
      }
    })

    if (skuExists) {
      return createErrorResponse('SKU already exists', 400)
    }
  }

  // Validate category exists (if category is being updated)
  if (data.categoryId) {
    const category = await prisma.category.findFirst({
      where: {
        id: data.categoryId,
        storeId,
        isActive: true
      }
    })

    if (!category) {
      return createErrorResponse('Invalid category selected', 400)
    }
  }

  // Validate selling price vs cost price
  const newCostPrice = data.costPrice ?? existingProduct.costPrice
  const newSellingPrice = data.sellingPrice ?? existingProduct.sellingPrice

  if (newSellingPrice < newCostPrice) {
    return createErrorResponse('Selling price cannot be less than cost price', 400)
  }

  const updatedProduct = await prisma.product.update({
    where: { id },
    data,
    include: {
      category: {
        select: {
          id: true,
          name: true
        }
      },
      inventory: {
        select: {
          quantity: true,
          reorderLevel: true
        }
      }
    }
  })

  // Update inventory reorder level if minStock changed
  if (data.minStock !== undefined) {
    await prisma.inventory.updateMany({
      where: {
        productId: id,
        storeId
      },
      data: {
        reorderLevel: data.minStock
      }
    })
  }

  // Create audit log
  await createAuditLog(
    'UPDATE',
    'PRODUCT',
    `Updated product: ${updatedProduct.name} (SKU: ${updatedProduct.sku})`,
    user.id,
    storeId
  )

  return createSuccessResponse(updatedProduct, 'Product updated successfully')
})

// DELETE /api/products/[id] - Delete product (soft delete)
export const DELETE = withPermission('PRODUCT', 'DELETE', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const id = pathSegments[pathSegments.length - 1]

  // Check if product exists
  const existingProduct = await prisma.product.findFirst({
    where: {
      id,
      storeId
    },
    include: {
      inventory: {
        select: {
          quantity: true
        }
      },
      _count: {
        select: {
          saleItems: true,
          purchaseItems: true
        }
      }
    }
  })

  if (!existingProduct) {
    return createErrorResponse('Product not found', 404)
  }

  // Check if product has inventory
  const hasInventory = existingProduct.inventory.some(inv => inv.quantity > 0)
  if (hasInventory) {
    return createErrorResponse('Cannot delete product with existing inventory', 400)
  }

  // Check if product has sales/purchase history
  if (existingProduct._count.saleItems > 0 || existingProduct._count.purchaseItems > 0) {
    return createErrorResponse('Cannot delete product with transaction history. Consider deactivating instead.', 400)
  }

  // Soft delete
  await prisma.product.update({
    where: { id },
    data: { isActive: false }
  })

  // Create audit log
  await createAuditLog(
    'DELETE',
    'PRODUCT',
    `Deleted product: ${existingProduct.name} (SKU: ${existingProduct.sku})`,
    user.id,
    storeId
  )

  return createSuccessResponse(null, 'Product deleted successfully')
})
