import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  getPaginationParams,
  buildSearchFilter,
  createPaginatedResponse,
  createAuditLog
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const planSchema = z.object({
  name: z.string().min(1, 'Plan name is required'),
  description: z.string().optional(),
  category: z.enum(['BASIC', 'PROFESSIONAL', 'ENTERPRISE', 'CUSTOM']),
  pricing: z.object({
    monthly: z.number().min(0),
    quarterly: z.number().min(0).optional(),
    yearly: z.number().min(0).optional(),
    currency: z.string().default('INR'),
    setupFee: z.number().min(0).default(0)
  }),
  features: z.array(z.object({
    name: z.string(),
    description: z.string().optional(),
    included: z.boolean(),
    limit: z.number().optional(),
    unit: z.string().optional(),
    category: z.string().optional()
  })),
  limits: z.object({
    users: z.number().min(1),
    stores: z.number().min(1),
    products: z.number().min(1),
    transactions: z.number().min(1),
    storage: z.number().min(1), // in GB
    apiCalls: z.number().min(1),
    customFields: z.number().min(0).default(0),
    integrations: z.number().min(0).default(0)
  }),
  isActive: z.boolean().default(true),
  isPublic: z.boolean().default(true),
  trialDays: z.number().min(0).max(365).default(14),
  sortOrder: z.number().default(0),
  metadata: z.record(z.any()).optional()
})

// GET /api/subscriptions/plans - Get all subscription plans
export const GET = async (request: NextRequest) => {
  const { page, limit, skip, search, sortBy, sortOrder } = getPaginationParams(request)
  const { searchParams } = new URL(request.url)
  
  const category = searchParams.get('category')
  const isPublic = searchParams.get('public') === 'true'
  const isActive = searchParams.get('active') !== 'false' // Default to true
  const includeFeatures = searchParams.get('features') !== 'false' // Default to true

  try {
    // Build filters
    const where: any = {
      ...buildSearchFilter(search, ['name', 'description'])
    }

    if (category) {
      where.category = category
    }

    if (isPublic) {
      where.isPublic = true
    }

    if (isActive) {
      where.isActive = true
    }

    // Get total count
    const total = await prisma.subscriptionPlan.count({ where })

    // Get plans with pagination
    const plans = await prisma.subscriptionPlan.findMany({
      where,
      include: {
        features: includeFeatures,
        _count: {
          select: {
            subscriptions: true
          }
        }
      },
      orderBy: [
        { sortOrder: 'asc' },
        { [sortBy]: sortOrder }
      ],
      skip,
      take: limit
    })

    // Add calculated fields
    const plansWithCalculations = plans.map(plan => ({
      ...plan,
      subscriberCount: plan._count.subscriptions,
      monthlyPrice: plan.pricing.monthly,
      yearlyPrice: plan.pricing.yearly,
      yearlyDiscount: plan.pricing.yearly ? 
        Math.round((1 - (plan.pricing.yearly / (plan.pricing.monthly * 12))) * 100) : 0,
      featuresCount: plan.features?.length || 0,
      isPopular: plan.category === 'PROFESSIONAL', // Mark professional as popular
      recommendedFor: getRecommendedFor(plan)
    }))

    return createPaginatedResponse(plansWithCalculations, page, limit, total)

  } catch (error) {
    console.error('Error fetching subscription plans:', error)
    return createErrorResponse('Failed to fetch subscription plans', 500)
  }
}

// POST /api/subscriptions/plans - Create new subscription plan
export const POST = withPermission('SUBSCRIPTION', 'CREATE', async (request: NextRequest, user: any) => {
  const body = await request.json()
  const data = planSchema.parse(body)

  // Only system admins can create plans
  if (!['FOUNDER', 'SUPER_ADMIN'].includes(user.role)) {
    return createErrorResponse('Insufficient permissions to create subscription plans', 403)
  }

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Validate pricing consistency
      if (data.pricing.quarterly && data.pricing.quarterly >= data.pricing.monthly * 3) {
        throw new Error('Quarterly pricing should be less than 3x monthly pricing')
      }

      if (data.pricing.yearly && data.pricing.yearly >= data.pricing.monthly * 12) {
        throw new Error('Yearly pricing should be less than 12x monthly pricing')
      }

      // Create plan
      const plan = await tx.subscriptionPlan.create({
        data: {
          name: data.name,
          description: data.description,
          category: data.category,
          pricing: data.pricing,
          limits: data.limits,
          isActive: data.isActive,
          isPublic: data.isPublic,
          trialDays: data.trialDays,
          sortOrder: data.sortOrder,
          metadata: data.metadata,
          createdById: user.id
        }
      })

      // Create features
      if (data.features && data.features.length > 0) {
        await tx.subscriptionPlanFeature.createMany({
          data: data.features.map(feature => ({
            planId: plan.id,
            name: feature.name,
            description: feature.description,
            included: feature.included,
            limit: feature.limit,
            unit: feature.unit,
            category: feature.category
          }))
        })
      }

      return plan
    })

    // Create audit log
    await createAuditLog(
      'CREATE',
      'SUBSCRIPTION_PLAN',
      `Created subscription plan: ${result.name} (${result.category})`,
      user.id,
      'system'
    )

    return createSuccessResponse(result, 'Subscription plan created successfully')

  } catch (error) {
    console.error('Error creating subscription plan:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to create subscription plan',
      400
    )
  }
})

// PUT /api/subscriptions/plans/[id] - Update subscription plan
export const PUT = withPermission('SUBSCRIPTION', 'UPDATE', async (request: NextRequest, user: any) => {
  const url = new URL(request.url)
  const planId = url.pathname.split('/').pop()
  const body = await request.json()

  // Only system admins can update plans
  if (!['FOUNDER', 'SUPER_ADMIN'].includes(user.role)) {
    return createErrorResponse('Insufficient permissions to update subscription plans', 403)
  }

  const updateSchema = planSchema.partial()
  const data = updateSchema.parse(body)

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Check if plan exists
      const existingPlan = await tx.subscriptionPlan.findUnique({
        where: { id: planId },
        include: { features: true }
      })

      if (!existingPlan) {
        throw new Error('Subscription plan not found')
      }

      // Check if plan has active subscriptions
      const activeSubscriptions = await tx.subscription.count({
        where: {
          planId,
          status: { in: ['ACTIVE', 'TRIAL'] }
        }
      })

      // Restrict certain changes if there are active subscriptions
      if (activeSubscriptions > 0) {
        if (data.limits && JSON.stringify(data.limits) !== JSON.stringify(existingPlan.limits)) {
          throw new Error('Cannot change plan limits while there are active subscriptions')
        }
      }

      // Update plan
      const updatedPlan = await tx.subscriptionPlan.update({
        where: { id: planId },
        data: {
          name: data.name,
          description: data.description,
          category: data.category,
          pricing: data.pricing,
          limits: data.limits,
          isActive: data.isActive,
          isPublic: data.isPublic,
          trialDays: data.trialDays,
          sortOrder: data.sortOrder,
          metadata: data.metadata,
          updatedAt: new Date()
        }
      })

      // Update features if provided
      if (data.features) {
        // Delete existing features
        await tx.subscriptionPlanFeature.deleteMany({
          where: { planId }
        })

        // Create new features
        if (data.features.length > 0) {
          await tx.subscriptionPlanFeature.createMany({
            data: data.features.map(feature => ({
              planId,
              name: feature.name,
              description: feature.description,
              included: feature.included,
              limit: feature.limit,
              unit: feature.unit,
              category: feature.category
            }))
          })
        }
      }

      return updatedPlan
    })

    // Create audit log
    await createAuditLog(
      'UPDATE',
      'SUBSCRIPTION_PLAN',
      `Updated subscription plan: ${result.name}`,
      user.id,
      'system'
    )

    return createSuccessResponse(result, 'Subscription plan updated successfully')

  } catch (error) {
    console.error('Error updating subscription plan:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to update subscription plan',
      400
    )
  }
})

// DELETE /api/subscriptions/plans/[id] - Delete subscription plan
export const DELETE = withPermission('SUBSCRIPTION', 'DELETE', async (request: NextRequest, user: any) => {
  const url = new URL(request.url)
  const planId = url.pathname.split('/').pop()

  // Only system admins can delete plans
  if (!['FOUNDER', 'SUPER_ADMIN'].includes(user.role)) {
    return createErrorResponse('Insufficient permissions to delete subscription plans', 403)
  }

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Check if plan exists
      const plan = await tx.subscriptionPlan.findUnique({
        where: { id: planId }
      })

      if (!plan) {
        throw new Error('Subscription plan not found')
      }

      // Check for active subscriptions
      const activeSubscriptions = await tx.subscription.count({
        where: {
          planId,
          status: { in: ['ACTIVE', 'TRIAL'] }
        }
      })

      if (activeSubscriptions > 0) {
        throw new Error('Cannot delete plan with active subscriptions')
      }

      // Soft delete by marking as inactive
      const deletedPlan = await tx.subscriptionPlan.update({
        where: { id: planId },
        data: {
          isActive: false,
          isPublic: false,
          deletedAt: new Date()
        }
      })

      return deletedPlan
    })

    // Create audit log
    await createAuditLog(
      'DELETE',
      'SUBSCRIPTION_PLAN',
      `Deleted subscription plan: ${result.name}`,
      user.id,
      'system'
    )

    return createSuccessResponse({}, 'Subscription plan deleted successfully')

  } catch (error) {
    console.error('Error deleting subscription plan:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to delete subscription plan',
      400
    )
  }
})

// Helper functions

function getRecommendedFor(plan: any): string[] {
  const recommendations = []

  switch (plan.category) {
    case 'BASIC':
      recommendations.push('Small businesses', 'Startups', 'Individual stores')
      break
    case 'PROFESSIONAL':
      recommendations.push('Growing businesses', 'Multiple locations', 'Medium enterprises')
      break
    case 'ENTERPRISE':
      recommendations.push('Large enterprises', 'Complex operations', 'High volume businesses')
      break
    case 'CUSTOM':
      recommendations.push('Specific requirements', 'Custom integrations', 'Enterprise solutions')
      break
  }

  // Add recommendations based on limits
  if (plan.limits.users >= 50) {
    recommendations.push('Large teams')
  }

  if (plan.limits.stores > 1) {
    recommendations.push('Multi-store operations')
  }

  if (plan.limits.products >= 10000) {
    recommendations.push('Large inventory')
  }

  return recommendations
}
