import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'

// GET /api/financial/reports - Get comprehensive financial reports
export const GET = withPermission('FINANCIAL', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { searchParams } = new URL(request.url)
  const reportType = searchParams.get('type') || 'summary' // summary, pnl, cashflow, balance
  const period = searchParams.get('period') || 'month' // day, week, month, quarter, year
  const startDate = searchParams.get('startDate')
  const endDate = searchParams.get('endDate')

  try {
    let dateRange = getDateRange(period, startDate, endDate)

    let report: any = {}

    switch (reportType) {
      case 'summary':
        report = await getFinancialSummary(storeId, dateRange)
        break
      case 'pnl':
        report = await getProfitAndLoss(storeId, dateRange)
        break
      case 'cashflow':
        report = await getCashFlow(storeId, dateRange)
        break
      case 'balance':
        report = await getBalanceSheet(storeId, dateRange)
        break
      default:
        return createErrorResponse('Invalid report type', 400)
    }

    return createSuccessResponse({
      reportType,
      period: {
        type: period,
        startDate: dateRange.startDate,
        endDate: dateRange.endDate
      },
      data: report,
      generatedAt: new Date()
    }, 'Financial report generated successfully')

  } catch (error) {
    console.error('Error generating financial report:', error)
    return createErrorResponse('Failed to generate financial report', 500)
  }
})

// Helper functions for different report types

async function getFinancialSummary(storeId: string, dateRange: any) {
  const [revenue, expenses, purchases, payments] = await Promise.all([
    // Revenue from sales
    prisma.sale.aggregate({
      where: {
        storeId,
        createdAt: { gte: dateRange.startDate, lte: dateRange.endDate },
        status: 'COMPLETED'
      },
      _sum: { total: true },
      _count: true
    }),

    // Total expenses
    prisma.expense.aggregate({
      where: {
        storeId,
        date: { gte: dateRange.startDate, lte: dateRange.endDate },
        status: { in: ['APPROVED'] }
      },
      _sum: { totalAmount: true },
      _count: true
    }),

    // Purchase costs
    prisma.purchase.aggregate({
      where: {
        storeId,
        createdAt: { gte: dateRange.startDate, lte: dateRange.endDate },
        status: { in: ['COMPLETED', 'RECEIVED'] }
      },
      _sum: { totalAmount: true },
      _count: true
    }),

    // Payment collections
    prisma.payment.aggregate({
      where: {
        storeId,
        createdAt: { gte: dateRange.startDate, lte: dateRange.endDate },
        status: 'COMPLETED'
      },
      _sum: { amount: true },
      _count: true
    })
  ])

  const totalRevenue = revenue._sum.total || 0
  const totalExpenses = expenses._sum.totalAmount || 0
  const totalPurchases = purchases._sum.totalAmount || 0
  const totalPayments = payments._sum.amount || 0

  const grossProfit = totalRevenue - totalPurchases
  const netProfit = grossProfit - totalExpenses

  return {
    revenue: {
      total: totalRevenue,
      count: revenue._count
    },
    expenses: {
      total: totalExpenses,
      count: expenses._count
    },
    purchases: {
      total: totalPurchases,
      count: purchases._count
    },
    payments: {
      total: totalPayments,
      count: payments._count
    },
    profitability: {
      grossProfit,
      netProfit,
      grossMargin: totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0,
      netMargin: totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0
    }
  }
}

async function getProfitAndLoss(storeId: string, dateRange: any) {
  const [salesData, purchaseData, expenseData, taxData] = await Promise.all([
    // Sales revenue breakdown
    prisma.sale.findMany({
      where: {
        storeId,
        createdAt: { gte: dateRange.startDate, lte: dateRange.endDate },
        status: 'COMPLETED'
      },
      select: {
        total: true,
        taxAmount: true,
        discount: true,
        items: {
          include: {
            product: {
              select: {
                costPrice: true
              }
            }
          }
        }
      }
    }),

    // Cost of goods sold
    prisma.purchase.aggregate({
      where: {
        storeId,
        createdAt: { gte: dateRange.startDate, lte: dateRange.endDate },
        status: { in: ['COMPLETED', 'RECEIVED'] }
      },
      _sum: { totalAmount: true }
    }),

    // Operating expenses by category
    prisma.expense.groupBy({
      by: ['category'],
      where: {
        storeId,
        date: { gte: dateRange.startDate, lte: dateRange.endDate },
        status: 'APPROVED'
      },
      _sum: { totalAmount: true }
    }),

    // Tax information
    prisma.sale.aggregate({
      where: {
        storeId,
        createdAt: { gte: dateRange.startDate, lte: dateRange.endDate },
        status: 'COMPLETED'
      },
      _sum: { taxAmount: true }
    })
  ])

  // Calculate COGS from sales items
  let totalCOGS = 0
  salesData.forEach(sale => {
    sale.items.forEach(item => {
      totalCOGS += item.quantity * item.product.costPrice
    })
  })

  const totalRevenue = salesData.reduce((sum, sale) => sum + sale.total, 0)
  const totalDiscount = salesData.reduce((sum, sale) => sum + sale.discount, 0)
  const grossRevenue = totalRevenue + totalDiscount

  const grossProfit = totalRevenue - totalCOGS
  
  const operatingExpenses = expenseData.reduce((acc, expense) => {
    acc[expense.category] = expense._sum.totalAmount || 0
    acc.total += expense._sum.totalAmount || 0
    return acc
  }, { total: 0 } as any)

  const operatingIncome = grossProfit - operatingExpenses.total
  const netIncome = operatingIncome // Simplified, would include other income/expenses

  return {
    revenue: {
      grossRevenue,
      discounts: totalDiscount,
      netRevenue: totalRevenue
    },
    costOfGoodsSold: totalCOGS,
    grossProfit: {
      amount: grossProfit,
      margin: totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0
    },
    operatingExpenses,
    operatingIncome: {
      amount: operatingIncome,
      margin: totalRevenue > 0 ? (operatingIncome / totalRevenue) * 100 : 0
    },
    netIncome: {
      amount: netIncome,
      margin: totalRevenue > 0 ? (netIncome / totalRevenue) * 100 : 0
    },
    taxes: {
      collected: taxData._sum.taxAmount || 0
    }
  }
}

async function getCashFlow(storeId: string, dateRange: any) {
  const [cashInflows, cashOutflows, openingBalance] = await Promise.all([
    // Cash inflows
    Promise.all([
      prisma.payment.aggregate({
        where: {
          storeId,
          createdAt: { gte: dateRange.startDate, lte: dateRange.endDate },
          status: 'COMPLETED'
        },
        _sum: { amount: true }
      }),
      prisma.sale.aggregate({
        where: {
          storeId,
          createdAt: { gte: dateRange.startDate, lte: dateRange.endDate },
          status: 'COMPLETED',
          paymentStatus: 'PAID'
        },
        _sum: { total: true }
      })
    ]),

    // Cash outflows
    Promise.all([
      prisma.expense.aggregate({
        where: {
          storeId,
          date: { gte: dateRange.startDate, lte: dateRange.endDate },
          status: 'APPROVED'
        },
        _sum: { totalAmount: true }
      }),
      prisma.purchase.aggregate({
        where: {
          storeId,
          createdAt: { gte: dateRange.startDate, lte: dateRange.endDate },
          status: { in: ['COMPLETED', 'RECEIVED'] }
        },
        _sum: { paidAmount: true }
      })
    ]),

    // Opening balance (simplified)
    0 // This would come from previous period's closing balance
  ])

  const totalInflows = (cashInflows[0]._sum.amount || 0) + (cashInflows[1]._sum.total || 0)
  const totalOutflows = (cashOutflows[0]._sum.totalAmount || 0) + (cashOutflows[1]._sum.paidAmount || 0)
  const netCashFlow = totalInflows - totalOutflows
  const closingBalance = openingBalance + netCashFlow

  return {
    openingBalance,
    inflows: {
      sales: cashInflows[1]._sum.total || 0,
      payments: cashInflows[0]._sum.amount || 0,
      total: totalInflows
    },
    outflows: {
      expenses: cashOutflows[0]._sum.totalAmount || 0,
      purchases: cashOutflows[1]._sum.paidAmount || 0,
      total: totalOutflows
    },
    netCashFlow,
    closingBalance
  }
}

async function getBalanceSheet(storeId: string, dateRange: any) {
  const [inventory, receivables, payables, assets] = await Promise.all([
    // Inventory value
    prisma.inventory.findMany({
      where: { storeId },
      include: {
        product: {
          select: { costPrice: true }
        }
      }
    }).then(items => 
      items.reduce((total, item) => total + (item.quantity * item.product.costPrice), 0)
    ),

    // Accounts receivable
    prisma.sale.aggregate({
      where: {
        storeId,
        paymentStatus: { in: ['PARTIAL', 'PENDING'] }
      },
      _sum: { total: true }
    }),

    // Accounts payable
    prisma.purchase.aggregate({
      where: {
        storeId,
        status: { in: ['APPROVED', 'RECEIVED'] }
      },
      _sum: { totalAmount: true }
    }).then(async (total) => {
      const paid = await prisma.purchase.aggregate({
        where: {
          storeId,
          status: { in: ['APPROVED', 'RECEIVED'] }
        },
        _sum: { paidAmount: true }
      })
      return (total._sum.totalAmount || 0) - (paid._sum.paidAmount || 0)
    }),

    // Fixed assets (simplified)
    0 // This would come from asset management
  ])

  const currentAssets = inventory + (receivables._sum.total || 0)
  const totalAssets = currentAssets + assets
  const currentLiabilities = payables
  const totalLiabilities = currentLiabilities
  const equity = totalAssets - totalLiabilities

  return {
    assets: {
      current: {
        inventory,
        accountsReceivable: receivables._sum.total || 0,
        total: currentAssets
      },
      fixed: assets,
      total: totalAssets
    },
    liabilities: {
      current: {
        accountsPayable: payables,
        total: currentLiabilities
      },
      total: totalLiabilities
    },
    equity: {
      total: equity
    }
  }
}

function getDateRange(period: string, startDate?: string | null, endDate?: string | null) {
  if (startDate && endDate) {
    return {
      startDate: new Date(startDate),
      endDate: new Date(endDate)
    }
  }

  const now = new Date()
  let start = new Date()

  switch (period) {
    case 'day':
      start.setHours(0, 0, 0, 0)
      break
    case 'week':
      start.setDate(now.getDate() - 7)
      break
    case 'month':
      start.setMonth(now.getMonth() - 1)
      break
    case 'quarter':
      start.setMonth(now.getMonth() - 3)
      break
    case 'year':
      start.setFullYear(now.getFullYear() - 1)
      break
    default:
      start.setMonth(now.getMonth() - 1)
  }

  return {
    startDate: start,
    endDate: now
  }
}
