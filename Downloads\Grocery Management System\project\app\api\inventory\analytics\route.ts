import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'

// GET /api/inventory/analytics - Get comprehensive inventory analytics
export const GET = withPermission('INVENTORY', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { searchParams } = new URL(request.url)
  const period = searchParams.get('period') || '30' // days
  const includeMovements = searchParams.get('includeMovements') === 'true'

  const periodDays = parseInt(period)
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - periodDays)

  try {
    // Get comprehensive inventory analytics
    const [
      inventorySummary,
      stockAlerts,
      categoryAnalytics,
      valueAnalytics,
      movementAnalytics,
      topProducts,
      recentAdjustments
    ] = await Promise.all([
      // Inventory Summary
      getInventorySummary(storeId),
      
      // Stock Alerts
      getStockAlerts(storeId),
      
      // Category Analytics
      getCategoryAnalytics(storeId),
      
      // Value Analytics
      getValueAnalytics(storeId),
      
      // Movement Analytics (if requested)
      includeMovements ? getMovementAnalytics(storeId, startDate) : null,
      
      // Top Products by Value
      getTopProductsByValue(storeId),
      
      // Recent Adjustments
      getRecentAdjustments(storeId, 10)
    ])

    const analytics = {
      summary: inventorySummary,
      alerts: stockAlerts,
      categories: categoryAnalytics,
      value: valueAnalytics,
      topProducts,
      recentAdjustments,
      ...(movementAnalytics && { movements: movementAnalytics })
    }

    return createSuccessResponse(analytics, 'Inventory analytics retrieved successfully')

  } catch (error) {
    console.error('Error fetching inventory analytics:', error)
    return createErrorResponse('Failed to fetch inventory analytics', 500)
  }
})

// Helper functions for analytics

async function getInventorySummary(storeId: string) {
  const [totalProducts, totalQuantity, lowStockCount, outOfStockCount] = await Promise.all([
    prisma.inventory.count({
      where: { storeId }
    }),
    
    prisma.inventory.aggregate({
      where: { storeId },
      _sum: { quantity: true }
    }),
    
    prisma.inventory.count({
      where: {
        storeId,
        quantity: { lte: prisma.inventory.fields.reorderLevel },
        quantity: { gt: 0 }
      }
    }),
    
    prisma.inventory.count({
      where: {
        storeId,
        quantity: 0
      }
    })
  ])

  return {
    totalProducts,
    totalQuantity: totalQuantity._sum.quantity || 0,
    lowStockCount,
    outOfStockCount,
    inStockCount: totalProducts - outOfStockCount
  }
}

async function getStockAlerts(storeId: string) {
  const [lowStockItems, outOfStockItems, expiringItems] = await Promise.all([
    // Low stock items
    prisma.inventory.findMany({
      where: {
        storeId,
        quantity: { lte: prisma.inventory.fields.reorderLevel },
        quantity: { gt: 0 }
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
            minStock: true
          }
        }
      },
      orderBy: { quantity: 'asc' },
      take: 20
    }),
    
    // Out of stock items
    prisma.inventory.findMany({
      where: {
        storeId,
        quantity: 0
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true
          }
        }
      },
      orderBy: { lastUpdated: 'desc' },
      take: 20
    }),
    
    // Items that might expire soon (placeholder - would need expiry date field)
    []
  ])

  return {
    lowStock: lowStockItems.map(item => ({
      productId: item.product.id,
      productName: item.product.name,
      sku: item.product.sku,
      currentQuantity: item.quantity,
      reorderLevel: item.reorderLevel,
      minStock: item.product.minStock,
      urgency: item.quantity <= (item.reorderLevel * 0.5) ? 'high' : 'medium'
    })),
    outOfStock: outOfStockItems.map(item => ({
      productId: item.product.id,
      productName: item.product.name,
      sku: item.product.sku,
      lastUpdated: item.lastUpdated
    })),
    expiring: expiringItems
  }
}

async function getCategoryAnalytics(storeId: string) {
  const categoryData = await prisma.inventory.findMany({
    where: { storeId },
    include: {
      product: {
        include: {
          category: {
            select: {
              id: true,
              name: true
            }
          }
        }
      }
    }
  })

  const categoryMap = new Map()
  
  categoryData.forEach(item => {
    const categoryName = item.product.category.name
    const existing = categoryMap.get(categoryName) || {
      categoryId: item.product.category.id,
      categoryName,
      totalProducts: 0,
      totalQuantity: 0,
      totalValue: 0,
      lowStockCount: 0,
      outOfStockCount: 0
    }

    existing.totalProducts += 1
    existing.totalQuantity += item.quantity
    existing.totalValue += item.quantity * item.product.costPrice
    
    if (item.quantity === 0) {
      existing.outOfStockCount += 1
    } else if (item.quantity <= item.reorderLevel) {
      existing.lowStockCount += 1
    }

    categoryMap.set(categoryName, existing)
  })

  return Array.from(categoryMap.values()).sort((a, b) => b.totalValue - a.totalValue)
}

async function getValueAnalytics(storeId: string) {
  const inventoryWithProducts = await prisma.inventory.findMany({
    where: { storeId },
    include: {
      product: {
        select: {
          costPrice: true,
          sellingPrice: true
        }
      }
    }
  })

  const analytics = inventoryWithProducts.reduce((acc, item) => {
    const costValue = item.quantity * item.product.costPrice
    const sellingValue = item.quantity * item.product.sellingPrice
    const potentialProfit = sellingValue - costValue

    acc.totalCostValue += costValue
    acc.totalSellingValue += sellingValue
    acc.totalPotentialProfit += potentialProfit

    return acc
  }, {
    totalCostValue: 0,
    totalSellingValue: 0,
    totalPotentialProfit: 0
  })

  return {
    ...analytics,
    averageMargin: analytics.totalCostValue > 0 
      ? ((analytics.totalPotentialProfit / analytics.totalCostValue) * 100) 
      : 0
  }
}

async function getMovementAnalytics(storeId: string, startDate: Date) {
  const movements = await prisma.inventoryAdjustment.findMany({
    where: {
      storeId,
      createdAt: { gte: startDate }
    },
    include: {
      product: {
        select: {
          name: true,
          sku: true
        }
      }
    },
    orderBy: { createdAt: 'desc' }
  })

  const dailyMovements = new Map()
  const typeBreakdown = { IN: 0, OUT: 0, SET: 0 }

  movements.forEach(movement => {
    const date = movement.createdAt.toISOString().split('T')[0]
    const existing = dailyMovements.get(date) || { date, in: 0, out: 0, adjustments: 0 }

    if (movement.adjustmentType === 'IN') {
      existing.in += movement.quantityChanged
      typeBreakdown.IN += movement.quantityChanged
    } else if (movement.adjustmentType === 'OUT') {
      existing.out += Math.abs(movement.quantityChanged)
      typeBreakdown.OUT += Math.abs(movement.quantityChanged)
    } else {
      existing.adjustments += 1
      typeBreakdown.SET += 1
    }

    dailyMovements.set(date, existing)
  })

  return {
    dailyMovements: Array.from(dailyMovements.values()).sort((a, b) => a.date.localeCompare(b.date)),
    typeBreakdown,
    totalMovements: movements.length
  }
}

async function getTopProductsByValue(storeId: string) {
  const products = await prisma.inventory.findMany({
    where: { storeId },
    include: {
      product: {
        select: {
          id: true,
          name: true,
          sku: true,
          costPrice: true,
          sellingPrice: true
        }
      }
    }
  })

  return products
    .map(item => ({
      productId: item.product.id,
      productName: item.product.name,
      sku: item.product.sku,
      quantity: item.quantity,
      costValue: item.quantity * item.product.costPrice,
      sellingValue: item.quantity * item.product.sellingPrice,
      potentialProfit: item.quantity * (item.product.sellingPrice - item.product.costPrice)
    }))
    .sort((a, b) => b.sellingValue - a.sellingValue)
    .slice(0, 10)
}

async function getRecentAdjustments(storeId: string, limit: number) {
  return await prisma.inventoryAdjustment.findMany({
    where: { storeId },
    include: {
      product: {
        select: {
          name: true,
          sku: true
        }
      },
      createdBy: {
        select: {
          name: true
        }
      }
    },
    orderBy: { createdAt: 'desc' },
    take: limit
  })
}
