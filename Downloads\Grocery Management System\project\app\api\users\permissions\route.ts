import { NextRequest } from 'next/server'
import { 
  withPermission, 
  createSuccessResponse
} from '@/lib/api-utils'

// GET /api/users/permissions - Get all available permissions
export const GET = withPermission('USER', 'READ', async (request: NextRequest, user: any) => {
  const allPermissions = {
    DASHBOARD: ['READ'],
    USER: ['CREATE', 'READ', 'UPDATE', 'DELETE'],
    STORE: ['CREATE', 'READ', 'UPDATE', 'DELETE'],
    PRODUCT: ['CREATE', 'READ', 'UPDATE', 'DELETE'],
    CATEGORY: ['CREATE', 'READ', 'UPDATE', 'DELETE'],
    INVENTORY: ['READ', 'UPDATE'],
    SALES: ['CREATE', 'READ', 'UPDATE', 'DELETE'],
    PURCHASE: ['CREATE', 'READ', 'UPDATE', 'DELETE'],
    CUSTOMER: ['CREATE', 'READ', 'UPDATE', 'DELETE'],
    SUPPLIER: ['CREATE', 'READ', 'UPDATE', 'DELETE'],
    EXPENSE: ['CREATE', 'READ', 'UPDATE', 'DELETE'],
    REPORTS: ['READ'],
    SETTINGS: ['READ', 'UPDATE'],
    AUDIT: ['READ']
  }

  const formattedPermissions = Object.entries(allPermissions).map(([module, actions]) => ({
    module,
    actions,
    permissions: actions.map(action => `${module}:${action}`)
  }))

  return createSuccessResponse(formattedPermissions)
})
