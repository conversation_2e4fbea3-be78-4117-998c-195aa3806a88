/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(dashboard)/dashboard/page";
exports.ids = ["app/(dashboard)/dashboard/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(dashboard)%2Fdashboard%2Fpage&page=%2F(dashboard)%2Fdashboard%2Fpage&appPaths=%2F(dashboard)%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(dashboard)%2Fdashboard%2Fpage&page=%2F(dashboard)%2Fdashboard%2Fpage&appPaths=%2F(dashboard)%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?b6e7\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n// @ts-ignore this need to be imported from next/dist to be external\n\n\nconst AppPageRouteModule = next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule;\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(dashboard)',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(dashboard)/dashboard/page.tsx */ \"(rsc)/./app/(dashboard)/dashboard/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(dashboard)/layout.tsx */ \"(rsc)/./app/(dashboard)/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\"];\n\n// @ts-expect-error - replaced by webpack/turbopack loader\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/(dashboard)/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(dashboard)/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(dashboard)%2Fdashboard%2Fpage&page=%2F(dashboard)%2Fdashboard%2Fpage&appPaths=%2F(dashboard)%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp%5C(dashboard)%5Cdashboard%5Cpage.tsx&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp%5C(dashboard)%5Cdashboard%5Cpage.tsx&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(dashboard)/dashboard/page.tsx */ \"(ssr)/./app/(dashboard)/dashboard/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDREVMTCU1Q0Rvd25sb2FkcyU1Q0dyb2NlcnklMjBNYW5hZ2VtZW50JTIwU3lzdGVtJTVDcHJvamVjdCU1Q2FwcCU1QyhkYXNoYm9hcmQpJTVDZGFzaGJvYXJkJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ3JvY2VyeS1tYW5hZ2VtZW50LXN5c3RlbS8/NTc5NiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXERFTExcXFxcRG93bmxvYWRzXFxcXEdyb2NlcnkgTWFuYWdlbWVudCBTeXN0ZW1cXFxccHJvamVjdFxcXFxhcHBcXFxcKGRhc2hib2FyZClcXFxcZGFzaGJvYXJkXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp%5C(dashboard)%5Cdashboard%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp%5C(dashboard)%5Clayout.tsx&server=true!":
/*!*************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp%5C(dashboard)%5Clayout.tsx&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(dashboard)/layout.tsx */ \"(ssr)/./app/(dashboard)/layout.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDREVMTCU1Q0Rvd25sb2FkcyU1Q0dyb2NlcnklMjBNYW5hZ2VtZW50JTIwU3lzdGVtJTVDcHJvamVjdCU1Q2FwcCU1QyhkYXNoYm9hcmQpJTVDbGF5b3V0LnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ncm9jZXJ5LW1hbmFnZW1lbnQtc3lzdGVtLz9jZTNjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcREVMTFxcXFxEb3dubG9hZHNcXFxcR3JvY2VyeSBNYW5hZ2VtZW50IFN5c3RlbVxcXFxwcm9qZWN0XFxcXGFwcFxcXFwoZGFzaGJvYXJkKVxcXFxsYXlvdXQudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp%5C(dashboard)%5Clayout.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Ccomponents%5Cauth%5Cauth-provider.tsx&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Ccomponents%5Cui%5Csonner.tsx&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Ccomponents%5Cauth%5Cauth-provider.tsx&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Ccomponents%5Cui%5Csonner.tsx&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/auth/auth-provider.tsx */ \"(ssr)/./components/auth/auth-provider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/sonner.tsx */ \"(ssr)/./components/ui/sonner.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDREVMTCU1Q0Rvd25sb2FkcyU1Q0dyb2NlcnklMjBNYW5hZ2VtZW50JTIwU3lzdGVtJTVDcHJvamVjdCU1Q2FwcCU1Q2dsb2JhbHMuY3NzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDREVMTCU1Q0Rvd25sb2FkcyU1Q0dyb2NlcnklMjBNYW5hZ2VtZW50JTIwU3lzdGVtJTVDcHJvamVjdCU1Q2NvbXBvbmVudHMlNUNhdXRoJTVDYXV0aC1wcm92aWRlci50c3gmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNERUxMJTVDRG93bmxvYWRzJTVDR3JvY2VyeSUyME1hbmFnZW1lbnQlMjBTeXN0ZW0lNUNwcm9qZWN0JTVDY29tcG9uZW50cyU1Q3VpJTVDc29ubmVyLnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0RFTEwlNUNEb3dubG9hZHMlNUNHcm9jZXJ5JTIwTWFuYWdlbWVudCUyMFN5c3RlbSU1Q3Byb2plY3QlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZm9udCU1Q2dvb2dsZSU1Q3RhcmdldC5jc3MlM0YlN0IlMjJwYXRoJTIyJTNBJTIyYXBwJTVDJTVDbGF5b3V0LnRzeCUyMiUyQyUyMmltcG9ydCUyMiUzQSUyMkludGVyJTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmxhdGluJTIyJTVEJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtMQUF3STtBQUN4SSIsInNvdXJjZXMiOlsid2VicGFjazovL2dyb2NlcnktbWFuYWdlbWVudC1zeXN0ZW0vPzk5OGQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxERUxMXFxcXERvd25sb2Fkc1xcXFxHcm9jZXJ5IE1hbmFnZW1lbnQgU3lzdGVtXFxcXHByb2plY3RcXFxcY29tcG9uZW50c1xcXFxhdXRoXFxcXGF1dGgtcHJvdmlkZXIudHN4XCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxERUxMXFxcXERvd25sb2Fkc1xcXFxHcm9jZXJ5IE1hbmFnZW1lbnQgU3lzdGVtXFxcXHByb2plY3RcXFxcY29tcG9uZW50c1xcXFx1aVxcXFxzb25uZXIudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Ccomponents%5Cauth%5Cauth-provider.tsx&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Ccomponents%5Cui%5Csonner.tsx&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/(dashboard)/dashboard/page.tsx":
/*!********************************************!*\
  !*** ./app/(dashboard)/dashboard/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/shared/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_auth_auth_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth/auth-provider */ \"(ssr)/./components/auth/auth-provider.tsx\");\n/* harmony import */ var _components_dashboard_stats_cards__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/stats-cards */ \"(ssr)/./components/dashboard/stats-cards.tsx\");\n/* harmony import */ var _components_dashboard_sales_chart__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/sales-chart */ \"(ssr)/./components/dashboard/sales-chart.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/separator */ \"(ssr)/./components/ui/separator.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_RefreshCw_TrendingUp_Users_Package_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,RefreshCw,TrendingUp,Users,Package,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_RefreshCw_TrendingUp_Users_Package_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,RefreshCw,TrendingUp,Users,Package,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_RefreshCw_TrendingUp_Users_Package_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,RefreshCw,TrendingUp,Users,Package,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_RefreshCw_TrendingUp_Users_Package_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,RefreshCw,TrendingUp,Users,Package,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_RefreshCw_TrendingUp_Users_Package_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,RefreshCw,TrendingUp,Users,Package,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_RefreshCw_TrendingUp_Users_Package_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,RefreshCw,TrendingUp,Users,Package,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    const { user, token } = (0,_components_auth_auth_provider__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [salesData, setSalesData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchDashboardData = async ()=>{\n        if (!token) return;\n        try {\n            setIsLoading(true);\n            const [statsResponse, salesChartResponse] = await Promise.all([\n                fetch(\"/api/dashboard/stats\", {\n                    headers: {\n                        Authorization: `Bearer ${token}`\n                    }\n                }),\n                fetch(\"/api/dashboard/sales-chart\", {\n                    headers: {\n                        Authorization: `Bearer ${token}`\n                    }\n                })\n            ]);\n            if (statsResponse.ok) {\n                const statsData = await statsResponse.json();\n                setStats(statsData);\n            }\n            if (salesChartResponse.ok) {\n                const chartData = await salesChartResponse.json();\n                setSalesData(chartData);\n            }\n        } catch (error) {\n            console.error(\"Error fetching dashboard data:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Failed to load dashboard data\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchDashboardData();\n    }, [\n        token\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_RefreshCw_TrendingUp_Users_Package_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Loading dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 space-y-6 p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold tracking-tight\",\n                                children: \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: [\n                                    \"Welcome back, \",\n                                    user?.name,\n                                    \"! Here's what's happening with your store.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: fetchDashboardData,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_RefreshCw_TrendingUp_Users_Package_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_RefreshCw_TrendingUp_Users_Package_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Notifications\",\n                                    stats && stats.lowStockItems > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                        variant: \"destructive\",\n                                        className: \"ml-2\",\n                                        children: stats.lowStockItems\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_stats_cards__WEBPACK_IMPORTED_MODULE_3__.StatsCards, {\n                stats: stats\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 105,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_sales_chart__WEBPACK_IMPORTED_MODULE_4__.SalesChart, {\n                            data: salesData\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_RefreshCw_TrendingUp_Users_Package_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Quick Actions\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                        className: \"w-full justify-start\",\n                                        variant: \"outline\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_RefreshCw_TrendingUp_Users_Package_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Add New Product\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                        className: \"w-full justify-start\",\n                                        variant: \"outline\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_RefreshCw_TrendingUp_Users_Package_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Create Sale\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                        className: \"w-full justify-start\",\n                                        variant: \"outline\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_RefreshCw_TrendingUp_Users_Package_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Add Customer\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                        className: \"w-full justify-start\",\n                                        variant: \"outline\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_RefreshCw_TrendingUp_Users_Package_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"View Reports\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            stats && stats.lowStockItems > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"border-orange-200 bg-orange-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                            className: \"text-orange-800 flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_RefreshCw_TrendingUp_Users_Package_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-5 w-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, this),\n                                \"Low Stock Alert\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-orange-700\",\n                                children: [\n                                    \"You have \",\n                                    stats.lowStockItems,\n                                    \" items that are running low on stock. Consider restocking soon to avoid stockouts.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                variant: \"outline\",\n                                className: \"mt-3 border-orange-300 text-orange-700 hover:bg-orange-100\",\n                                children: \"View Low Stock Items\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/(dashboard)/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/(dashboard)/layout.tsx":
/*!************************************!*\
  !*** ./app/(dashboard)/layout.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/shared/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_auth_auth_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/auth/auth-provider */ \"(ssr)/./components/auth/auth-provider.tsx\");\n/* harmony import */ var _components_layout_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/sidebar */ \"(ssr)/./components/layout/sidebar.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DashboardLayout({ children }) {\n    const { user, logout, isLoading } = (0,_components_auth_auth_provider__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        console.log(\"Dashboard Layout: isLoading:\", isLoading, \"user:\", !!user);\n        if (!isLoading && !user) {\n            console.log(\"Dashboard Layout: No user found, redirecting to login\");\n            router.push(\"/login\");\n        }\n    }, [\n        user,\n        isLoading,\n        router\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-muted-foreground\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\layout.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\layout.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_sidebar__WEBPACK_IMPORTED_MODULE_2__.Sidebar, {\n                userRole: user.role,\n                onLogout: logout\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\layout.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 overflow-y-auto\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\layout.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\(dashboard)\\\\layout.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/(dashboard)/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./components/auth/auth-provider.tsx":
/*!*******************************************!*\
  !*** ./components/auth/auth-provider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/shared/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"Auth: Initializing auth provider\");\n        const savedToken = localStorage.getItem(\"auth-token\");\n        const savedUser = localStorage.getItem(\"auth-user\");\n        console.log(\"Auth: Saved token exists:\", !!savedToken);\n        console.log(\"Auth: Saved user exists:\", !!savedUser);\n        if (savedToken && savedUser) {\n            console.log(\"Auth: Restoring user session\");\n            setToken(savedToken);\n            setUser(JSON.parse(savedUser));\n            // Ensure cookie is set if localStorage has token\n            document.cookie = `auth-token=${savedToken}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Lax`;\n        }\n        setIsLoading(false);\n        console.log(\"Auth: Auth provider initialized\");\n    }, []);\n    const login = async (email, password)=>{\n        try {\n            const response = await fetch(\"/api/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.error || \"Login failed\");\n            }\n            const data = await response.json();\n            console.log(\"Auth: Login successful, setting user and token\");\n            setUser(data.user);\n            setToken(data.token);\n            // Store in localStorage\n            localStorage.setItem(\"auth-token\", data.token);\n            localStorage.setItem(\"auth-user\", JSON.stringify(data.user));\n            // Also set cookie for middleware\n            const maxAge = 7 * 24 * 60 * 60 // 7 days\n            ;\n            const cookieValue = `auth-token=${data.token}; path=/; max-age=${maxAge}; SameSite=Lax`;\n            document.cookie = cookieValue;\n            console.log(\"Auth: Cookie set:\", cookieValue);\n            // Verify cookie was set\n            setTimeout(()=>{\n                const cookies = document.cookie;\n                console.log(\"Auth: All cookies after setting:\", cookies);\n                console.log(\"Auth: Token cookie exists:\", cookies.includes(\"auth-token\"));\n            }, 50);\n        } catch (error) {\n            throw error;\n        }\n    };\n    const logout = ()=>{\n        setUser(null);\n        setToken(null);\n        localStorage.removeItem(\"auth-token\");\n        localStorage.removeItem(\"auth-user\");\n        // Clear cookie\n        document.cookie = \"auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            token,\n            login,\n            logout,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\auth\\\\auth-provider.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/auth/auth-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/sales-chart.tsx":
/*!**********************************************!*\
  !*** ./components/dashboard/sales-chart.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SalesChart: () => (/* binding */ SalesChart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/shared/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_ResponsiveContainer_LineChart_Line_XAxis_YAxis_CartesianGrid_Tooltip_Legend_recharts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ResponsiveContainer,LineChart,Line,XAxis,YAxis,CartesianGrid,Tooltip,Legend!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_ResponsiveContainer_LineChart_Line_XAxis_YAxis_CartesianGrid_Tooltip_Legend_recharts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ResponsiveContainer,LineChart,Line,XAxis,YAxis,CartesianGrid,Tooltip,Legend!=!recharts */ \"(ssr)/./node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_ResponsiveContainer_LineChart_Line_XAxis_YAxis_CartesianGrid_Tooltip_Legend_recharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ResponsiveContainer,LineChart,Line,XAxis,YAxis,CartesianGrid,Tooltip,Legend!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_ResponsiveContainer_LineChart_Line_XAxis_YAxis_CartesianGrid_Tooltip_Legend_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ResponsiveContainer,LineChart,Line,XAxis,YAxis,CartesianGrid,Tooltip,Legend!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_ResponsiveContainer_LineChart_Line_XAxis_YAxis_CartesianGrid_Tooltip_Legend_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ResponsiveContainer,LineChart,Line,XAxis,YAxis,CartesianGrid,Tooltip,Legend!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_ResponsiveContainer_LineChart_Line_XAxis_YAxis_CartesianGrid_Tooltip_Legend_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ResponsiveContainer,LineChart,Line,XAxis,YAxis,CartesianGrid,Tooltip,Legend!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_ResponsiveContainer_LineChart_Line_XAxis_YAxis_CartesianGrid_Tooltip_Legend_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ResponsiveContainer,LineChart,Line,XAxis,YAxis,CartesianGrid,Tooltip,Legend!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/Legend.js\");\n/* harmony import */ var _barrel_optimize_names_ResponsiveContainer_LineChart_Line_XAxis_YAxis_CartesianGrid_Tooltip_Legend_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ResponsiveContainer,LineChart,Line,XAxis,YAxis,CartesianGrid,Tooltip,Legend!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/Line.js\");\n/* __next_internal_client_entry_do_not_use__ SalesChart auto */ \n\n\nfunction SalesChart({ data }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n        className: \"col-span-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                        children: \"Sales & Purchase Overview\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\dashboard\\\\sales-chart.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardDescription, {\n                        children: \"Daily sales and purchase trends for the last 7 days\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\dashboard\\\\sales-chart.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\dashboard\\\\sales-chart.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                className: \"pl-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ResponsiveContainer_LineChart_Line_XAxis_YAxis_CartesianGrid_Tooltip_Legend_recharts__WEBPACK_IMPORTED_MODULE_2__.ResponsiveContainer, {\n                    width: \"100%\",\n                    height: 350,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ResponsiveContainer_LineChart_Line_XAxis_YAxis_CartesianGrid_Tooltip_Legend_recharts__WEBPACK_IMPORTED_MODULE_3__.LineChart, {\n                        data: data,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ResponsiveContainer_LineChart_Line_XAxis_YAxis_CartesianGrid_Tooltip_Legend_recharts__WEBPACK_IMPORTED_MODULE_4__.CartesianGrid, {\n                                strokeDasharray: \"3 3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\dashboard\\\\sales-chart.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ResponsiveContainer_LineChart_Line_XAxis_YAxis_CartesianGrid_Tooltip_Legend_recharts__WEBPACK_IMPORTED_MODULE_5__.XAxis, {\n                                dataKey: \"date\",\n                                tickFormatter: (value)=>new Date(value).toLocaleDateString(\"en-US\", {\n                                        month: \"short\",\n                                        day: \"numeric\"\n                                    })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\dashboard\\\\sales-chart.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ResponsiveContainer_LineChart_Line_XAxis_YAxis_CartesianGrid_Tooltip_Legend_recharts__WEBPACK_IMPORTED_MODULE_6__.YAxis, {\n                                tickFormatter: (value)=>`₹${value.toLocaleString()}`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\dashboard\\\\sales-chart.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ResponsiveContainer_LineChart_Line_XAxis_YAxis_CartesianGrid_Tooltip_Legend_recharts__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {\n                                labelFormatter: (value)=>new Date(value).toLocaleDateString(),\n                                formatter: (value, name)=>[\n                                        `₹${value.toLocaleString()}`,\n                                        name === \"sales\" ? \"Sales\" : \"Purchases\"\n                                    ]\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\dashboard\\\\sales-chart.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ResponsiveContainer_LineChart_Line_XAxis_YAxis_CartesianGrid_Tooltip_Legend_recharts__WEBPACK_IMPORTED_MODULE_8__.Legend, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\dashboard\\\\sales-chart.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ResponsiveContainer_LineChart_Line_XAxis_YAxis_CartesianGrid_Tooltip_Legend_recharts__WEBPACK_IMPORTED_MODULE_9__.Line, {\n                                type: \"monotone\",\n                                dataKey: \"sales\",\n                                stroke: \"#2563eb\",\n                                strokeWidth: 3,\n                                dot: {\n                                    fill: \"#2563eb\",\n                                    strokeWidth: 2,\n                                    r: 4\n                                },\n                                activeDot: {\n                                    r: 6\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\dashboard\\\\sales-chart.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ResponsiveContainer_LineChart_Line_XAxis_YAxis_CartesianGrid_Tooltip_Legend_recharts__WEBPACK_IMPORTED_MODULE_9__.Line, {\n                                type: \"monotone\",\n                                dataKey: \"purchases\",\n                                stroke: \"#dc2626\",\n                                strokeWidth: 3,\n                                dot: {\n                                    fill: \"#dc2626\",\n                                    strokeWidth: 2,\n                                    r: 4\n                                },\n                                activeDot: {\n                                    r: 6\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\dashboard\\\\sales-chart.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\dashboard\\\\sales-chart.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\dashboard\\\\sales-chart.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\dashboard\\\\sales-chart.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\dashboard\\\\sales-chart.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/sales-chart.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/stats-cards.tsx":
/*!**********************************************!*\
  !*** ./components/dashboard/stats-cards.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatsCards: () => (/* binding */ StatsCards)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/shared/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_TrendingUp_Users_Package_AlertTriangle_DollarSign_ShoppingCart_Calendar_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=TrendingUp,Users,Package,AlertTriangle,DollarSign,ShoppingCart,Calendar,Clock!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_TrendingUp_Users_Package_AlertTriangle_DollarSign_ShoppingCart_Calendar_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=TrendingUp,Users,Package,AlertTriangle,DollarSign,ShoppingCart,Calendar,Clock!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_TrendingUp_Users_Package_AlertTriangle_DollarSign_ShoppingCart_Calendar_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=TrendingUp,Users,Package,AlertTriangle,DollarSign,ShoppingCart,Calendar,Clock!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_TrendingUp_Users_Package_AlertTriangle_DollarSign_ShoppingCart_Calendar_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=TrendingUp,Users,Package,AlertTriangle,DollarSign,ShoppingCart,Calendar,Clock!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_TrendingUp_Users_Package_AlertTriangle_DollarSign_ShoppingCart_Calendar_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=TrendingUp,Users,Package,AlertTriangle,DollarSign,ShoppingCart,Calendar,Clock!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_TrendingUp_Users_Package_AlertTriangle_DollarSign_ShoppingCart_Calendar_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=TrendingUp,Users,Package,AlertTriangle,DollarSign,ShoppingCart,Calendar,Clock!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_TrendingUp_Users_Package_AlertTriangle_DollarSign_ShoppingCart_Calendar_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=TrendingUp,Users,Package,AlertTriangle,DollarSign,ShoppingCart,Calendar,Clock!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_TrendingUp_Users_Package_AlertTriangle_DollarSign_ShoppingCart_Calendar_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=TrendingUp,Users,Package,AlertTriangle,DollarSign,ShoppingCart,Calendar,Clock!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* __next_internal_client_entry_do_not_use__ StatsCards auto */ \n\n\nfunction StatsCards({ stats }) {\n    const cards = [\n        {\n            title: \"Today's Sales\",\n            value: `₹${stats.todaySales.toLocaleString()}`,\n            description: \"Sales amount today\",\n            icon: _barrel_optimize_names_TrendingUp_Users_Package_AlertTriangle_DollarSign_ShoppingCart_Calendar_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            trend: \"+12%\",\n            color: \"text-green-600\"\n        },\n        {\n            title: \"This Month\",\n            value: `₹${stats.thisMonthSales.toLocaleString()}`,\n            description: \"Monthly sales\",\n            icon: _barrel_optimize_names_TrendingUp_Users_Package_AlertTriangle_DollarSign_ShoppingCart_Calendar_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            trend: \"+8%\",\n            color: \"text-blue-600\"\n        },\n        {\n            title: \"Total Sales\",\n            value: stats.totalSales.toLocaleString(),\n            description: \"Total completed sales\",\n            icon: _barrel_optimize_names_TrendingUp_Users_Package_AlertTriangle_DollarSign_ShoppingCart_Calendar_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            trend: \"+23%\",\n            color: \"text-purple-600\"\n        },\n        {\n            title: \"Total Customers\",\n            value: stats.totalCustomers.toLocaleString(),\n            description: \"Registered customers\",\n            icon: _barrel_optimize_names_TrendingUp_Users_Package_AlertTriangle_DollarSign_ShoppingCart_Calendar_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            trend: \"+5%\",\n            color: \"text-indigo-600\"\n        },\n        {\n            title: \"Total Products\",\n            value: stats.totalProducts.toLocaleString(),\n            description: \"Active products\",\n            icon: _barrel_optimize_names_TrendingUp_Users_Package_AlertTriangle_DollarSign_ShoppingCart_Calendar_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            trend: \"+2%\",\n            color: \"text-teal-600\"\n        },\n        {\n            title: \"Low Stock Alert\",\n            value: stats.lowStockItems.toLocaleString(),\n            description: \"Items below reorder level\",\n            icon: _barrel_optimize_names_TrendingUp_Users_Package_AlertTriangle_DollarSign_ShoppingCart_Calendar_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            trend: \"-3\",\n            color: \"text-orange-600\"\n        },\n        {\n            title: \"Pending Orders\",\n            value: stats.pendingOrders.toLocaleString(),\n            description: \"Orders awaiting approval\",\n            icon: _barrel_optimize_names_TrendingUp_Users_Package_AlertTriangle_DollarSign_ShoppingCart_Calendar_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            trend: \"+1\",\n            color: \"text-red-600\"\n        },\n        {\n            title: \"Total Purchases\",\n            value: stats.totalPurchases.toLocaleString(),\n            description: \"Purchase orders placed\",\n            icon: _barrel_optimize_names_TrendingUp_Users_Package_AlertTriangle_DollarSign_ShoppingCart_Calendar_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            trend: \"+4%\",\n            color: \"text-cyan-600\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid gap-4 md:grid-cols-2 lg:grid-cols-4\",\n        children: cards.map((card)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                className: \"hover:shadow-md transition-shadow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                className: \"text-sm font-medium text-muted-foreground\",\n                                children: card.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\dashboard\\\\stats-cards.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(card.icon, {\n                                className: `h-4 w-4 ${card.color}`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\dashboard\\\\stats-cards.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\dashboard\\\\stats-cards.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold\",\n                                children: card.value\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\dashboard\\\\stats-cards.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1 text-xs text-muted-foreground\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: card.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\dashboard\\\\stats-cards.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `font-medium ${card.trend.startsWith(\"+\") ? \"text-green-600\" : card.trend.startsWith(\"-\") ? \"text-red-600\" : \"text-gray-600\"}`,\n                                        children: card.trend\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\dashboard\\\\stats-cards.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\dashboard\\\\stats-cards.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\dashboard\\\\stats-cards.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, card.title, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\dashboard\\\\stats-cards.tsx\",\n                lineNumber: 91,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\dashboard\\\\stats-cards.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/stats-cards.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/sidebar.tsx":
/*!***************************************!*\
  !*** ./components/layout/sidebar.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/shared/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(ssr)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/tags.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/warehouse.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/receipt.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-pie.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/handshake.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/headphones.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard,Package,ShoppingCart,Users,Truck,Receipt,BarChart3,Settings,Store,Tags,Warehouse,FileText,Bell,LogOut,Menu,X,ChevronDown,Building2,UserCheck,TrendingUp,DollarSign,Shield,AlertTriangle,PieChart,CreditCard,Handshake,MessageSquare,Lock,Globe,Headphones,BookOpen,Activity!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _lib_permissions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/permissions */ \"(ssr)/./lib/permissions.ts\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\n\n\n\n\nconst navigationItems = [\n    {\n        title: \"Dashboard\",\n        href: \"/dashboard\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        module: \"DASHBOARD\"\n    },\n    {\n        title: \"Store Management\",\n        href: \"/stores\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        module: \"STORE\"\n    },\n    {\n        title: \"User Management\",\n        href: \"/users\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        module: \"USER\"\n    },\n    {\n        title: \"Permissions\",\n        href: \"/permissions\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        module: \"ROLE\"\n    },\n    {\n        title: \"Products\",\n        href: \"/products\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        module: \"PRODUCT\",\n        children: [\n            {\n                title: \"All Products\",\n                href: \"/products\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                module: \"PRODUCT\"\n            },\n            {\n                title: \"Categories\",\n                href: \"/categories\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                module: \"CATEGORY\"\n            },\n            {\n                title: \"Inventory\",\n                href: \"/inventory\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                module: \"INVENTORY\"\n            }\n        ]\n    },\n    {\n        title: \"Sales\",\n        href: \"/sales\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        module: \"SALES\",\n        children: [\n            {\n                title: \"Point of Sale\",\n                href: \"/pos\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                module: \"SALES\"\n            },\n            {\n                title: \"Sales History\",\n                href: \"/sales\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                module: \"SALES\"\n            },\n            {\n                title: \"Sales Analytics\",\n                href: \"/sales-analytics\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                module: \"SALES\"\n            }\n        ]\n    },\n    {\n        title: \"Purchases\",\n        href: \"/purchases\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n        module: \"PURCHASE\",\n        children: [\n            {\n                title: \"Purchase Orders\",\n                href: \"/purchases\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                module: \"PURCHASE\"\n            },\n            {\n                title: \"Purchase Returns\",\n                href: \"/purchase-returns\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                module: \"PURCHASE_RETURN\"\n            }\n        ]\n    },\n    {\n        title: \"Customers\",\n        href: \"/customers\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        module: \"CUSTOMER\"\n    },\n    {\n        title: \"Suppliers\",\n        href: \"/suppliers\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n        module: \"SUPPLIER\"\n    },\n    {\n        title: \"Distributors\",\n        href: \"/distributors\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n        module: \"DISTRIBUTOR\"\n    },\n    {\n        title: \"Expenses\",\n        href: \"/expenses\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n        module: \"EXPENSE\"\n    },\n    {\n        title: \"B2B Management\",\n        href: \"/b2b\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n        module: \"B2B\",\n        children: [\n            {\n                title: \"B2B Orders\",\n                href: \"/b2b\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                module: \"B2B\"\n            },\n            {\n                title: \"B2B Pricing\",\n                href: \"/b2b-pricing\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                module: \"B2B\"\n            }\n        ]\n    },\n    {\n        title: \"Billing & Finance\",\n        href: \"/billing\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n        module: \"BILLING\",\n        children: [\n            {\n                title: \"Invoices\",\n                href: \"/billing\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                module: \"BILLING\"\n            },\n            {\n                title: \"GST Management\",\n                href: \"/gst\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                module: \"BILLING\"\n            },\n            {\n                title: \"Tax Reports\",\n                href: \"/tax-reports\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n                module: \"BILLING\"\n            },\n            {\n                title: \"Financial Reports\",\n                href: \"/financial\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                module: \"FINANCIAL\"\n            }\n        ]\n    },\n    {\n        title: \"Analytics\",\n        href: \"/analytics\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n        module: \"ANALYTICS\"\n    },\n    {\n        title: \"Reports\",\n        href: \"/reports\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n        module: \"REPORTS\"\n    },\n    {\n        title: \"Alerts & Monitoring\",\n        href: \"/alerts\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n        module: \"ALERTS\",\n        children: [\n            {\n                title: \"System Alerts\",\n                href: \"/alerts\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n                module: \"ALERTS\"\n            },\n            {\n                title: \"Inventory Alerts\",\n                href: \"/inventory-alerts\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                module: \"INVENTORY\"\n            },\n            {\n                title: \"Security Monitoring\",\n                href: \"/security\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n                module: \"SECURITY\"\n            }\n        ]\n    },\n    {\n        title: \"Notifications\",\n        href: \"/notifications\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n        module: \"NOTIFICATION\"\n    },\n    {\n        title: \"Documents\",\n        href: \"/documents\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n        module: \"DOCUMENT\"\n    },\n    {\n        title: \"Subscriptions\",\n        href: \"/subscriptions\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"],\n        module: \"SUBSCRIPTION\"\n    },\n    {\n        title: \"Support\",\n        href: \"/support\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"],\n        module: \"SUPPORT\",\n        children: [\n            {\n                title: \"Support Tickets\",\n                href: \"/support\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"],\n                module: \"SUPPORT\"\n            },\n            {\n                title: \"Knowledge Base\",\n                href: \"/knowledge-base\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"],\n                module: \"SUPPORT\"\n            },\n            {\n                title: \"Feedback\",\n                href: \"/feedback\",\n                icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"],\n                module: \"FEEDBACK\"\n            }\n        ]\n    },\n    {\n        title: \"Audit & Activity\",\n        href: \"/audit\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"],\n        module: \"AUDIT\"\n    },\n    {\n        title: \"Settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"],\n        module: \"SETTINGS\"\n    }\n];\nfunction Sidebar({ userRole, onLogout }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [expandedItems, setExpandedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const userModules = (0,_lib_permissions__WEBPACK_IMPORTED_MODULE_7__.getUserModules)(userRole);\n    const filteredNavItems = navigationItems.filter((item)=>userModules.includes(item.module));\n    const toggleExpanded = (title)=>{\n        setExpandedItems((prev)=>prev.includes(title) ? prev.filter((item)=>item !== title) : [\n                ...prev,\n                title\n            ]);\n    };\n    const NavLink = ({ item, isChild = false })=>{\n        const isActive = pathname === item.href;\n        const hasChildren = item.children && item.children.length > 0;\n        const isExpanded = expandedItems.includes(item.title);\n        if (hasChildren) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"w-full justify-between h-12 px-4\", isChild && \"pl-8\", \"text-left font-normal hover:bg-accent hover:text-accent-foreground\"),\n                        onClick: ()=>toggleExpanded(item.title),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                        className: \"mr-3 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this),\n                                    item.title\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"h-4 w-4 transition-transform\", isExpanded && \"rotate-180\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, this),\n                    isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1 ml-4\",\n                        children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                item: child,\n                                isChild: true\n                            }, child.href, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                lineNumber: 258,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n            variant: isActive ? \"secondary\" : \"ghost\",\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"w-full justify-start h-12 px-4\", isChild && \"pl-8\", isActive && \"bg-secondary text-secondary-foreground\", \"text-left font-normal hover:bg-accent hover:text-accent-foreground\"),\n            asChild: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                href: item.href,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                        className: \"mr-3 h-5 w-5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, this),\n                    item.title\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                lineNumber: 299,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n            lineNumber: 289,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                variant: \"ghost\",\n                size: \"icon\",\n                className: \"fixed top-4 left-4 z-50 md:hidden\",\n                onClick: ()=>setIsOpen(!isOpen),\n                children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 19\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 47\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                lineNumber: 310,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/20 z-40 md:hidden\",\n                onClick: ()=>setIsOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                lineNumber: 321,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"fixed left-0 top-0 z-40 h-full w-72 bg-background border-r transform transition-transform duration-200 ease-in-out md:relative md:transform-none\", isOpen ? \"translate-x-0\" : \"-translate-x-full md:translate-x-0\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-full flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex h-16 items-center border-b px-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-6 w-6 text-primary\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: \"GroceryPOS\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                            className: \"flex-1 px-4 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"space-y-2\",\n                                children: filteredNavItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                        item: item\n                                    }, item.href, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 w-8 rounded-full bg-primary flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                className: \"h-4 w-4 text-primary-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium truncate\",\n                                                    children: \"Current Role\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: userRole.replace(\"_\", \" \")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"w-full justify-start text-red-600 hover:text-red-600 hover:bg-red-50\",\n                                    onClick: onLogout,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LayoutDashboard_Package_ShoppingCart_Users_Truck_Receipt_BarChart3_Settings_Store_Tags_Warehouse_FileText_Bell_LogOut_Menu_X_ChevronDown_Building2_UserCheck_TrendingUp_DollarSign_Shield_AlertTriangle_PieChart_CreditCard_Handshake_MessageSquare_Lock_Globe_Headphones_BookOpen_Activity_lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Sign Out\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                    lineNumber: 332,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\layout\\\\sidebar.tsx\",\n                lineNumber: 328,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/shared/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/shared/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/shared/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/scroll-area.tsx":
/*!***************************************!*\
  !*** ./components/ui/scroll-area.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScrollArea: () => (/* binding */ ScrollArea),\n/* harmony export */   ScrollBar: () => (/* binding */ ScrollBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/shared/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-scroll-area */ \"(ssr)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ScrollArea,ScrollBar auto */ \n\n\n\nconst ScrollArea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative overflow-hidden\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Viewport, {\n                className: \"h-full w-full rounded-[inherit]\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 17,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScrollBar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 20,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Corner, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 21,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\scroll-area.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined));\nScrollArea.displayName = _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\nconst ScrollBar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, orientation = \"vertical\", ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaScrollbar, {\n        ref: ref,\n        orientation: orientation,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex touch-none select-none transition-colors\", orientation === \"vertical\" && \"h-full w-2.5 border-l border-l-transparent p-[1px]\", orientation === \"horizontal\" && \"h-2.5 flex-col border-t border-t-transparent p-[1px]\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaThumb, {\n            className: \"relative flex-1 rounded-full bg-border\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\scroll-area.tsx\",\n            lineNumber: 43,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\scroll-area.tsx\",\n        lineNumber: 30,\n        columnNumber: 3\n    }, undefined));\nScrollBar.displayName = _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaScrollbar.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/scroll-area.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/separator.tsx":
/*!*************************************!*\
  !*** ./components/ui/separator.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Separator: () => (/* binding */ Separator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/shared/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-separator */ \"(ssr)/./node_modules/@radix-ui/react-separator/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Separator auto */ \n\n\n\nconst Separator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, orientation = \"horizontal\", decorative = true, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        decorative: decorative,\n        orientation: orientation,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"shrink-0 bg-border\", orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\separator.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, undefined));\nSeparator.displayName = _radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3NlcGFyYXRvci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFK0I7QUFDaUM7QUFFL0I7QUFFakMsTUFBTUcsMEJBQVlILDZDQUFnQixDQUloQyxDQUNFLEVBQUVLLFNBQVMsRUFBRUMsY0FBYyxZQUFZLEVBQUVDLGFBQWEsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFDdEVDLG9CQUVBLDhEQUFDUiwyREFBdUI7UUFDdEJRLEtBQUtBO1FBQ0xGLFlBQVlBO1FBQ1pELGFBQWFBO1FBQ2JELFdBQVdILDhDQUFFQSxDQUNYLHNCQUNBSSxnQkFBZ0IsZUFBZSxtQkFBbUIsa0JBQ2xERDtRQUVELEdBQUdHLEtBQUs7Ozs7OztBQUlmTCxVQUFVUSxXQUFXLEdBQUdWLDJEQUF1QixDQUFDVSxXQUFXO0FBRXRDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ3JvY2VyeS1tYW5hZ2VtZW50LXN5c3RlbS8uL2NvbXBvbmVudHMvdWkvc2VwYXJhdG9yLnRzeD9iNDFmIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0ICogYXMgU2VwYXJhdG9yUHJpbWl0aXZlIGZyb20gJ0ByYWRpeC11aS9yZWFjdC1zZXBhcmF0b3InO1xuXG5pbXBvcnQgeyBjbiB9IGZyb20gJ0AvbGliL3V0aWxzJztcblxuY29uc3QgU2VwYXJhdG9yID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgU2VwYXJhdG9yUHJpbWl0aXZlLlJvb3Q+LFxuICBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIFNlcGFyYXRvclByaW1pdGl2ZS5Sb290PlxuPihcbiAgKFxuICAgIHsgY2xhc3NOYW1lLCBvcmllbnRhdGlvbiA9ICdob3Jpem9udGFsJywgZGVjb3JhdGl2ZSA9IHRydWUsIC4uLnByb3BzIH0sXG4gICAgcmVmXG4gICkgPT4gKFxuICAgIDxTZXBhcmF0b3JQcmltaXRpdmUuUm9vdFxuICAgICAgcmVmPXtyZWZ9XG4gICAgICBkZWNvcmF0aXZlPXtkZWNvcmF0aXZlfVxuICAgICAgb3JpZW50YXRpb249e29yaWVudGF0aW9ufVxuICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgJ3Nocmluay0wIGJnLWJvcmRlcicsXG4gICAgICAgIG9yaWVudGF0aW9uID09PSAnaG9yaXpvbnRhbCcgPyAnaC1bMXB4XSB3LWZ1bGwnIDogJ2gtZnVsbCB3LVsxcHhdJyxcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gIClcbik7XG5TZXBhcmF0b3IuZGlzcGxheU5hbWUgPSBTZXBhcmF0b3JQcmltaXRpdmUuUm9vdC5kaXNwbGF5TmFtZTtcblxuZXhwb3J0IHsgU2VwYXJhdG9yIH07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJTZXBhcmF0b3JQcmltaXRpdmUiLCJjbiIsIlNlcGFyYXRvciIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJvcmllbnRhdGlvbiIsImRlY29yYXRpdmUiLCJwcm9wcyIsInJlZiIsIlJvb3QiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/separator.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/sonner.tsx":
/*!**********************************!*\
  !*** ./components/ui/sonner.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/shared/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nconst Toaster = ({ ...props })=>{\n    const { theme = \"system\" } = (0,next_themes__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n        theme: theme,\n        className: \"toaster group\",\n        toastOptions: {\n            classNames: {\n                toast: \"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg\",\n                description: \"group-[.toast]:text-muted-foreground\",\n                actionButton: \"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground\",\n                cancelButton: \"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground\"\n            }\n        },\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\components\\\\ui\\\\sonner.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/sonner.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/permissions.ts":
/*!****************************!*\
  !*** ./lib/permissions.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ROLE_PERMISSIONS: () => (/* binding */ ROLE_PERMISSIONS),\n/* harmony export */   getModulePermissions: () => (/* binding */ getModulePermissions),\n/* harmony export */   getUserModules: () => (/* binding */ getUserModules),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission)\n/* harmony export */ });\nconst ROLE_PERMISSIONS = [\n    // FOUNDER - Full access\n    {\n        role: \"FOUNDER\",\n        module: \"USER\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"ASSIGN\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"ROLE\",\n        permissions: [\n            \"MANAGE\",\n            \"ACCESS_SETTINGS\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"STORE\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"SETTINGS\",\n        permissions: [\n            \"ACCESS_SETTINGS\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"REPORTS\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\",\n            \"PRINT\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"SUBSCRIPTION\",\n        permissions: [\n            \"READ\",\n            \"UPDATE\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"AUDIT_LOG\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\"\n        ]\n    },\n    {\n        role: \"FOUNDER\",\n        module: \"DASHBOARD\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    // SUPER_ADMIN\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"USER\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"ASSIGN\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"STORE\",\n        permissions: [\n            \"READ\",\n            \"UPDATE\",\n            \"MANAGE\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"SETTINGS\",\n        permissions: [\n            \"ACCESS_SETTINGS\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"REPORTS\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\",\n            \"PRINT\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"AUDIT_LOG\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"SUPPORT\",\n        permissions: [\n            \"READ\",\n            \"CREATE\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"SUPER_ADMIN\",\n        module: \"DASHBOARD\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    // ADMIN\n    {\n        role: \"ADMIN\",\n        module: \"PRODUCT\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\",\n            \"IMPORT\",\n            \"EXPORT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"CATEGORY\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"DELETE\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"INVENTORY\",\n        permissions: [\n            \"READ\",\n            \"UPDATE\",\n            \"EXPORT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"PURCHASE\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"APPROVE\",\n            \"REJECT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"PURCHASE_RETURN\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"SALES\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"EXPORT\",\n            \"PRINT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"SALES_RETURN\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"CUSTOMER\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"SUPPLIER\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"DISTRIBUTOR\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"EXPENSE\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"PAYMENT\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"B2B\",\n        permissions: [\n            \"CREATE\",\n            \"READ\",\n            \"UPDATE\",\n            \"APPROVE\",\n            \"REJECT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"BILLING\",\n        permissions: [\n            \"READ\",\n            \"PRINT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"TAX\",\n        permissions: [\n            \"READ\",\n            \"UPDATE\",\n            \"ACCESS_SETTINGS\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"NOTIFICATION\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"DOCUMENT\",\n        permissions: [\n            \"UPLOAD\",\n            \"READ\",\n            \"DOWNLOAD\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"REPORTS\",\n        permissions: [\n            \"READ\",\n            \"EXPORT\",\n            \"PRINT\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"DASHBOARD\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"FEEDBACK\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    {\n        role: \"ADMIN\",\n        module: \"SUPPORT\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    // STAFF\n    {\n        role: \"STAFF\",\n        module: \"SALES\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"SALES_RETURN\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"INVENTORY\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"BILLING\",\n        permissions: [\n            \"READ\",\n            \"PRINT\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"DOCUMENT\",\n        permissions: [\n            \"UPLOAD\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"CUSTOMER\",\n        permissions: [\n            \"CREATE\",\n            \"READ\"\n        ]\n    },\n    {\n        role: \"STAFF\",\n        module: \"DASHBOARD\",\n        permissions: [\n            \"READ\"\n        ]\n    },\n    // DISTRIBUTOR\n    {\n        role: \"DISTRIBUTOR\",\n        module: \"PURCHASE\",\n        permissions: [\n            \"READ\",\n            \"APPROVE\",\n            \"REJECT\"\n        ]\n    },\n    {\n        role: \"DISTRIBUTOR\",\n        module: \"PURCHASE_RETURN\",\n        permissions: [\n            \"READ\",\n            \"APPROVE\"\n        ]\n    },\n    {\n        role: \"DISTRIBUTOR\",\n        module: \"DASHBOARD\",\n        permissions: [\n            \"READ\"\n        ]\n    }\n];\nfunction hasPermission(userRole, module, permission) {\n    const rolePermissions = ROLE_PERMISSIONS.filter((rp)=>rp.role === userRole && rp.module === module);\n    return rolePermissions.some((rp)=>rp.permissions.includes(permission));\n}\nfunction getUserModules(userRole) {\n    return ROLE_PERMISSIONS.filter((rp)=>rp.role === userRole).map((rp)=>rp.module);\n}\nfunction getModulePermissions(userRole, module) {\n    const rolePermission = ROLE_PERMISSIONS.find((rp)=>rp.role === userRole && rp.module === module);\n    return rolePermission?.permissions || [];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/permissions.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTZDO0FBQ0o7QUFFbEMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ3JvY2VyeS1tYW5hZ2VtZW50LXN5c3RlbS8uL2xpYi91dGlscy50cz9mNzQ1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gJ2Nsc3gnO1xuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gJ3RhaWx3aW5kLW1lcmdlJztcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSk7XG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d11a3318c6f5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ncm9jZXJ5LW1hbmFnZW1lbnQtc3lzdGVtLy4vYXBwL2dsb2JhbHMuY3NzPzk2MzIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkMTFhMzMxOGM2ZjVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/(dashboard)/dashboard/page.tsx":
/*!********************************************!*\
  !*** ./app/(dashboard)/dashboard/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\Grocery Management System\project\app\(dashboard)\dashboard\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/(dashboard)/layout.tsx":
/*!************************************!*\
  !*** ./app/(dashboard)/layout.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\Grocery Management System\project\app\(dashboard)\layout.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/shared/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_ui_sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/sonner */ \"(rsc)/./components/ui/sonner.tsx\");\n/* harmony import */ var _components_auth_auth_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/auth/auth-provider */ \"(rsc)/./components/auth/auth-provider.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"GroceryPOS - Complete Store Management System\",\n    description: \"Modern SaaS solution for grocery store management with POS, inventory, and analytics\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_auth_provider__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                        position: \"top-right\",\n                        richColors: true,\n                        expand: false,\n                        duration: 4000\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\layout.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Grocery Management System\\\\project\\\\app\\\\layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/auth/auth-provider.tsx":
/*!*******************************************!*\
  !*** ./components/auth/auth-provider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\Grocery Management System\project\components\auth\auth-provider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = proxy["AuthProvider"];

const e1 = proxy["useAuth"];


/***/ }),

/***/ "(rsc)/./components/ui/sonner.tsx":
/*!**********************************!*\
  !*** ./components/ui/sonner.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\Grocery Management System\project\components\ui\sonner.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = proxy["Toaster"];


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/sonner","vendor-chunks/next-themes","vendor-chunks/@swc","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/clsx","vendor-chunks/@radix-ui","vendor-chunks/class-variance-authority","vendor-chunks/lodash","vendor-chunks/recharts","vendor-chunks/d3-shape","vendor-chunks/d3-scale","vendor-chunks/d3-array","vendor-chunks/d3-format","vendor-chunks/d3-interpolate","vendor-chunks/d3-time","vendor-chunks/react-transition-group","vendor-chunks/react-smooth","vendor-chunks/prop-types","vendor-chunks/recharts-scale","vendor-chunks/dom-helpers","vendor-chunks/d3-time-format","vendor-chunks/d3-color","vendor-chunks/victory-vendor","vendor-chunks/react-is","vendor-chunks/tiny-invariant","vendor-chunks/internmap","vendor-chunks/d3-path","vendor-chunks/fast-equals","vendor-chunks/@babel","vendor-chunks/object-assign","vendor-chunks/eventemitter3","vendor-chunks/decimal.js-light"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(dashboard)%2Fdashboard%2Fpage&page=%2F(dashboard)%2Fdashboard%2Fpage&appPaths=%2F(dashboard)%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5CGrocery%20Management%20System%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();