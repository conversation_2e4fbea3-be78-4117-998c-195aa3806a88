import { NextRequest, NextResponse } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  getPaginationParams,
  buildSearchFilter,
  createPaginatedResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const categorySchema = z.object({
  name: z.string().min(1, 'Category name is required'),
  description: z.string().optional()
})

export const GET = withPermission('CATEGORY', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { page, limit, skip, search, sortBy, sortOrder } = getPaginationParams(request)

  // Build filters
  const where: any = {
    storeId,
    isActive: true,
    ...buildSearchFilter(search, ['name', 'description'])
  }

  // Get total count
  const total = await prisma.category.count({ where })

  // Get categories with pagination
  const categories = await prisma.category.findMany({
    where,
    include: {
      _count: {
        select: { products: true }
      }
    },
    orderBy: { [sortBy]: sortOrder },
    skip,
    take: limit
  })

  return createPaginatedResponse(categories, page, limit, total)
})

export const POST = withPermission('CATEGORY', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const data = categorySchema.parse(body)

  // Check if category name already exists
  const existingCategory = await prisma.category.findFirst({
    where: {
      name: data.name,
      storeId,
      isActive: true
    }
  })

  if (existingCategory) {
    return createErrorResponse('Category name already exists', 400)
  }

  const category = await prisma.category.create({
    data: {
      ...data,
      storeId
    },
    include: {
      _count: {
        select: { products: true }
      }
    }
  })

  // Create audit log
  await createAuditLog(
    'CREATE',
    'CATEGORY',
    `Created category: ${category.name}`,
    user.id,
    storeId
  )

  return createSuccessResponse(category, 'Category created successfully')
})